// supabase/functions/stripe-webhook/index.ts
// Handles incoming Stripe webhook events, verifies signature, logs event, processes relevant events.

import { serve } from 'https://deno.land/std@0.177.0/http/server.ts'; // Or latest stable std version
import { createClient, SupabaseClient } from 'https://esm.sh/@supabase/supabase-js@2'; // Use v2+ syntax
import Stripe from 'https://esm.sh/stripe@11.16.0?target=deno'; // Pin version or use latest compatible

// --- Environment Variables ---
// These MUST be set in your Supabase Edge Function settings (either via UI or supabase secrets set)
const supabaseUrl = Deno.env.get('SUPABASE_URL');
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY'); // Service Role needed to update tables bypassed by RLS
const stripeSecretKey = Deno.env.get('STRIPE_SECRET_KEY');
const stripeWebhookSecret = Deno.env.get('STRIPE_WEBHOOK_SECRET'); // Get this from your Stripe dashboard webhook endpoint config

if (!supabaseUrl || !supabaseServiceKey || !stripeSecretKey || !stripeWebhookSecret) {
  console.error('FATAL: Missing required environment variables for Stripe Webhook function.');
  // In a real scenario, you might want more robust error handling or alerting here
  Deno.exit(1); // Exit if essential config is missing
}

// --- Initialize Clients ---
const supabaseAdmin: SupabaseClient = createClient(supabaseUrl, supabaseServiceKey, {
    auth: {
      // No need to persist session for service role key
      persistSession: false,
      autoRefreshToken: false
    }
});

const stripe = new Stripe(stripeSecretKey, {
  apiVersion: '2022-11-15', // Match your Stripe API version
  httpClient: Stripe.createFetchHttpClient(), // Deno compatibility
});

// Define expected Supabase table types (optional but helpful for type safety)
type WebhookEventLog = {
  id?: string;
  provider: string;
  event_type: string;
  event_id?: string | null; // Store Stripe event ID
  payload: any;
  status: 'received' | 'processing' | 'processed' | 'error' | 'ignored';
  processing_notes?: string | null;
  processed_at?: string | null;
};

type Registration = {
  id: string;
  payment_status: string;
  payment_intent_id?: string | null;
  // other fields...
};

type Transaction = {
   // Define fields as per your transactions table
   provider: string;
   provider_transaction_id?: string | null;
   event_id?: string | null; // Store Stripe event ID here too?
   registration_id?: string | null;
   tournament_id?: string | null;
   profile_id?: string | null;
   transaction_type: string;
   amount: number;
   currency: string;
   status: string;
   // other fields...
};

// --- Main Handler ---
serve(async (req: Request) => {
  const signature = req.headers.get('Stripe-Signature');
  const rawBody = await req.text(); // Read raw body for signature verification

  let event: Stripe.Event;
  let webhookLogEntry: Partial<WebhookEventLog> = { // Data for webhook_events table
      provider: 'stripe',
      event_type: 'unknown',
      payload: null,
      status: 'received',
      processing_notes: null
  };
  let webhookEventId: string | null = null; // DB ID of the logged event

  // 1. --- Verify Signature ---
  try {
    if (!signature) throw new Error('Stripe-Signature header is missing.');

    event = await stripe.webhooks.constructEventAsync(
      rawBody,
      signature,
      stripeWebhookSecret,
      undefined,
      Stripe.createSubtleCryptoProvider() // Required for Deno
    );
    webhookLogEntry.event_type = event.type;
    webhookLogEntry.event_id = event.id; // Store Stripe's event ID
    webhookLogEntry.payload = event; // Store the full event object

  } catch (err) {
    console.error(`Webhook signature verification failed: ${err.message}`);
    webhookLogEntry.status = 'error';
    webhookLogEntry.processing_notes = `Signature verification failed: ${err.message}`;
    webhookLogEntry.payload = { error: err.message, body: rawBody.substring(0, 500) }; // Log part of body on error

    // Log failed attempt before returning error
    await supabaseAdmin.from('webhook_events').insert(webhookLogEntry as WebhookEventLog); // Cast needed? Check types
    return new Response(`Webhook Error: ${err.message}`, { status: 400 });
  }

  // 2. --- Log Received Event (Idempotency Check Point 1) ---
  try {
      // Optional: Check if event_id already processed successfully before inserting?
      const { data: existingSuccess, error: checkError } = await supabaseAdmin
        .from('webhook_events')
        .select('id')
        .eq('event_id', event.id)
        .eq('status', 'processed')
        .maybe_single();

      if (checkError) {
          console.error("Error checking for existing processed event:", checkError);
          // Decide whether to proceed or return error
      }
      if (existingSuccess) {
          console.log(`Event ${event.id} already processed successfully. Acknowledging.`);
          return new Response(JSON.stringify({ received: true, status: 'already_processed' }), { status: 200 });
      }

      // Insert the log record
      const { data: loggedEvent, error: logError } = await supabaseAdmin
        .from('webhook_events')
        .insert(webhookLogEntry as WebhookEventLog) // Cast to full type
        .select('id')
        .single(); // Expect a single row back

       if (logError) {
           // If logging fails, this might indicate a DB issue or duplicate event ID constraint hit.
           // If duplicate event_id is the cause, we might have processed it in a race condition.
           if (logError.code === '23505') { // Postgres unique violation code
                console.warn(`Event ${event.id} likely already logged (unique constraint). Assuming race condition or retry.`);
                // Potentially try to fetch the existing log ID, or just acknowledge Stripe if likely processed.
                return new Response(JSON.stringify({ received: true, status: 'assumed_duplicate_logged' }), { status: 200 });
           } else {
               throw logError; // Throw other DB errors
           }
       }
       webhookEventId = loggedEvent?.id; // Get DB ID for later status updates

  } catch(dbError) {
       console.error("CRITICAL: Error logging webhook event to DB:", dbError);
       // If logging fails critically, safest to return 500 to make Stripe retry.
       return new Response(`Webhook DB Logging Error: ${dbError.message}`, { status: 500 });
  }

  // 3. --- Process the Event ---
  // Mark as processing (best effort, final status updated at end)
  if (webhookEventId) {
       await supabaseAdmin.from('webhook_events').update({ status: 'processing' }).eq('id', webhookEventId);
  }

  let processingStatus: WebhookEventLog['status'] = 'processed'; // Assume success unless error occurs
  let processingNotes: string | null = null;

  try {
      logging.info(`Processing Stripe event: ${event.id} (${event.type})`);

      switch (event.type) {
        case 'checkout.session.completed': {
          const session = event.data.object as Stripe.Checkout.Session;
          // Extract metadata or client_reference_id passed during checkout creation
          const registrationId = session.client_reference_id; // Preferred method if set during checkout creation
          const tournamentId = session.metadata?.tournament_id; // Example if using metadata
          const teamId = session.metadata?.team_id;
          const profileId = session.metadata?.profile_id; // User who paid
          const feeType = session.metadata?.fee_type ?? 'registration_fee'; // e.g., 'registration_fee', 'tournament_creation_fee'

          const paymentIntentId = typeof session.payment_intent === 'string' ? session.payment_intent : session.payment_intent?.id;
          const amount = (session.amount_total ?? 0) / 100; // Convert cents to dollars/currency unit
          const currency = session.currency?.toUpperCase() ?? 'USD';

          if (!paymentIntentId) {
              throw new Error("Payment Intent ID missing from checkout session.");
          }

          console.log(`Checkout completed. PI: ${paymentIntentId}, RegID: ${registrationId}, TournID: ${tournamentId}, Amount: ${amount} ${currency}`);

          // --- Update Related Records ---
          // Use a transaction if updating multiple tables atomically is critical
          // await supabaseAdmin.rpc('handle_successful_payment', { ...params... }); // Or use an RPC

          // a) Update Registration Status (if applicable)
          if (registrationId && feeType === 'registration_fee') {
              const { error: regError } = await supabaseAdmin
                  .from('registrations')
                  .update({ payment_status: 'paid', payment_intent_id: paymentIntentId })
                  .eq('id', registrationId);
              if (regError) throw new Error(`Failed to update registration ${registrationId}: ${regError.message}`);
              console.log(`Updated registration ${registrationId} to paid.`);
          }

          // b) Update Tournament Status (if applicable - e.g., for creation fee)
          if (tournamentId && feeType === 'tournament_creation_fee') {
              const { error: tournError } = await supabaseAdmin
                  .from('tournaments')
                  .update({ creation_payment_status: 'paid', creation_payment_intent_id: paymentIntentId })
                  .eq('id', tournamentId);
              if (tournError) throw new Error(`Failed to update tournament ${tournamentId}: ${tournError.message}`);
              console.log(`Updated tournament ${tournamentId} creation fee to paid.`);
          }

          // c) Create Transaction Record (Upsert based on provider_transaction_id for idempotency)
          const { error: txError } = await supabaseAdmin.from('transactions').upsert({
              provider: 'stripe',
              provider_transaction_id: paymentIntentId, // Use PI as unique ID from provider
              event_id: event.id, // Reference the Stripe event ID
              registration_id: registrationId, // Link to registration if applicable
              tournament_id: tournamentId,   // Link to tournament if applicable
              profile_id: profileId,         // Link to user if available
              // related_club_id: Needs fetching based on team/tournament if needed
              // fee_config_id: Needs fetching based on metadata/registration details
              transaction_type: feeType,
              amount: amount,
              currency: currency,
              status: 'paid',
          }, { onConflict: 'provider_transaction_id' }); // Prevent duplicates based on Stripe ID
          if (txError) throw new Error(`Failed to upsert transaction ${paymentIntentId}: ${txError.message}`);
          console.log(`Upserted transaction record for ${paymentIntentId}.`);

          // d) Trigger any downstream actions (e.g., notifications, sending welcome email) via RPC or background job

          break; // End checkout.session.completed
        }

        case 'charge.refunded': {
          const charge = event.data.object as Stripe.Charge;
          const paymentIntentId = charge.payment_intent as string; // Get associated PI
          const amountRefunded = (charge.amount_refunded ?? 0) / 100;
          console.log(`Charge refunded for PI: ${paymentIntentId}, Amount: ${amountRefunded}`);

          // Update Transaction status (find by provider_transaction_id)
          const { error: txUpdateError } = await supabaseAdmin
              .from('transactions')
              .update({ status: 'refunded', notes: `Refunded ${amountRefunded}` })
              .eq('provider_transaction_id', paymentIntentId);
          if (txUpdateError) throw new Error(`Failed to update transaction ${paymentIntentId} to refunded: ${txUpdateError.message}`);

          // Optionally update registration/tournament payment status if fully refunded? Needs business logic.

          break; // End charge.refunded
        }

        // --- Add handlers for other critical events ---
        // case 'charge.failed':
        // case 'payment_intent.succeeded': // Alternative to checkout.session, contains more direct payment info
        // case 'payment_intent.payment_failed':
        // ... etc ...

        default:
          console.log(`Webhook event type ${event.type} received but not handled.`);
          processingStatus = 'ignored'; // Mark as ignored if no specific handler
          processingNotes = `Unhandled event type: ${event.type}`;
      }

      // If processing successful
      processingNotes = processingNotes ?? 'Successfully processed.';

  } catch (procError) {
      console.error(`Error processing webhook event ${event.id} (${event.type}): ${procError.message}`, procError);
      processingStatus = 'error';
      processingNotes = procError.message.substring(0, 1000); // Limit error message length
      // Return 500 to potentially trigger Stripe retries for processing errors
      // Update log before returning
      if (webhookEventId) {
         await supabaseAdmin.from('webhook_events').update({ status: processingStatus, processing_notes: processingNotes, processed_at: new Date().toISOString() }).eq('id', webhookEventId);
      }
      return new Response(`Webhook Processing Error: ${procError.message}`, { status: 500 });
  }

  // 4. --- Update Log with Final Status ---
  if (webhookEventId) {
      const { error: updateError } = await supabaseAdmin
          .from('webhook_events')
          .update({
              status: processingStatus,
              processing_notes: processingNotes,
              processed_at: new Date().toISOString()
          })
          .eq('id', webhookEventId);
      if(updateError) console.error("Failed to update webhook log final status:", updateError);
  }

  // 5. --- Acknowledge Receipt to Stripe ---
  console.log(`Finished processing event ${event.id}. Status: ${processingStatus}`);
  return new Response(JSON.stringify({ received: true, status: processingStatus }), { status: 200 });
});

//To deploy this:
//1.Make sure you have the Supabase CLI installed and are logged in.
//2.Link your local project to your Supabase project: supabase link --project-ref <your-project-ref>
//3.Place the code above in supabase/functions/stripe-webhook/index.ts.
//4.Add necessary Deno imports if prompted (like import_map.json).
//5.Set the required environment variables/secrets in the Supabase dashboard: SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY, STRIPE_SECRET_KEY, STRIPE_WEBHOOK_SECRET.
//6.Deploy the function: supabase functions deploy stripe-webhook --no-verify-jwt (use --no-verify-jwt for service functions that don't rely on a user's token for auth, although internal checks might still be needed).
//7.Configure the resulting function URL in your Stripe webhook settings.
