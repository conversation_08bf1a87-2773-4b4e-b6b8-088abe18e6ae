-- Function to create a club and a club admin account in one transaction
-- This function should be restricted to service_role only for security
CREATE OR REPLACE FUNCTION create_club_admin_account(
  p_club_name TEXT,
  p_admin_first_name TEXT,
  p_admin_last_name TEXT,
  p_admin_email TEXT,
  p_admin_password TEXT
) RETURNS UUID AS $$
DECLARE
  v_user_id UUID;
  v_club_id UUID;
BEGIN
  -- Input validation
  IF p_club_name IS NULL OR p_club_name = '' THEN
    RAISE EXCEPTION 'Club name cannot be empty';
  END IF;
  
  IF p_admin_email IS NULL OR p_admin_email = '' THEN
    RAISE EXCEPTION 'Admin email cannot be empty';
  END IF;
  
  IF p_admin_password IS NULL OR LENGTH(p_admin_password) < 6 THEN
    RAISE EXCEPTION 'Password must be at least 6 characters';
  END IF;
  
  -- Start transaction
  BEGIN
    -- Create the club first
    INSERT INTO clubs (name, created_at, updated_at)
    VALUES (p_club_name, NOW(), NOW())
    RETURNING id INTO v_club_id;
    
    -- Create the user account using auth.admin_create_user
    -- This is more secure than directly inserting into auth.users
    v_user_id := auth.admin_create_user(
      email := p_admin_email,
      password := p_admin_password,
      email_confirm := false -- Require email confirmation
    );
    
    -- Create the profile with club_admin role and link to the club
    INSERT INTO profiles (
      id,
      email,
      first_name,
      last_name,
      role,
      managing_club_id,
      created_at,
      updated_at
    )
    VALUES (
      v_user_id,
      p_admin_email,
      p_admin_first_name,
      p_admin_last_name,
      'club_admin',
      v_club_id,
      NOW(),
      NOW()
    );
    
    -- Create an entry in club_directors junction table
    INSERT INTO club_directors (
      club_id,
      user_id,
      status,
      created_at,
      updated_at
    )
    VALUES (
      v_club_id,
      v_user_id,
      'approved', -- Auto-approve since this is the club creator
      NOW(),
      NOW()
    );
    
    -- Return the created user ID
    RETURN v_user_id;
  EXCEPTION
    WHEN OTHERS THEN
      -- Roll back the transaction on any error
      RAISE;
  END;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Set permissions to restrict this function to service_role only
REVOKE ALL ON FUNCTION create_club_admin_account FROM PUBLIC;
REVOKE ALL ON FUNCTION create_club_admin_account FROM ANON;
REVOKE ALL ON FUNCTION create_club_admin_account FROM AUTHENTICATED;
GRANT EXECUTE ON FUNCTION create_club_admin_account TO service_role;
