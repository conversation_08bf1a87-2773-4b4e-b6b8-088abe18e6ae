-- Revised RPC Function: Create Club Admin and associated Club
-- Uses auth.admin_create_user for safety and standard behavior
CREATE OR REPLACE FUNCTION public.create_club_admin_account(
    p_club_name TEXT,
    p_admin_first_name TEXT,
    p_admin_last_name TEXT,
    p_admin_email TEXT,
    p_admin_password TEXT
) RETURNS UUID -- Returns the new user's ID
LANGUAGE plpgsql
SECURITY DEFINER -- Runs with elevated privileges (service_role)
SET search_path = public, auth, extensions
AS $$
DECLARE
    v_user_id UUID;
    v_club_id UUID;
    v_display_name TEXT;
    v_require_confirmation BOOLEAN;
BEGIN
    -- Input validation
    IF p_club_name IS NULL OR p_club_name = '' THEN
        RAISE EXCEPTION 'Club Name cannot be empty';
    END IF;

    IF p_admin_first_name IS NULL OR p_admin_first_name = '' THEN
        RAISE EXCEPTION 'Admin First Name cannot be empty';
    END IF;

    IF p_admin_last_name IS NULL OR p_admin_last_name = '' THEN
        RAISE EXCEPTION 'Admin Last Name cannot be empty';
    END IF;

    IF p_admin_email IS NULL OR p_admin_email = '' THEN
        RAISE EXCEPTION 'Admin Email cannot be empty';
    END IF;

    IF p_admin_password IS NULL OR p_admin_password = '' OR length(p_admin_password) < 6 THEN
        RAISE EXCEPTION 'Password must be at least 6 characters';
    END IF;

    -- Check if club name already exists
    IF EXISTS (SELECT 1 FROM public.clubs WHERE name = p_club_name) THEN
        RAISE EXCEPTION 'Club with this name already exists.';
    END IF;

    v_display_name := trim(p_admin_first_name || ' ' || p_admin_last_name);

    -- Determine if email confirmation is required from project settings
    -- This is a conceptual check; actual implementation might vary
    -- It's often simpler to just let admin_create_user respect the setting
    SELECT settings ->> 'external_email_enabled' = 'true' AND settings ->> 'mailer_autoconfirm' = 'false'
    INTO v_require_confirmation
    FROM auth.settings; -- Note: Accessing auth.settings might require specific permissions

    -- 1. Create the user using auth.admin_create_user
    --    This respects project settings for email confirmation automatically
    --    and handles password hashing securely.
    v_user_id := auth.admin_create_user(
        p_admin_email,
        p_admin_password,
        jsonb_build_object(
            'email', p_admin_email, -- Add email to metadata if needed by profile trigger
            'first_name', p_admin_first_name,
            'last_name', p_admin_last_name,
            'full_name', v_display_name,
            'user_role', 'club_admin' -- Pass role in metadata for profile trigger
        )
    );
    RAISE NOTICE 'Created auth user: %', v_user_id;

    -- 2. Create the Club
    INSERT INTO public.clubs (name, is_verified) -- Default is_verified to FALSE
    VALUES (p_club_name, FALSE)
    RETURNING id INTO v_club_id;
    RAISE NOTICE 'Created club: %', v_club_id;

    -- 3. Create or Update the corresponding profile (handle_new_user trigger might fire)
    --    Ensure the trigger sets managing_club_id correctly if possible,
    --    otherwise, update it here.
    INSERT INTO public.profiles (id, email, first_name, last_name, full_name, user_role, managing_club_id)
    VALUES (
        v_user_id, p_admin_email, p_admin_first_name, p_admin_last_name,
        v_display_name, 'club_admin', v_club_id
    )
    ON CONFLICT (id) DO UPDATE SET
        email = EXCLUDED.email,
        first_name = EXCLUDED.first_name,
        last_name = EXCLUDED.last_name,
        full_name = EXCLUDED.full_name,
        user_role = EXCLUDED.user_role,
        managing_club_id = EXCLUDED.managing_club_id,
        updated_at = timezone('utc', now());
    RAISE NOTICE 'Created/Updated profile for user % linked to club %', v_user_id, v_club_id;

    RETURN v_user_id;

EXCEPTION
    WHEN unique_violation THEN -- Catch duplicate email/club name errors more specifically
        IF SQLERRM LIKE '%auth.users_email_key%' THEN
            RAISE EXCEPTION 'User with this email already exists.';
        END IF;
        IF SQLERRM LIKE '%public.clubs_name_key%' THEN
            RAISE EXCEPTION 'Club with this name already exists.';
        END IF;
        RAISE; -- Re-raise other unique violations
    WHEN others THEN
        RAISE EXCEPTION 'Error creating club admin account: %', SQLERRM;
END;
$$;

-- Recommended Permissions (More secure approach)
REVOKE EXECUTE ON FUNCTION public.create_club_admin_account(TEXT, TEXT, TEXT, TEXT, TEXT) FROM PUBLIC;
GRANT EXECUTE ON FUNCTION public.create_club_admin_account(TEXT, TEXT, TEXT, TEXT, TEXT) TO authenticated, service_role;

-- Comment on function
COMMENT ON FUNCTION public.create_club_admin_account IS 'Creates a club and club admin account in one atomic transaction using auth.admin_create_user';
