CREATE OR R<PERSON>LACE FUNCTION public.get_trigger_info(trigger_name text)
RETURNS TABLE (
  trigger_name text,
  event_manipulation text,
  action_statement text,
  action_timing text
) 
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    t.trigger_name::text,
    t.event_manipulation::text,
    t.action_statement::text,
    t.action_timing::text
  FROM 
    information_schema.triggers t
  WHERE 
    t.trigger_schema = 'public'
    AND t.trigger_name = get_trigger_info.trigger_name
  ORDER BY 
    t.trigger_name;
END;
$$;
