CREATE OR REPLACE FUNCTION public.get_rls_policies(table_name text)
RETURNS TABLE (
  policyname text,
  permissive text,
  cmd text,
  qual text
) 
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    p.policyname::text,
    p.permissive::text,
    p.cmd::text,
    p.qual::text
  FROM 
    pg_policies p
  WHERE 
    p.schemaname = 'public'
    AND p.tablename = get_rls_policies.table_name
  ORDER BY 
    p.policyname;
END;
$$;
