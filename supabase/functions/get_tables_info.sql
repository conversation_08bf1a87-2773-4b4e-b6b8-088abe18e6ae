CREATE OR REPLACE FUNCTION public.get_tables_info()
RETURNS TABLE (
  table_name text,
  table_type text,
  is_insertable_into text
) 
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    t.table_name::text,
    t.table_type::text,
    t.is_insertable_into::text
  FROM 
    information_schema.tables t
  WHERE 
    t.table_schema = 'public'
  ORDER BY 
    t.table_name;
END;
$$;
