-- Ensure the tournament_status ENUM type exists
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'tournament_status') THEN
        CREATE TYPE public.tournament_status AS ENUM (
            'planning', 'registration_open', 'registration_closed', 'scheduling', 'live', 'completed', 'cancelled'
        );
    END IF;
END$$;

-- Ensure the status column exists in the tournaments table
ALTER TABLE public.tournaments
ADD COLUMN IF NOT EXISTS status public.tournament_status NOT NULL DEFAULT 'planning';

COMMENT ON COLUMN public.tournaments.status IS 'The current status of the tournament (e.g., planning, live, completed).';
