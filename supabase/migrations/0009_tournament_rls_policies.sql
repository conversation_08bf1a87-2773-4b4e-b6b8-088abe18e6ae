-- Enable RLS on tournaments table if not already enabled (should be by full schema)
ALTER TABLE public.tournaments ENABLE ROW LEVEL SECURITY;

-- Policy: Authenticated users can insert tournaments, setting themselves as the director
DROP POLICY IF EXISTS "Authenticated users can create tournaments" ON public.tournaments;
CREATE POLICY "Authenticated users can create tournaments"
ON public.tournaments
FOR INSERT
WITH CHECK (director_user_id = auth.uid());

-- Policy: Directors can view their own tournaments
DROP POLICY IF EXISTS "Directors can view their own tournaments" ON public.tournaments;
CREATE POLICY "Directors can view their own tournaments"
ON public.tournaments
FOR SELECT
USING (director_user_id = auth.uid());

-- Policy: Directors can update their own tournaments
DROP POLICY IF EXISTS "Directors can update their own tournaments" ON public.tournaments;
CREATE POLICY "Directors can update their own tournaments"
ON public.tournaments
FOR UPDATE
USING (director_user_id = auth.uid())
WITH CHECK (director_user_id = auth.uid());

-- Policy: Directors can delete their own tournaments (optional, consider soft delete or admin-only delete)
DROP POLICY IF EXISTS "Directors can delete their own tournaments" ON public.tournaments;
CREATE POLICY "Directors can delete their own tournaments"
ON public.tournaments
FOR DELETE
USING (director_user_id = auth.uid());

-- Policy: Admins can manage all tournaments
DROP POLICY IF EXISTS "Admins can manage all tournaments" ON public.tournaments;
CREATE POLICY "Admins can manage all tournaments"
ON public.tournaments
FOR ALL -- Covers SELECT, INSERT, UPDATE, DELETE
USING (public.is_claims_admin())
WITH CHECK (public.is_claims_admin());

-- Ensure public/anon users cannot see any tournaments by default if not explicitly allowed
-- (This is usually covered by default deny if no policies match, but can be explicit)
-- DROP POLICY IF EXISTS "Deny public access by default" ON public.tournaments;
-- CREATE POLICY "Deny public access by default"
-- ON public.tournaments
-- FOR SELECT
-- USING (false);
-- Note: A public read policy might be desired later, e.g., for live/completed tournaments.
