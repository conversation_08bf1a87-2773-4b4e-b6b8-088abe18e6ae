-- Add contact information columns to the venues table
ALTER TABLE public.venues
ADD COLUMN IF NOT EXISTS contact_person TEXT NULL,
ADD COLUMN IF NOT EXISTS contact_email TEXT NULL,
ADD COLUMN IF NOT EXISTS contact_phone TEXT NULL;

COMMENT ON COLUMN public.venues.contact_person IS 'Primary contact person for the venue.';
COMMENT ON COLUMN public.venues.contact_email IS 'Email address for the venue contact.';
COMMENT ON COLUMN public.venues.contact_phone IS 'Phone number for the venue contact.';
