-- Venues RPC Functions

CREATE OR REPLACE FUNCTION create_venue(p_venue_data JSONB)
RETURNS SETOF public.venues
LANGUAGE plpgsql SECURITY INVOKER AS $$
DECLARE
    v_new_venue_id UUID;
BEGIN
    INSERT INTO public.venues (
        name, address, city, state, zip_code, notes, 
        operational_start_time, operational_end_time, status, website, parking_fee, restrooms_available,
        contact_person, contact_email, contact_phone
    )
    VALUES (
        p_venue_data ->> 'name',
        p_venue_data ->> 'address',
        p_venue_data ->> 'city',
        p_venue_data ->> 'state',
        p_venue_data ->> 'zip_code',
        p_venue_data ->> 'notes',
        (p_venue_data ->> 'operational_start_time')::TIME,
        (p_venue_data ->> 'operational_end_time')::TIME,
        p_venue_data ->> 'status',
        p_venue_data ->> 'website',
        (p_venue_data ->> 'parking_fee')::NUMERIC,
        (p_venue_data ->> 'restrooms_available')::BOOLEAN,
        p_venue_data ->> 'contact_person',
        p_venue_data ->> 'contact_email',
        p_venue_data ->> 'contact_phone'
    ) RETURNING id INTO v_new_venue_id;
    RETURN QUERY SELECT * FROM public.venues WHERE id = v_new_venue_id;
END;
$$;

CREATE OR REPLACE FUNCTION get_venues()
RETURNS SETOF public.venues
LANGUAGE sql STABLE SECURITY INVOKER AS $$
  SELECT * FROM public.venues;
$$;

CREATE OR REPLACE FUNCTION update_venue(p_venue_id UUID, p_venue_data JSONB)
RETURNS SETOF public.venues
LANGUAGE plpgsql SECURITY INVOKER AS $$
BEGIN
    UPDATE public.venues
    SET
        name = COALESCE(p_venue_data ->> 'name', name),
        address = COALESCE(p_venue_data ->> 'address', address),
        city = COALESCE(p_venue_data ->> 'city', city),
        state = COALESCE(p_venue_data ->> 'state', state),
        zip_code = COALESCE(p_venue_data ->> 'zip_code', zip_code),
        notes = COALESCE(p_venue_data ->> 'notes', notes),
        operational_start_time = COALESCE((p_venue_data ->> 'operational_start_time')::TIME, operational_start_time),
        operational_end_time = COALESCE((p_venue_data ->> 'operational_end_time')::TIME, operational_end_time),
        status = COALESCE(p_venue_data ->> 'status', status),
        website = COALESCE(p_venue_data ->> 'website', website),
        parking_fee = COALESCE((p_venue_data ->> 'parking_fee')::NUMERIC, parking_fee),
        restrooms_available = COALESCE((p_venue_data ->> 'restrooms_available')::BOOLEAN, restrooms_available),
        contact_person = COALESCE(p_venue_data ->> 'contact_person', contact_person),
        contact_email = COALESCE(p_venue_data ->> 'contact_email', contact_email),
        contact_phone = COALESCE(p_venue_data ->> 'contact_phone', contact_phone),
        updated_at = timezone('utc', now())
    WHERE id = p_venue_id;
    RETURN QUERY SELECT * FROM public.venues WHERE id = p_venue_id;
END;
$$;

-- Fields RPC Functions

CREATE OR REPLACE FUNCTION create_field(p_field_data JSONB)
RETURNS SETOF public.fields
LANGUAGE plpgsql SECURITY INVOKER AS $$
DECLARE
    v_new_field_id UUID;
BEGIN
    INSERT INTO public.fields (
        venue_id, name_number, surface_type, lights, notes, 
        operational_start_time, operational_end_time, field_status, location_type, field_type_id
    )
    VALUES (
        (p_field_data ->> 'venue_id')::UUID,
        p_field_data ->> 'name_number',
        p_field_data ->> 'surface_type',
        (p_field_data ->> 'lights')::BOOLEAN,
        p_field_data ->> 'notes',
        (p_field_data ->> 'operational_start_time')::TIME,
        (p_field_data ->> 'operational_end_time')::TIME,
        p_field_data ->> 'field_status',
        p_field_data ->> 'location_type',
        (p_field_data ->> 'field_type_id')::UUID
    ) RETURNING id INTO v_new_field_id;
    RETURN QUERY SELECT * FROM public.fields WHERE id = v_new_field_id;
END;
$$;

CREATE OR REPLACE FUNCTION get_fields_for_venue(p_venue_id UUID)
RETURNS SETOF public.fields
LANGUAGE sql STABLE SECURITY INVOKER AS $$
  SELECT * FROM public.fields WHERE venue_id = p_venue_id;
$$;

CREATE OR REPLACE FUNCTION update_field(p_field_id UUID, p_field_data JSONB)
RETURNS SETOF public.fields 
LANGUAGE plpgsql SECURITY INVOKER AS $$ -- Reverted to SECURITY INVOKER
DECLARE
    v_calculated_field_type_id UUID;
    v_size_text TEXT;
    v_input_field_type_id_text TEXT;
    v_final_field_type_id_to_use UUID;
BEGIN
    v_input_field_type_id_text := p_field_data ->> 'field_type_id';

    IF v_input_field_type_id_text IS NOT NULL AND v_input_field_type_id_text <> '' THEN
        BEGIN
            v_calculated_field_type_id := v_input_field_type_id_text::UUID;
        EXCEPTION
            WHEN invalid_text_representation THEN
                v_calculated_field_type_id := NULL;
        END;
    END IF;

    IF v_calculated_field_type_id IS NULL THEN
        v_size_text := TRIM(p_field_data ->> 'size');
        IF v_size_text IS NOT NULL AND v_size_text <> '' THEN
            SELECT ft.id INTO v_calculated_field_type_id
            FROM public.field_types ft
            WHERE ft.name ILIKE v_size_text || '%'
            ORDER BY length(ft.name) ASC
            LIMIT 1;
        END IF;
    END IF;
    
    v_final_field_type_id_to_use := v_calculated_field_type_id;

    UPDATE public.fields f
    SET
        name_number = COALESCE(p_field_data ->> 'name_number', f.name_number),
        surface_type = COALESCE(p_field_data ->> 'surface_type', f.surface_type),
        lights = COALESCE((p_field_data ->> 'lights')::BOOLEAN, f.lights),
        notes = COALESCE(p_field_data ->> 'notes', f.notes) || ' (debug_ts ' || timezone('utc', now())::TEXT || ')', -- Force notes update
        operational_start_time = COALESCE((p_field_data ->> 'operational_start_time')::TIME, f.operational_start_time),
        operational_end_time = COALESCE((p_field_data ->> 'operational_end_time')::TIME, f.operational_end_time),
        field_status = COALESCE(p_field_data ->> 'field_status', f.field_status),
        location_type = COALESCE(p_field_data ->> 'location_type', f.location_type),
        field_type_id = v_final_field_type_id_to_use, -- Kept direct assignment
        updated_at = timezone('utc', now())
    WHERE f.id = p_field_id;
    
    -- Use RETURN QUERY with the UPDATE ... RETURNING statement
    RETURN QUERY 
    UPDATE public.fields f
    SET
        name_number = COALESCE(p_field_data ->> 'name_number', f.name_number),
        surface_type = COALESCE(p_field_data ->> 'surface_type', f.surface_type),
        lights = COALESCE((p_field_data ->> 'lights')::BOOLEAN, f.lights),
        notes = COALESCE(p_field_data ->> 'notes', f.notes), 
        operational_start_time = COALESCE((p_field_data ->> 'operational_start_time')::TIME, f.operational_start_time),
        operational_end_time = COALESCE((p_field_data ->> 'operational_end_time')::TIME, f.operational_end_time),
        field_status = COALESCE(p_field_data ->> 'field_status', f.field_status),
        location_type = COALESCE(p_field_data ->> 'location_type', f.location_type),
        field_type_id = v_final_field_type_id_to_use, 
        updated_at = timezone('utc', now())
    WHERE f.id = p_field_id::UUID -- Explicit cast for p_field_id
    RETURNING f.*;
END;
$$;

-- Field Unavailability RPC Functions

CREATE OR REPLACE FUNCTION add_field_unavailability(p_unavailability_data JSONB)
RETURNS SETOF public.field_unavailability
LANGUAGE plpgsql SECURITY INVOKER AS $$
DECLARE
    v_new_unavailability_id UUID;
BEGIN
    INSERT INTO public.field_unavailability (
        field_id, tournament_id, unavailable_start, unavailable_end, reason
    )
    VALUES (
        (p_unavailability_data ->> 'field_id')::UUID,
        (p_unavailability_data ->> 'tournament_id')::UUID, -- Allow NULL if not provided
        (p_unavailability_data ->> 'unavailable_start')::TIMESTAMPTZ,
        (p_unavailability_data ->> 'unavailable_end')::TIMESTAMPTZ,
        p_unavailability_data ->> 'reason'
    ) RETURNING id INTO v_new_unavailability_id;
    RETURN QUERY SELECT * FROM public.field_unavailability WHERE id = v_new_unavailability_id;
END;
$$;

CREATE OR REPLACE FUNCTION get_field_unavailabilities(p_field_id_param UUID, p_tournament_id_filter UUID DEFAULT NULL)
RETURNS SETOF public.field_unavailability
LANGUAGE sql STABLE SECURITY INVOKER AS $$
  SELECT * 
  FROM public.field_unavailability
  WHERE field_id = p_field_id_param
    AND (tournament_id = p_tournament_id_filter OR p_tournament_id_filter IS NULL);
$$;

CREATE OR REPLACE FUNCTION delete_field_unavailability(p_unavailability_id UUID)
RETURNS void
LANGUAGE sql SECURITY INVOKER AS $$
  DELETE FROM public.field_unavailability WHERE id = p_unavailability_id;
$$;

-- Field Types RPC Function
CREATE OR REPLACE FUNCTION get_field_types()
RETURNS SETOF public.field_types
LANGUAGE sql STABLE SECURITY INVOKER AS $$
  SELECT * FROM public.field_types;
$$;
