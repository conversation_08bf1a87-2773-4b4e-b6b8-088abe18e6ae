-- Enable RLS on fields table if not already enabled (should be by full schema)
ALTER TABLE public.fields ENABLE ROW LEVEL SECURITY;

-- Policy: Authenticated users can insert new fields.
-- This is a broad policy. More specific policies might be needed if, for example,
-- only the user who created the venue (or an admin) can add fields to it.
-- However, the 'venues' table currently doesn't have an owner_id.
-- For now, allowing any authenticated user to add fields.
DROP POLICY IF EXISTS "Authenticated users can create fields" ON public.fields;
CREATE POLICY "Authenticated users can create fields"
ON public.fields
FOR INSERT
TO authenticated -- Grant to the 'authenticated' role
WITH CHECK (true); -- No specific check other than being authenticated

-- Policy: Allow public read access to fields.
DROP POLICY IF EXISTS "Public can view fields" ON public.fields;
CREATE POLICY "Public can view fields"
ON public.fields
FOR SELECT
USING (true);

-- Policy: Admins can manage all fields (update/delete).
DROP POLICY IF EXISTS "Admins can manage all fields" ON public.fields;
CREATE POLICY "Admins can manage all fields"
ON public.fields
FOR ALL -- Covers SELECT, INSERT, UPDATE, DELETE
USING (public.is_claims_admin())
WITH CHECK (public.is_claims_admin());

-- Note: Similar to venues, more granular UPDATE/DELETE policies for fields
-- would typically depend on ownership of the venue or the field itself.
