-- Migration: Add division-centric columns to tournaments table
-- This migration adds support for the new division-based tournament structure

-- Add new columns to tournaments table
ALTER TABLE public.tournaments 
ADD COLUMN IF NOT EXISTS divisions JSONB NULL,
ADD COLUMN IF NOT EXISTS early_bird_discount NUMERIC(10,2) NULL,
ADD COLUMN IF NOT EXISTS early_bird_deadline TIMESTAMPTZ NULL,
ADD COLUMN IF NOT EXISTS late_fee_amount NUMERIC(10,2) NULL,
ADD COLUMN IF NOT EXISTS late_registration_start_date TIMESTAMPTZ NULL;

-- Add comments for documentation
COMMENT ON COLUMN public.tournaments.divisions IS 'JSONB array containing division objects with age groups, gender, competitive level, play format, and fees';
COMMENT ON COLUMN public.tournaments.early_bird_discount IS 'Early bird discount amount in dollars';
COMMENT ON COLUMN public.tournaments.early_bird_deadline IS 'Deadline for early bird registration discount';
COMMENT ON COLUMN public.tournaments.late_fee_amount IS 'Late registration fee amount in dollars';
COMMENT ON COLUMN public.tournaments.late_registration_start_date IS 'Date when late registration fees start applying';
