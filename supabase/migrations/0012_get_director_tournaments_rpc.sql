CREATE OR REPLACE FUNCTION get_director_tournaments()
RETURNS SETOF public.tournaments
LANGUAGE sql
STABLE
SECURITY INVOKER
AS $$
  SELECT
    id,
    name,
    start_date,
    end_date,
    director_user_id,
    managing_club_id,
    status,
    registration_deadline,
    location_city,
    location_state,
    rules_url,
    rules_text,
    website,
    logo_url,
    min_games_guaranteed,
    allow_guest_players,
    season_year,
    tournament_creation_fee,
    creation_payment_status,
    creation_payment_intent_id,
    custom_settings,
    -- Include new division-centric columns
    divisions,
    early_bird_discount,
    early_bird_deadline,
    late_fee_amount,
    late_registration_start_date,
    created_at,
    updated_at
  FROM public.tournaments
  WHERE director_user_id = auth.uid()
  ORDER BY start_date DESC, name ASC;
$$;

COMMENT ON FUNCTION get_director_tournaments() IS 'Retrieves all tournaments created by the currently authenticated user (director).';
