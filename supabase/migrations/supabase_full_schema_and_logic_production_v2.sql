-- production_final.sql
-- Supabase Full Schema and Logic - Production Final (V7 Final)
--
-- Documentation:
-- This script is the final production baseline for our Supabase tournament management database.
--
-- Changes made:
-- 1. Removed explicit SECURITY DEFINER clauses from views so that they use SECURITY INVOKER.
-- 2. Fixed the audit logging trigger function "log_audit_trail" by capturing TG_OP into a local variable (v_action)
--    to avoid errors such as "column 'tg_op' does not exist."
-- 3. Organized the script in phases for clarity. Triggers for audit logging remain commented out for initial testing.
-- 4. RLS is enabled on key tables.
-- 5. Added bracket size validation and bye handling features:
--    - Added round_number, is_bye columns to matches table
--    - Created views for bracket analysis and fairness analysis
--    - Added functions for bracket validation, round-robin scheduling, and fairness analysis
--    - Created API endpoints for the scheduler service
--
-- Change Management:
-- - Run this script as an admin (via Supabase CLI or psql).
-- - Seed initial data with valid IDs.
-- - Verify core functions and procedures (e.g., refresh_standings, update_match_score_manual).
-- - Only after successful testing, uncomment the trigger creation statements in PHASE 9.
-- - Commit this script to the repository as the production baseline.

---------------------------------------------------------------
-- PHASE 0: Extensions
---------------------------------------------------------------
CREATE EXTENSION IF NOT EXISTS hstore;

---------------------------------------------------------------
-- PHASE 1: ENUM Types
---------------------------------------------------------------
DROP TYPE IF EXISTS public.tournament_status CASCADE;
DROP TYPE IF EXISTS public.match_status CASCADE;
DROP TYPE IF EXISTS public.user_role CASCADE;
DROP TYPE IF EXISTS public.payment_status CASCADE;
DROP TYPE IF EXISTS public.bracket_round CASCADE;
DROP TYPE IF EXISTS public.assignment_role CASCADE;
DROP TYPE IF EXISTS public.team_role CASCADE;
DROP TYPE IF EXISTS public.request_type CASCADE;

CREATE TYPE public.tournament_status AS ENUM (
    'planning', 'registration_open', 'registration_closed', 'scheduling', 'live', 'completed', 'cancelled'
);
CREATE TYPE public.match_status AS ENUM (
    'scheduled', 'in_progress', 'completed', 'penalty_shootout', 'postponed', 'cancelled_weather', 'cancelled_other', 'forfeited_team1', 'forfeited_team2'
);
CREATE TYPE public.user_role AS ENUM (
    'admin', 'tournament_director', 'club_admin', 'coach', 'team_manager', 'referee', 'player_parent'
);
CREATE TYPE public.payment_status AS ENUM (
    'pending', 'paid', 'failed', 'waived', 'refunded'
);
CREATE TYPE public.bracket_round AS ENUM (
    'group', 'round_of_16', 'quarterfinal', 'semifinal', 'final', 'consolation'
);
CREATE TYPE public.assignment_role AS ENUM (
    'Center', 'AR1', 'AR2', '4th Official'
);
CREATE TYPE public.team_role AS ENUM (
    'Coach', 'Assistant Coach', 'Manager'
);
CREATE TYPE public.request_type AS ENUM (
    'Cannot Play Before', 'Cannot Play After', 'Cannot Play Between', 'Prefer Morning', 'Prefer Afternoon'
);

-- Added for club_directors table
DROP TYPE IF EXISTS public.director_affiliation_status CASCADE;
CREATE TYPE public.director_affiliation_status AS ENUM (
    'pending',
    'approved',
    'rejected',
    'invited',
    'inactive'
);

---------------------------------------------------------------
-- PHASE 2: Helper Functions & Independent Tables
---------------------------------------------------------------

-- Generic Timestamp Update Function
CREATE OR REPLACE FUNCTION public.handle_profile_update()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = timezone('utc', now());
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Helper function to check admin role
CREATE OR REPLACE FUNCTION public.is_claims_admin()
RETURNS boolean
LANGUAGE sql STABLE SECURITY DEFINER AS $$
    SELECT coalesce(current_setting('request.jwt.claims', true)::jsonb ->> 'user_role' = 'admin', false);
$$;

-- Function: Calculate Player 'U' Age
CREATE OR REPLACE FUNCTION public.get_player_u_age(p_birthdate DATE, p_season_end_year INT)
RETURNS INT
LANGUAGE sql IMMUTABLE AS $$
    SELECT p_season_end_year - EXTRACT(YEAR FROM p_birthdate)::INT;
$$;

-- Function: Calculate Fees
CREATE OR REPLACE FUNCTION public.calculate_fee(p_fee_type TEXT, p_base_amount NUMERIC DEFAULT NULL)
RETURNS NUMERIC
LANGUAGE plpgsql STABLE AS $$
DECLARE
    v_fee_config RECORD;
    v_calculated_fee NUMERIC := 0;
BEGIN
    SELECT * INTO v_fee_config
      FROM public.fee_configurations
     WHERE fee_type = p_fee_type
       AND is_active = TRUE;
    IF FOUND THEN
        IF v_fee_config.amount IS NOT NULL THEN
            v_calculated_fee := v_fee_config.amount;
        ELSIF v_fee_config.percentage IS NOT NULL AND p_base_amount IS NOT NULL THEN
            v_calculated_fee := p_base_amount * v_fee_config.percentage;
        ELSIF v_fee_config.percentage IS NOT NULL AND p_base_amount IS NULL THEN
            RAISE WARNING 'Cannot calculate percentage fee for % without a base amount.', p_fee_type;
            v_calculated_fee := 0;
        END IF;
    ELSE
        RAISE WARNING 'Fee configuration not found or inactive: %', p_fee_type;
        v_calculated_fee := 0;
    END IF;
    RETURN v_calculated_fee;
END;
$$;

-- Profiles Table
CREATE TABLE IF NOT EXISTS public.profiles (
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    email TEXT UNIQUE NOT NULL,
    full_name TEXT,
    role user_role NOT NULL DEFAULT 'player_parent',
    phone_number TEXT,
    managing_club_id UUID,
    permissions JSONB NULL,
    created_at TIMESTAMPTZ DEFAULT timezone('utc', now()) NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT timezone('utc', now()) NOT NULL
);

DROP TRIGGER IF EXISTS on_profile_update ON public.profiles;
CREATE TRIGGER on_profile_update
BEFORE UPDATE ON public.profiles
FOR EACH ROW EXECUTE FUNCTION public.handle_profile_update();

-- Clubs Table
CREATE TABLE IF NOT EXISTS public.clubs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL UNIQUE,
    admin_user_id UUID,
    website TEXT,
    logo_url TEXT,
    created_at TIMESTAMPTZ DEFAULT timezone('utc', now()) NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT timezone('utc', now()) NOT NULL
);

DROP TRIGGER IF EXISTS on_club_update ON public.clubs;
CREATE TRIGGER on_club_update
BEFORE UPDATE ON public.clubs
FOR EACH ROW EXECUTE FUNCTION public.handle_profile_update();

-- Add Foreign Keys (DEFERRABLE)
ALTER TABLE public.profiles
    ADD CONSTRAINT profiles_managing_club_id_fkey FOREIGN KEY (managing_club_id)
        REFERENCES public.clubs(id) ON DELETE SET NULL DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE public.clubs
    ADD CONSTRAINT clubs_admin_user_id_fkey FOREIGN KEY (admin_user_id)
        REFERENCES public.profiles(id) ON DELETE SET NULL DEFERRABLE INITIALLY DEFERRED;

-- Field Types Table
CREATE TABLE IF NOT EXISTS public.field_types (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMPTZ DEFAULT timezone('utc', now()) NOT NULL
);
INSERT INTO public.field_types (name, description)
VALUES ('4v4 Mini', 'Smallest field, typically for U6-U8'),
       ('7v7', 'Mid-size field, typically for U9-U10'),
       ('9v9', '3/4 size field, typically for U11-U12'),
       ('11v11 Full-Size', 'Standard regulation field, typically for U13+')
ON CONFLICT (name) DO NOTHING;

-- Venues Table
CREATE TABLE IF NOT EXISTS public.venues (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL UNIQUE,
    address TEXT,
    city TEXT,
    state TEXT,
    zip_code TEXT,
    notes TEXT,
    parking_fee TEXT,
    restrooms_available TEXT,
    website TEXT,
    status TEXT,
    operational_start_time TIME NULL,
    operational_end_time TIME NULL,
    created_at TIMESTAMPTZ DEFAULT timezone('utc', now()) NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT timezone('utc', now()) NOT NULL
);
DROP TRIGGER IF EXISTS on_venue_update ON public.venues;
CREATE TRIGGER on_venue_update
BEFORE UPDATE ON public.venues
FOR EACH ROW EXECUTE FUNCTION public.handle_profile_update();

-- Players Table
CREATE TABLE IF NOT EXISTS public.players (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    first_name TEXT NOT NULL,
    last_name TEXT NOT NULL,
    date_of_birth DATE NOT NULL,
    gender TEXT CHECK (gender IN ('Male', 'Female')),
    guardian_profile_id UUID REFERENCES public.profiles(id) ON DELETE SET NULL,
    player_id_card_url TEXT,
    notes TEXT,
    created_at TIMESTAMPTZ DEFAULT timezone('utc', now()) NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT timezone('utc', now()) NOT NULL
);
DROP TRIGGER IF EXISTS on_player_update ON public.players;
CREATE TRIGGER on_player_update
BEFORE UPDATE ON public.players
FOR EACH ROW EXECUTE FUNCTION public.handle_profile_update();

-- Referees Table
CREATE TABLE IF NOT EXISTS public.referees (
    profile_id UUID PRIMARY KEY REFERENCES public.profiles(id) ON DELETE CASCADE,
    referee_grade TEXT,
    certifying_body TEXT,
    certification_expiry DATE,
    years_experience INT,
    notes TEXT,
    max_games_per_day INT NULL,
    min_rest_minutes INT NULL DEFAULT 45,
    affiliated_club_id UUID REFERENCES public.clubs(id) ON DELETE SET NULL,
    created_at TIMESTAMPTZ DEFAULT timezone('utc', now()) NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT timezone('utc', now()) NOT NULL
);
DROP TRIGGER IF EXISTS on_referee_update ON public.referees;
CREATE TRIGGER on_referee_update
BEFORE UPDATE ON public.referees
FOR EACH ROW EXECUTE FUNCTION public.handle_profile_update();

-- Fee Configurations Table
CREATE TABLE IF NOT EXISTS public.fee_configurations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    fee_type TEXT NOT NULL UNIQUE,
    amount NUMERIC(10,2) NULL,
    percentage NUMERIC(5,4) NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT timezone('utc', now()) NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT timezone('utc', now()) NOT NULL
);
DROP TRIGGER IF EXISTS on_fee_config_update ON public.fee_configurations;
CREATE TRIGGER on_fee_config_update
BEFORE UPDATE ON public.fee_configurations
FOR EACH ROW EXECUTE FUNCTION public.handle_profile_update();

---------------------------------------------------------------
-- PHASE 3-6: Dependent Tables
---------------------------------------------------------------

-- Teams Table
CREATE TABLE IF NOT EXISTS public.teams (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    club_id UUID REFERENCES public.clubs(id) ON DELETE SET NULL,
    gender TEXT CHECK (gender IN ('Boys', 'Girls', 'Co-Ed')),
    city TEXT,
    state TEXT,
    notes TEXT,
    logo_url TEXT,
    created_at TIMESTAMPTZ DEFAULT timezone('utc', now()) NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT timezone('utc', now()) NOT NULL
);
DROP TRIGGER IF EXISTS on_team_update ON public.teams;
CREATE TRIGGER on_team_update
BEFORE UPDATE ON public.teams
FOR EACH ROW EXECUTE FUNCTION public.handle_profile_update();

-- Tournaments Table
CREATE TABLE IF NOT EXISTS public.tournaments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    director_user_id UUID REFERENCES public.profiles(id) ON DELETE SET NULL,
    managing_club_id UUID REFERENCES public.clubs(id) ON DELETE SET NULL,
    status tournament_status NOT NULL DEFAULT 'planning',
    registration_deadline TIMESTAMPTZ,
    location_city TEXT,
    location_state TEXT,
    rules_url TEXT,
    rules_text TEXT,
    website TEXT,
    logo_url TEXT,
    min_games_guaranteed INT DEFAULT 3,
    allow_guest_players BOOLEAN DEFAULT false,
    season_year INT NULL,
    tournament_creation_fee NUMERIC(10,2) NULL,
    creation_payment_status payment_status NULL DEFAULT 'pending',
    creation_payment_intent_id TEXT NULL,
    custom_settings JSONB NULL,
    created_at TIMESTAMPTZ DEFAULT timezone('utc', now()) NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT timezone('utc', now()) NOT NULL
);
DROP TRIGGER IF EXISTS on_tournament_update ON public.tournaments;
CREATE TRIGGER on_tournament_update
BEFORE UPDATE ON public.tournaments
FOR EACH ROW EXECUTE FUNCTION public.handle_profile_update();

-- Fields Table
CREATE TABLE IF NOT EXISTS public.fields (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    venue_id UUID NOT NULL REFERENCES public.venues(id) ON DELETE CASCADE,
    name_number TEXT NOT NULL,
    surface_type TEXT,
    lights BOOLEAN DEFAULT false,
    notes TEXT,
    operational_start_time TIME NULL,
    operational_end_time TIME NULL,
    field_status TEXT DEFAULT 'Open',
    location_type TEXT CHECK (location_type IN ('Indoor', 'Outdoor')) NULL,
    field_type_id UUID REFERENCES public.field_types(id) ON DELETE SET NULL,
    created_at TIMESTAMPTZ DEFAULT timezone('utc', now()) NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT timezone('utc', now()) NOT NULL,
    UNIQUE (venue_id, name_number)
);
DROP TRIGGER IF EXISTS on_field_update ON public.fields;
CREATE TRIGGER on_field_update
BEFORE UPDATE ON public.fields
FOR EACH ROW EXECUTE FUNCTION public.handle_profile_update();

-- Tournament Age Groups Table
CREATE TABLE IF NOT EXISTS public.tournament_age_groups (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tournament_id UUID NOT NULL REFERENCES public.tournaments(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    gender TEXT CHECK (gender IN ('Boys', 'Girls', 'Co-Ed')),
    age_year INT,
    age_u INT,
    max_teams INT,
    registration_fee NUMERIC(10,2),
    format TEXT NOT NULL,
    game_duration_minutes INT,
    halftime_minutes INT DEFAULT 5,
    transition_minutes_after INT DEFAULT 15,
    custom_settings JSONB NULL,
    created_at TIMESTAMPTZ DEFAULT timezone('utc', now()) NOT NULL,
    UNIQUE (tournament_id, name)
);

-- Team Staff Table
CREATE TABLE IF NOT EXISTS public.team_staff (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    team_id UUID NOT NULL REFERENCES public.teams(id) ON DELETE CASCADE,
    profile_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    role team_role NOT NULL,
    is_primary BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMPTZ DEFAULT timezone('utc', now()) NOT NULL,
    UNIQUE (team_id, profile_id)
);

-- Referee Availability Table
CREATE TABLE IF NOT EXISTS public.referee_availability (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    referee_profile_id UUID NOT NULL REFERENCES public.referees(profile_id) ON DELETE CASCADE,
    tournament_id UUID NOT NULL REFERENCES public.tournaments(id) ON DELETE CASCADE,
    available_start_time TIMESTAMPTZ NOT NULL,
    available_end_time TIMESTAMPTZ NOT NULL,
    notes TEXT,
    created_at TIMESTAMPTZ DEFAULT timezone('utc', now()) NOT NULL,
    CONSTRAINT end_after_start CHECK (available_end_time > available_start_time),
    UNIQUE (referee_profile_id, tournament_id, available_start_time)
);

-- Referee Preferences Table
CREATE TABLE IF NOT EXISTS public.referee_preferences (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    referee_profile_id UUID NOT NULL REFERENCES public.referees(profile_id) ON DELETE CASCADE,
    tournament_id UUID NOT NULL REFERENCES public.tournaments(id) ON DELETE CASCADE,
    preferred_age_group_id UUID REFERENCES public.tournament_age_groups(id) ON DELETE SET NULL,
    preferred_field_id UUID REFERENCES public.fields(id) ON DELETE SET NULL,
    preferred_role assignment_role NULL,
    notes TEXT,
    created_at TIMESTAMPTZ DEFAULT timezone('utc', now()) NOT NULL,
    UNIQUE (referee_profile_id, tournament_id)
);

-- Team Members Table
CREATE TABLE IF NOT EXISTS public.team_members (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    team_id UUID NOT NULL REFERENCES public.teams(id) ON DELETE CASCADE,
    player_id UUID NOT NULL REFERENCES public.players(id) ON DELETE CASCADE,
    tournament_id UUID REFERENCES public.tournaments(id) ON DELETE CASCADE,
    jersey_number INT,
    is_guest_player BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    roster_notes TEXT,
    created_at TIMESTAMPTZ DEFAULT timezone('utc', now()) NOT NULL,
    UNIQUE(team_id, player_id, tournament_id)
);

-- Field Unavailability Table
CREATE TABLE IF NOT EXISTS public.field_unavailability (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    field_id UUID NOT NULL REFERENCES public.fields(id) ON DELETE CASCADE,
    tournament_id UUID REFERENCES public.tournaments(id) ON DELETE CASCADE,
    unavailable_start TIMESTAMPTZ NOT NULL,
    unavailable_end TIMESTAMPTZ NOT NULL,
    reason TEXT,
    created_at TIMESTAMPTZ DEFAULT timezone('utc', now()) NOT NULL,
    CONSTRAINT check_field_unavailability_times CHECK (unavailable_end > unavailable_start)
);

-- Tournament Groups Table
CREATE TABLE IF NOT EXISTS public.tournament_groups (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tournament_age_group_id UUID NOT NULL REFERENCES public.tournament_age_groups(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    games_played INT DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT timezone('utc', now()) NOT NULL,
    UNIQUE (tournament_age_group_id, name)
);

-- Registrations Table
CREATE TABLE IF NOT EXISTS public.registrations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    team_id UUID NOT NULL REFERENCES public.teams(id) ON DELETE CASCADE,
    tournament_age_group_id UUID NOT NULL REFERENCES public.tournament_age_groups(id) ON DELETE CASCADE,
    registration_date TIMESTAMPTZ DEFAULT timezone('utc', now()) NOT NULL,
    payment_status payment_status NOT NULL DEFAULT 'pending',
    payment_intent_id TEXT,
    assigned_group_id UUID REFERENCES public.tournament_groups(id) ON DELETE SET NULL,
    notes TEXT,
    seeding_rank INT NULL,
    created_at TIMESTAMPTZ DEFAULT timezone('utc', now()) NOT NULL,
    UNIQUE (team_id, tournament_age_group_id)
);

-- Matches Table
CREATE TABLE IF NOT EXISTS public.matches (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tournament_id UUID NOT NULL REFERENCES public.tournaments(id) ON DELETE CASCADE,
    tournament_age_group_id UUID NOT NULL REFERENCES public.tournament_age_groups(id) ON DELETE CASCADE,
    group_id UUID REFERENCES public.tournament_groups(id) ON DELETE SET NULL,
    round_name bracket_round NOT NULL,
    round_number INT,
    match_number INT,
    team1_id UUID REFERENCES public.teams(id) ON DELETE SET NULL,
    team2_id UUID REFERENCES public.teams(id) ON DELETE SET NULL,
    placeholder_team1_text TEXT NULL,
    placeholder_team2_text TEXT NULL,
    field_id UUID REFERENCES public.fields(id) ON DELETE SET NULL,
    scheduled_time TIMESTAMPTZ,
    actual_start_time TIMESTAMPTZ,
    actual_end_time TIMESTAMPTZ,
    status match_status NOT NULL DEFAULT 'scheduled',
    team1_score INT CHECK (team1_score >= 0),
    team2_score INT CHECK (team2_score >= 0),
    team1_penalty_score INT CHECK (team1_penalty_score >= 0),
    team2_penalty_score INT CHECK (team2_penalty_score >= 0),
    winner_id UUID REFERENCES public.teams(id) ON DELETE SET NULL,
    forfeit_winner_id UUID REFERENCES public.teams(id) ON DELETE SET NULL,
    last_manual_override_by UUID REFERENCES public.profiles(id) ON DELETE SET NULL,
    last_manual_override_at TIMESTAMPTZ NULL,
    override_notes TEXT NULL,
    previous_override_details JSONB NULL,
    referee_notes TEXT,
    internal_notes TEXT,
    is_bye BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMPTZ DEFAULT timezone('utc', now()) NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT timezone('utc', now()) NOT NULL,
    CHECK (team1_id IS NULL OR team2_id IS NULL OR team1_id <> team2_id)
);
DROP TRIGGER IF EXISTS on_match_update ON public.matches;
CREATE TRIGGER on_match_update
BEFORE UPDATE ON public.matches
FOR EACH ROW EXECUTE FUNCTION public.handle_profile_update();

-- Standings Table
CREATE TABLE IF NOT EXISTS public.standings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tournament_id UUID NOT NULL REFERENCES public.tournaments(id) ON DELETE CASCADE,
    tournament_age_group_id UUID NOT NULL REFERENCES public.tournament_age_groups(id) ON DELETE CASCADE,
    group_id UUID NOT NULL REFERENCES public.tournament_groups(id) ON DELETE CASCADE,
    team_id UUID NOT NULL REFERENCES public.teams(id) ON DELETE CASCADE,
    name TEXT,
    age_group TEXT,
    group_name TEXT,
    played INT DEFAULT 0 NOT NULL,
    wins INT DEFAULT 0 NOT NULL,
    draws INT DEFAULT 0 NOT NULL,
    losses INT DEFAULT 0 NOT NULL,
    points INT DEFAULT 0 NOT NULL,
    goals_for INT DEFAULT 0 NOT NULL,
    goals_against INT DEFAULT 0 NOT NULL,
    goal_difference INT DEFAULT 0 NOT NULL,
    capped_goal_difference INT DEFAULT 0 NOT NULL,
    shutouts INT DEFAULT 0 NOT NULL,
    group_position INT,
    last_manual_override_by UUID REFERENCES public.profiles(id) ON DELETE SET NULL,
    last_manual_override_at TIMESTAMPTZ NULL,
    override_notes TEXT NULL,
    previous_override_details JSONB NULL,
    updated_at TIMESTAMPTZ DEFAULT timezone('utc', now()) NOT NULL,
    UNIQUE (tournament_id, group_id, team_id)
);
DROP TRIGGER IF EXISTS on_standing_update ON public.standings;
CREATE TRIGGER on_standing_update
BEFORE UPDATE ON public.standings
FOR EACH ROW EXECUTE FUNCTION public.handle_profile_update();

-- Tie Breaker Overrides Table
CREATE TABLE IF NOT EXISTS public.tie_breaker_overrides (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    group_id UUID NOT NULL REFERENCES public.tournament_groups(id) ON DELETE CASCADE,
    team_id UUID NOT NULL REFERENCES public.teams(id) ON DELETE CASCADE,
    is_override_winner BOOLEAN DEFAULT FALSE,
    notes TEXT,
    set_by_user_id UUID REFERENCES public.profiles(id),
    created_at TIMESTAMPTZ DEFAULT timezone('utc', now()) NOT NULL,
    UNIQUE (group_id, team_id)
);

-- Match Assignments Table
CREATE TABLE IF NOT EXISTS public.match_assignments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    match_id UUID NOT NULL REFERENCES public.matches(id) ON DELETE CASCADE,
    referee_profile_id UUID NOT NULL REFERENCES public.referees(profile_id) ON DELETE CASCADE,
    role assignment_role NOT NULL,
    confirmed BOOLEAN DEFAULT FALSE,
    notes TEXT,
    created_at TIMESTAMPTZ DEFAULT timezone('utc', now()) NOT NULL,
    UNIQUE (match_id, referee_profile_id),
    UNIQUE (match_id, role)
);

-- Team Requests Table
CREATE TABLE IF NOT EXISTS public.team_requests (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    registration_id UUID NOT NULL REFERENCES public.registrations(id) ON DELETE CASCADE,
    request request_type NOT NULL,
    start_time TIMESTAMPTZ NULL,
    end_time TIMESTAMPTZ NULL,
    request_day DATE NULL,
    notes TEXT,
    created_at TIMESTAMPTZ DEFAULT timezone('utc', now()) NOT NULL
);

-- Transactions Table
CREATE TABLE IF NOT EXISTS public.transactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    profile_id UUID REFERENCES public.profiles(id) ON DELETE SET NULL,
    registration_id UUID REFERENCES public.registrations(id) ON DELETE SET NULL,
    tournament_id UUID REFERENCES public.tournaments(id) ON DELETE SET NULL,
    related_club_id UUID REFERENCES public.clubs(id) ON DELETE SET NULL,
    fee_config_id UUID REFERENCES public.fee_configurations(id) ON DELETE SET NULL,
    transaction_type TEXT NOT NULL,
    amount NUMERIC(10,2) NOT NULL,
    currency CHAR(3) NOT NULL DEFAULT 'USD',
    payment_provider TEXT,
    provider_transaction_id TEXT UNIQUE,
    status payment_status NOT NULL DEFAULT 'pending',
    notes TEXT,
    created_at TIMESTAMPTZ DEFAULT timezone('utc', now()) NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT timezone('utc', now()) NOT NULL
);
DROP TRIGGER IF EXISTS on_transaction_update ON public.transactions;
CREATE TRIGGER on_transaction_update
BEFORE UPDATE ON public.transactions
FOR EACH ROW EXECUTE FUNCTION public.handle_profile_update();

-- Device Tokens Table
CREATE TABLE IF NOT EXISTS public.device_tokens (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    profile_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    token TEXT NOT NULL UNIQUE,
    device_os TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    last_used TIMESTAMPTZ DEFAULT timezone('utc', now()) NOT NULL,
    created_at TIMESTAMPTZ DEFAULT timezone('utc', now()) NOT NULL
);

-- Audit Log Table
CREATE TABLE IF NOT EXISTS public.audit_log (
    id BIGSERIAL PRIMARY KEY,
    timestamp TIMESTAMPTZ DEFAULT timezone('utc', now()) NOT NULL,
    user_id UUID REFERENCES public.profiles(id) ON DELETE SET NULL,
    user_email TEXT,
    action TEXT NOT NULL,
    table_name TEXT,
    record_id TEXT,
    details JSONB NULL
);

-- Webhook Events Table
CREATE TABLE IF NOT EXISTS public.webhook_events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    received_at TIMESTAMPTZ DEFAULT timezone('utc', now()) NOT NULL,
    provider TEXT NOT NULL,
    event_type TEXT NOT NULL,
    event_id TEXT UNIQUE,
    payload JSONB NULL,
    status TEXT NOT NULL DEFAULT 'received',
    processing_notes TEXT,
    processed_at TIMESTAMPTZ NULL
);

---------------------------------------------------------------
-- PHASE 7: Indexes
---------------------------------------------------------------
CREATE INDEX IF NOT EXISTS idx_matches_tournament_age_group ON public.matches(tournament_id, tournament_age_group_id);
CREATE INDEX IF NOT EXISTS idx_matches_group ON public.matches(group_id);
CREATE INDEX IF NOT EXISTS idx_matches_status ON public.matches(status);
CREATE INDEX IF NOT EXISTS idx_matches_teams ON public.matches(team1_id, team2_id);
CREATE INDEX IF NOT EXISTS idx_matches_scheduled_time ON public.matches(scheduled_time);
CREATE INDEX IF NOT EXISTS idx_standings_tournament_group_team ON public.standings(tournament_id, group_id, team_id);
CREATE INDEX IF NOT EXISTS idx_standings_group_pos ON public.standings(group_id, group_position);
CREATE INDEX IF NOT EXISTS idx_registrations_tournament_age_group ON public.registrations(tournament_age_group_id);
CREATE INDEX IF NOT EXISTS idx_registrations_team ON public.registrations(team_id);
CREATE INDEX IF NOT EXISTS idx_teams_club ON public.teams(club_id);
CREATE INDEX IF NOT EXISTS idx_tournaments_status ON public.tournaments(status);
CREATE INDEX IF NOT EXISTS idx_tournament_age_groups_tournament ON public.tournament_age_groups(tournament_id);
CREATE INDEX IF NOT EXISTS idx_tournament_groups_age_group ON public.tournament_groups(tournament_age_group_id);
CREATE INDEX IF NOT EXISTS idx_referees_grade ON public.referees(referee_grade);
CREATE INDEX IF NOT EXISTS idx_referee_availability_tournament ON public.referee_availability(tournament_id);
CREATE INDEX IF NOT EXISTS idx_referee_availability_referee ON public.referee_availability(referee_profile_id);
CREATE INDEX IF NOT EXISTS idx_referee_availability_times ON public.referee_availability(available_start_time, available_end_time);
CREATE INDEX IF NOT EXISTS idx_match_assignments_match ON public.match_assignments(match_id);
CREATE INDEX IF NOT EXISTS idx_match_assignments_referee ON public.match_assignments(referee_profile_id);
CREATE INDEX IF NOT EXISTS idx_team_staff_team ON public.team_staff(team_id);
CREATE INDEX IF NOT EXISTS idx_team_staff_profile ON public.team_staff(profile_id);
CREATE INDEX IF NOT EXISTS idx_players_guardian ON public.players(guardian_profile_id);
CREATE INDEX IF NOT EXISTS idx_team_members_team ON public.team_members(team_id);
CREATE INDEX IF NOT EXISTS idx_team_members_player ON public.team_members(player_id);
CREATE INDEX IF NOT EXISTS idx_team_members_tournament ON public.team_members(tournament_id);
CREATE INDEX IF NOT EXISTS idx_team_requests_registration ON public.team_requests(registration_id);
CREATE INDEX IF NOT EXISTS idx_referees_affiliated_club ON public.referees(affiliated_club_id);
CREATE INDEX IF NOT EXISTS idx_field_unavailability_field ON public.field_unavailability(field_id);
CREATE INDEX IF NOT EXISTS idx_field_unavailability_times ON public.field_unavailability(unavailable_start, unavailable_end);
CREATE INDEX IF NOT EXISTS idx_registrations_seeding ON public.registrations(seeding_rank);
CREATE INDEX IF NOT EXISTS idx_fields_field_type ON public.fields(field_type_id);
CREATE INDEX IF NOT EXISTS idx_referee_preferences_referee ON public.referee_preferences(referee_profile_id);
CREATE INDEX IF NOT EXISTS idx_referee_preferences_tournament ON public.referee_preferences(tournament_id);
CREATE INDEX IF NOT EXISTS idx_tournaments_season_year ON public.tournaments(season_year);
CREATE INDEX IF NOT EXISTS idx_transactions_profile ON public.transactions(profile_id);
CREATE INDEX IF NOT EXISTS idx_transactions_registration ON public.transactions(registration_id);
CREATE INDEX IF NOT EXISTS idx_transactions_tournament ON public.transactions(tournament_id);
CREATE INDEX IF NOT EXISTS idx_transactions_provider_id ON public.transactions(provider_transaction_id);
CREATE INDEX IF NOT EXISTS idx_transactions_fee_config ON public.transactions(fee_config_id);
CREATE INDEX IF NOT EXISTS idx_device_tokens_profile ON public.device_tokens(profile_id);
CREATE INDEX IF NOT EXISTS idx_device_tokens_active_os ON public.device_tokens(is_active, device_os);
CREATE INDEX IF NOT EXISTS idx_audit_log_timestamp ON public.audit_log(timestamp);
CREATE INDEX IF NOT EXISTS idx_audit_log_user ON public.audit_log(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_log_table_record ON public.audit_log(table_name, record_id);
CREATE INDEX IF NOT EXISTS idx_audit_log_action ON public.audit_log(action);
CREATE INDEX IF NOT EXISTS idx_audit_log_table ON public.audit_log(table_name);
CREATE INDEX IF NOT EXISTS idx_webhook_events_provider_type ON public.webhook_events(provider, event_type);
CREATE INDEX IF NOT EXISTS idx_webhook_events_status ON public.webhook_events(status);
CREATE INDEX IF NOT EXISTS idx_webhook_events_event_id ON public.webhook_events(event_id);

---------------------------------------------------------------
-- PHASE 8: Functions / Views / Procedures
---------------------------------------------------------------

-- Function: Calculate Team Performance (Finalized Logic)
CREATE OR REPLACE FUNCTION public.calculate_team_performance(p_tournament_id uuid)
RETURNS TABLE (
    returned_team_id uuid,
    team_name text,
    team_age_group text,
    team_group text,
    group_id uuid,
    played int,
    wins int,
    losses int,
    draws int,
    points int,
    goals_for int,
    goals_against int,
    goal_difference int,
    capped_gd int,
    shutouts int,
    h2h_played int,
    h2h_points int,
    h2h_goals_for int,
    h2h_goals_against int,
    h2h_goal_difference int,
    h2h_capped_gd int,
    h2h_shutouts int,
    is_tied boolean,
    tie_count int,
    group_position int
)
LANGUAGE plpgsql STABLE SECURITY DEFINER AS $$
DECLARE
    POINTS_WIN INT := 3;
    POINTS_DRAW INT := 1;
    POINTS_LOSS INT := 0;
    FORFEIT_SCORE_WINNER INT := 3;
    FORFEIT_SCORE_LOSER INT := 0;
    MAX_GD_PER_GAME INT := 5;
BEGIN
    RETURN QUERY
    WITH RelevantTeams AS (
        SELECT t.id, t.name, tg.id AS group_id, tg.name AS group_name, tag.name AS age_group_name
        FROM public.teams t
        JOIN public.registrations r ON t.id = r.team_id
        JOIN public.tournament_age_groups tag ON r.tournament_age_group_id = tag.id
        JOIN public.tournament_groups tg ON r.assigned_group_id = tg.id
        WHERE tag.tournament_id = p_tournament_id
          AND r.assigned_group_id IS NOT NULL
    ),
    ProcessedMatches AS (
        SELECT m.id AS match_id, m.group_id, m.status, m.team1_id, m.team2_id, m.forfeit_winner_id,
               CASE
                 WHEN m.status = 'forfeited_team2' THEN FORFEIT_SCORE_WINNER
                 WHEN m.status = 'forfeited_team1' THEN FORFEIT_SCORE_LOSER
                 WHEN m.status = 'completed' THEN COALESCE(m.team1_score, 0)
                 ELSE 0
               END AS score1,
               CASE
                 WHEN m.status = 'forfeited_team1' THEN FORFEIT_SCORE_WINNER
                 WHEN m.status = 'forfeited_team2' THEN FORFEIT_SCORE_LOSER
                 WHEN m.status = 'completed' THEN COALESCE(m.team2_score, 0)
                 ELSE 0
               END AS score2
        FROM public.matches m
        WHERE m.tournament_id = p_tournament_id
          AND m.round_name = 'group'
          AND m.group_id IS NOT NULL
          AND m.status IN ('completed', 'forfeited_team1', 'forfeited_team2')
    ),
    TeamOutcomes AS (
        SELECT rt.id AS team_id, pm.match_id, pm.group_id, pm.status,
               CASE WHEN rt.id = pm.team1_id THEN pm.team1_id ELSE pm.team2_id END AS current_team_id,
               CASE WHEN rt.id = pm.team1_id THEN pm.team2_id ELSE pm.team1_id END AS opponent_team_id,
               CASE WHEN rt.id = pm.team1_id THEN pm.score1 ELSE pm.score2 END AS gf,
               CASE WHEN rt.id = pm.team1_id THEN pm.score2 ELSE pm.score1 END AS ga,
               CASE
                 WHEN rt.id = pm.forfeit_winner_id THEN 'W'
                 WHEN (rt.id = pm.team1_id AND pm.status = 'forfeited_team1')
                      OR (rt.id = pm.team2_id AND pm.status = 'forfeited_team2') THEN 'L'
                 WHEN pm.status = 'completed' AND ((rt.id = pm.team1_id AND pm.score1 > pm.score2)
                                                   OR (rt.id = pm.team2_id AND pm.score2 > pm.score1)) THEN 'W'
                 WHEN pm.status = 'completed' AND pm.score1 = pm.score2 THEN 'D'
                 WHEN pm.status = 'completed' AND ((rt.id = pm.team1_id AND pm.score1 < pm.score2)
                                                   OR (rt.id = pm.team2_id AND pm.score2 < pm.score1)) THEN 'L'
                 ELSE NULL
               END AS outcome
        FROM RelevantTeams rt
        JOIN ProcessedMatches pm ON rt.group_id = pm.group_id AND (rt.id = pm.team1_id OR rt.id = pm.team2_id)
    ),
    AggregatedStats AS (
        SELECT team_id, group_id,
               COUNT(match_id) AS played,
               SUM(CASE WHEN outcome = 'W' THEN 1 ELSE 0 END) AS wins,
               SUM(CASE WHEN outcome = 'L' THEN 1 ELSE 0 END) AS losses,
               SUM(CASE WHEN outcome = 'D' THEN 1 ELSE 0 END) AS draws,
               SUM(CASE WHEN outcome = 'W' THEN POINTS_WIN WHEN outcome = 'D' THEN POINTS_DRAW ELSE POINTS_LOSS END) AS points,
               SUM(gf) AS goals_for,
               SUM(ga) AS goals_against,
               SUM(gf - ga) AS goal_difference,
               SUM(LEAST(GREATEST(gf - ga, -MAX_GD_PER_GAME), MAX_GD_PER_GAME)) AS capped_gd,
               SUM(CASE WHEN outcome IN ('W','D') AND ga = 0 THEN 1 ELSE 0 END) AS shutouts
        FROM TeamOutcomes
        GROUP BY team_id, group_id
    ),
    IdentifyTies AS (
        SELECT team_id, group_id, points,
               COUNT(*) OVER (PARTITION BY group_id, points) AS tie_count
        FROM AggregatedStats
        WHERE points IS NOT NULL
    ),
    HeadToHeadOutcomes AS (
        SELECT tmo.*
        FROM TeamOutcomes tmo
        JOIN IdentifyTies it1 ON tmo.current_team_id = it1.team_id AND tmo.group_id = it1.group_id AND it1.tie_count > 1
        JOIN IdentifyTies it2 ON tmo.opponent_team_id = it2.team_id AND tmo.group_id = it2.group_id AND it1.points = it2.points
    ),
    HeadToHeadStats AS (
        SELECT h2h.team_id, h2h.group_id,
               COUNT(h2h.match_id) AS h2h_played,
               SUM(CASE WHEN h2h.outcome = 'W' THEN POINTS_WIN WHEN h2h.outcome = 'D' THEN POINTS_DRAW ELSE POINTS_LOSS END) AS h2h_points,
               SUM(h2h.gf) AS h2h_goals_for,
               SUM(h2h.ga) AS h2h_goals_against,
               SUM(h2h.gf - h2h.ga) AS h2h_goal_difference,
               SUM(LEAST(GREATEST(h2h.gf - h2h.ga, -MAX_GD_PER_GAME), MAX_GD_PER_GAME)) AS h2h_capped_gd,
               SUM(CASE WHEN h2h.outcome IN ('W','D') AND h2h.ga = 0 THEN 1 ELSE 0 END) AS h2h_shutouts
        FROM HeadToHeadOutcomes h2h
        GROUP BY h2h.team_id, h2h.group_id
    ),
    FinalStandingsPreRank AS (
        SELECT rt.id AS returned_team_id,
               rt.name AS team_name,
               rt.age_group_name AS team_age_group,
               rt.group_name AS team_group,
               rt.group_id,
               COALESCE(agg.played, 0) AS played,
               COALESCE(agg.wins, 0) AS wins,
               COALESCE(agg.losses, 0) AS losses,
               COALESCE(agg.draws, 0) AS draws,
               COALESCE(agg.points, 0) AS points,
               COALESCE(agg.goals_for, 0) AS goals_for,
               COALESCE(agg.goals_against, 0) AS goals_against,
               COALESCE(agg.goal_difference, 0) AS goal_difference,
               COALESCE(agg.capped_gd, 0) AS capped_gd,
               COALESCE(agg.shutouts, 0) AS shutouts,
               COALESCE(h2h.h2h_played, 0) AS h2h_played,
               COALESCE(h2h.h2h_points, -1) AS h2h_points,
               COALESCE(h2h.h2h_goals_for, 0) AS h2h_goals_for,
               COALESCE(h2h.h2h_goals_against, 999) AS h2h_goals_against,
               COALESCE(h2h.h2h_goal_difference, -999) AS h2h_goal_difference,
               COALESCE(h2h.h2h_capped_gd, -999) AS h2h_capped_gd,
               COALESCE(h2h.h2h_shutouts, -1) AS h2h_shutouts,
               (it.tie_count > 1) AS is_tied,
               COALESCE(it.tie_count, 1) AS tie_count
        FROM RelevantTeams rt
        LEFT JOIN AggregatedStats agg ON rt.id = agg.team_id AND rt.group_id = agg.group_id
        LEFT JOIN IdentifyTies it ON rt.id = it.team_id AND rt.group_id = it.group_id
        LEFT JOIN HeadToHeadStats h2h ON rt.id = h2h.team_id AND rt.group_id = h2h.group_id
    )
    SELECT fspr.*, RANK() OVER (
        PARTITION BY fspr.group_id
        ORDER BY fspr.points DESC,
                 CASE WHEN fspr.tie_count > 1 THEN fspr.h2h_points ELSE -1 END DESC,
                 fspr.wins DESC,
                 fspr.capped_gd DESC,
                 fspr.goals_against ASC,
                 fspr.shutouts DESC,
                 fspr.goals_for DESC,
                 fspr.team_name ASC
    )::int AS group_position
    FROM FinalStandingsPreRank fspr;
END;
$$;

-- Procedure: Refresh Standings
CREATE OR REPLACE PROCEDURE public.refresh_standings(p_tournament_id uuid)
LANGUAGE plpgsql SECURITY DEFINER AS $$
DECLARE row RECORD;
BEGIN
    RAISE LOG '[%] Refreshing standings for tournament: %', clock_timestamp(), p_tournament_id::text;
    FOR row IN SELECT * FROM public.calculate_team_performance(p_tournament_id)
    LOOP
        INSERT INTO public.standings (
            id, tournament_id, team_id, group_id, name, age_group, group_name,
            group_position, played, wins, losses, draws, points, goals_for,
            goals_against, goal_difference, capped_goal_difference, shutouts, updated_at,
            last_manual_override_by, last_manual_override_at, override_notes, previous_override_details
        )
        VALUES (
            gen_random_uuid(), p_tournament_id, row.returned_team_id, row.group_id,
            row.team_name, row.team_age_group, row.team_group, row.group_position,
            row.played, row.wins, row.losses, row.draws, row.points, row.goals_for,
            row.goals_against, row.goal_difference, row.capped_gd, row.shutouts, now(),
            NULL, NULL, NULL, NULL
        )
        ON CONFLICT (tournament_id, group_id, team_id)
        DO UPDATE SET
            name = EXCLUDED.name,
            age_group = EXCLUDED.age_group,
            group_name = EXCLUDED.group_name,
            group_position = EXCLUDED.group_position,
            played = EXCLUDED.played,
            wins = EXCLUDED.wins,
            losses = EXCLUDED.losses,
            draws = EXCLUDED.draws,
            points = EXCLUDED.points,
            goals_for = EXCLUDED.goals_for,
            goals_against = EXCLUDED.goals_against,
            goal_difference = EXCLUDED.goal_difference,
            capped_goal_difference = EXCLUDED.capped_goal_difference,
            shutouts = EXCLUDED.shutouts,
            updated_at = now(),
            last_manual_override_by = standings.last_manual_override_by,
            last_manual_override_at = standings.last_manual_override_at,
            override_notes = standings.override_notes,
            previous_override_details = standings.previous_override_details
        WHERE standings.last_manual_override_at IS NULL;
    END LOOP;
    RAISE LOG '[%] Finished refreshing standings for tournament: %', clock_timestamp(), p_tournament_id::text;
END;
$$;

-- Procedure: Advance Playoff Teams (Finalized)
CREATE OR REPLACE PROCEDURE public.advance_playoff_teams(p_tournament_id UUID)
LANGUAGE plpgsql SECURITY DEFINER AS $$
DECLARE
    v_completed_match RECORD;
    v_target_match RECORD;
    v_winner_id UUID;
    v_placeholder_text TEXT;
    target_round public.bracket_round;
    v_match_updated BOOLEAN;
    v_advancement_flag_col_exists BOOLEAN;
BEGIN
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns
         WHERE table_schema = 'public'
           AND table_name = 'matches'
           AND column_name = 'advancement_processed'
    ) INTO v_advancement_flag_col_exists;

    RAISE LOG '[%] Checking for playoff advancements in tournament: %', clock_timestamp(), p_tournament_id::text;

    FOR v_completed_match IN
        SELECT m.id, m.round_name, m.match_number, m.winner_id, m.tournament_age_group_id
        FROM public.matches m
        WHERE m.tournament_id = p_tournament_id
          AND m.status = 'completed'
          AND m.winner_id IS NOT NULL
          AND m.round_name IN ('quarterfinal', 'semifinal')
          AND (
              NOT v_advancement_flag_col_exists
              OR NOT COALESCE((m.previous_override_details->>'advancement_processed')::boolean, FALSE)
          )
    LOOP
        v_winner_id := v_completed_match.winner_id;
        v_match_updated := FALSE;
        target_round := NULL;
        v_placeholder_text := NULL;

        CASE v_completed_match.round_name
            WHEN 'quarterfinal' THEN
                target_round := 'semifinal';
                v_placeholder_text := 'Winner QF' || v_completed_match.match_number::text;
            WHEN 'semifinal' THEN
                target_round := 'final';
                v_placeholder_text := 'Winner SF' || v_completed_match.match_number::text;
            ELSE
                RAISE LOG '[%] Match % (Round: %) completed but not a standard feeder.', clock_timestamp(), v_completed_match.id::text, v_completed_match.round_name::text;
        END CASE;

        IF target_round IS NOT NULL AND v_placeholder_text IS NOT NULL THEN
            SELECT * INTO v_target_match
            FROM public.matches t
            WHERE t.tournament_id = p_tournament_id
              AND t.tournament_age_group_id = v_completed_match.tournament_age_group_id
              AND t.round_name = target_round
              AND (t.placeholder_team1_text = v_placeholder_text OR t.placeholder_team2_text = v_placeholder_text)
              AND t.status = 'scheduled'
            FOR UPDATE OF t
            LIMIT 1;

            IF FOUND THEN
                RAISE LOG '[%] Attempting advance: Winner % from % % to % % (Target Match ID: %)', clock_timestamp(),
                    COALESCE(v_winner_id::text,'NULL'),
                    COALESCE(v_completed_match.round_name::text,'N/A'),
                    COALESCE(v_completed_match.match_number::text,'N/A'),
                    COALESCE(target_round::text,'N/A'),
                    COALESCE(v_target_match.match_number::text,'N/A'),
                    COALESCE(v_target_match.id::text,'N/A');

                IF v_target_match.placeholder_team1_text = v_placeholder_text AND v_target_match.team1_id IS NULL THEN
                    UPDATE public.matches
                    SET team1_id = v_winner_id,
                        placeholder_team1_text = NULL,
                        updated_at = now()
                    WHERE id = v_target_match.id;
                    v_match_updated := TRUE;
                ELSIF v_target_match.placeholder_team2_text = v_placeholder_text AND v_target_match.team2_id IS NULL THEN
                    UPDATE public.matches
                    SET team2_id = v_winner_id,
                        placeholder_team2_text = NULL,
                        updated_at = now()
                    WHERE id = v_target_match.id;
                    v_match_updated := TRUE;
                ELSE
                    RAISE WARNING '[%] Target % slot for placeholder % already filled or mismatch for match %.', clock_timestamp(),
                        COALESCE(target_round::text,'N/A'),
                        COALESCE(v_placeholder_text,'N/A'),
                        COALESCE(v_target_match.id::text,'N/A');
                END IF;
            ELSE
                RAISE LOG '[%] No target match found for placeholder "%" in round "%" for age group %.', clock_timestamp(),
                    COALESCE(v_placeholder_text,'N/A'),
                    COALESCE(target_round::text,'N/A'),
                    COALESCE(v_completed_match.tournament_age_group_id::text,'N/A');
            END IF;
        END IF;
    END LOOP;

    RAISE LOG '[%] Finished checking playoff advancements for tournament: %', clock_timestamp(), p_tournament_id::text;
END;
$$;

---------------------------------------------------------------
-- PHASE 9: RLS & Trigger Setup (Optional at this stage)
---------------------------------------------------------------
-- For now, triggers are not enabled. After testing, you can enable them by uncommenting the following lines:

-- CREATE TRIGGER audit_trigger_profiles
-- AFTER INSERT OR UPDATE OR DELETE ON public.profiles
-- FOR EACH ROW EXECUTE FUNCTION public.log_audit_trail();
--
-- CREATE TRIGGER audit_trigger_clubs
-- AFTER INSERT OR UPDATE OR DELETE ON public.clubs
-- FOR EACH ROW EXECUTE FUNCTION public.log_audit_trail();
--
-- CREATE TRIGGER audit_trigger_teams
-- AFTER INSERT OR UPDATE OR DELETE ON public.teams
-- FOR EACH ROW EXECUTE FUNCTION public.log_audit_trail();
--
-- CREATE TRIGGER audit_trigger_tournaments
-- AFTER INSERT OR UPDATE OR DELETE ON public.tournaments
-- FOR EACH ROW EXECUTE FUNCTION public.log_audit_trail();
--
-- CREATE TRIGGER audit_trigger_matches
-- AFTER INSERT OR UPDATE OR DELETE ON public.matches
-- FOR EACH ROW EXECUTE FUNCTION public.log_audit_trail();
--
-- CREATE TRIGGER audit_trigger_standings
-- AFTER INSERT OR UPDATE OR DELETE ON public.standings
-- FOR EACH ROW EXECUTE FUNCTION public.log_audit_trail();
--
---------------------------------------------------------------
-- Enable RLS on key tables
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.clubs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.teams ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tournaments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tournament_age_groups ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tournament_groups ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.registrations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.venues ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.fields ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.field_types ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.matches ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.standings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tie_breaker_overrides ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.referees ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.referee_availability ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.referee_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.match_assignments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.team_staff ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.players ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.team_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.team_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.field_unavailability ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.fee_configurations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.device_tokens ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.audit_log ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.webhook_events ENABLE ROW LEVEL SECURITY;

---------------------------------------------------------------
-- PHASE 10: Policy Examples (Simplified)
---------------------------------------------------------------
DROP POLICY IF EXISTS "Users can view own profile" ON public.profiles;
CREATE POLICY "Users can view own profile" ON public.profiles
FOR SELECT
USING (auth.uid() = id);

DROP POLICY IF EXISTS "Users can update own profile" ON public.profiles;
CREATE POLICY "Users can update own profile" ON public.profiles
FOR UPDATE
USING (auth.uid() = id)
WITH CHECK (auth.uid() = id);

DROP POLICY IF EXISTS "Admins can manage all profiles" ON public.profiles;
CREATE POLICY "Admins can manage all profiles" ON public.profiles
FOR ALL
USING (public.is_claims_admin());

DROP POLICY IF EXISTS "Allow public read access" ON public.clubs;
CREATE POLICY "Allow public read access" ON public.clubs
FOR SELECT
USING (true);

DROP POLICY IF EXISTS "Club Admins can manage their club" ON public.clubs;
CREATE POLICY "Club Admins can manage their club" ON public.clubs
FOR ALL
USING (
    id = (SELECT managing_club_id FROM public.profiles WHERE id = auth.uid())
    OR public.is_claims_admin()
);

DROP POLICY IF EXISTS "Allow public read access" ON public.teams;
CREATE POLICY "Allow public read access" ON public.teams
FOR SELECT
USING (true);

DROP POLICY IF EXISTS "Team Staff/Club Admins/Admin can manage relevant teams" ON public.teams;
CREATE POLICY "Team Staff/Club Admins/Admin can manage relevant teams" ON public.teams
FOR ALL
USING (
    id IN (SELECT team_id FROM public.team_staff WHERE profile_id = auth.uid())
    OR club_id = (SELECT managing_club_id FROM public.profiles WHERE id = auth.uid())
    OR public.is_claims_admin()
)
WITH CHECK (
    (club_id IS NULL OR club_id = (SELECT managing_club_id FROM public.profiles WHERE id = auth.uid()) OR public.is_claims_admin())
    AND ((TG_OP = 'INSERT' AND (club_id = (SELECT managing_club_id FROM public.profiles WHERE id = auth.uid()) OR public.is_claims_admin()))
         OR TG_OP <> 'INSERT')
);

---------------------------------------------------------------
---------------------------------------------------------------
-- PHASE X: Additional Tables (Club Directors) - Placed after Clubs and Profiles
---------------------------------------------------------------

-- Club Directors Table (Junction table for club affiliations)
CREATE TABLE IF NOT EXISTS public.club_directors (
    club_id UUID NOT NULL REFERENCES public.clubs(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE, -- Links to profiles.id which is FK to auth.users.id
    status public.director_affiliation_status NOT NULL DEFAULT 'pending',
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    PRIMARY KEY (club_id, user_id)
);

COMMENT ON TABLE public.club_directors IS 'Junction table linking users (e.g., Tournament Directors, Club Admins) to clubs they are affiliated with.';
COMMENT ON COLUMN public.club_directors.status IS 'Status of the affiliation (e.g., pending approval, approved).';

-- Grant permissions for service_role (used by backend)
GRANT SELECT, INSERT, UPDATE, DELETE ON TABLE public.club_directors TO service_role;
GRANT USAGE ON TYPE public.director_affiliation_status TO service_role;


-- END OF SCRIPT
---------------------------------------------------------------

-- Instructions:
-- 1. Run this script as a Supabase admin (via psql or Supabase CLI).
-- 2. Verify that all objects (tables, functions, views, procedures) are created without errors.
-- 3. Confirm that the views (e.g., view_registrations_details) are created without a SECURITY DEFINER clause.
-- 4. Seed your database with valid test data (ensuring that foreign keys referencing auth.users use valid IDs).
-- 5. Test core functions and procedures (e.g., refresh_standings, update_match_score_manual).
-- 6. Once core functionality is verified, uncomment the trigger creation statements in PHASE 9 to enable audit logging.
-- 7. Commit this final script to your Git repository as your production baseline.
