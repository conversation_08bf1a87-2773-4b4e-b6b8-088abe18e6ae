-- RPC to create a new tournament
CREATE OR REPLACE FUNCTION create_tournament(p_tournament_data JSONB)
RETURNS SETOF public.tournaments -- Returns the newly created tournament row
LANGUAGE plpgsql
SECURITY INVOKER -- Or DEFINER if specific permissions are needed
AS $$
DECLARE
    v_new_tournament_id UUID;
    v_auth_user_id UUID := auth.uid(); -- Get the ID of the currently authenticated user (this is auth.users.id)
    -- v_profile_id UUID; -- Not needed if we directly use v_auth_user_id for director_user_id
BEGIN
    -- The profiles.id column is the same as auth.users.id, so v_auth_user_id can be used directly
    -- for director_user_id in the tournaments table, as director_user_id references profiles.id.

    -- IF v_profile_id IS NULL THEN -- This check is implicitly handled by FK constraint if director_user_id must exist in profiles
    --     RAISE EXCEPTION 'User profile not found. Cannot create tournament.';
    -- END IF;

    INSERT INTO public.tournaments (
        name,
        start_date,
        end_date,
        status, -- Restoring status column
        director_user_id, -- Corrected column name, references profiles(id) which is auth.uid()
        managing_club_id, -- Corrected column name
        custom_settings -- Store sport_type or other details here if no direct column
    )
    VALUES (
        p_tournament_data ->> 'name',
        (p_tournament_data ->> 'startDate')::DATE, -- Key from Tournament.toJson()
        (p_tournament_data ->> 'endDate')::DATE,   -- Key from Tournament.toJson()
        (p_tournament_data ->> 'status')::public.tournament_status, -- Restoring status value
        v_auth_user_id, -- Use the auth.uid() directly as it's the PK for profiles
        NULL, -- managing_club_id is NULL for independent director
        jsonb_build_object('sportType', p_tournament_data ->> 'sportType') -- Store sportType in custom_settings
        -- If other fields like min_games_guaranteed, rules_url etc. are part of initial creation, add them here
    ) RETURNING id INTO v_new_tournament_id;

    RETURN QUERY SELECT * FROM public.tournaments WHERE id = v_new_tournament_id;
END;
$$;

COMMENT ON FUNCTION create_tournament(JSONB) IS 'Creates a new tournament, associating it with the calling user (director). For independent directors, club_id is NULL.';
