-- Enable RLS on venues table if not already enabled (should be by full schema)
ALTER TABLE public.venues ENABLE ROW LEVEL SECURITY;

-- Policy: Authenticated users can insert new venues.
DROP POLICY IF EXISTS "Authenticated users can create venues" ON public.venues;
CREATE POLICY "Authenticated users can create venues"
ON public.venues
FOR INSERT
TO authenticated -- Grant to the 'authenticated' role
WITH CHECK (true); -- No specific check other than being authenticated

-- Policy: Allow public read access to venues.
DROP POLICY IF EXISTS "Public can view venues" ON public.venues;
CREATE POLICY "Public can view venues"
ON public.venues
FOR SELECT
USING (true);

-- Policy: <PERSON><PERSON> can manage all venues (update/delete).
DROP POLICY IF EXISTS "<PERSON><PERSON> can manage all venues" ON public.venues;
CREATE POLICY "<PERSON><PERSON> can manage all venues"
ON public.venues
FOR ALL -- Covers SELECT, INSERT, UPDATE, DELETE
USING (public.is_claims_admin())
WITH CHECK (public.is_claims_admin());

-- Note: More granular UPDATE/DELETE policies would require an 'owner_id' or similar
-- column on the venues table, or linking them to a user through another table.
-- For now, only admins can update/delete to prevent users from modifying others' venues.
