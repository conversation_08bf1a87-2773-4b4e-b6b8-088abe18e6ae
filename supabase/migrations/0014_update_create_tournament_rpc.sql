-- Migration: Update create_tournament RPC to support division-centric structure
-- This migration updates the create_tournament function to handle the new tournament data structure

-- R<PERSON> to create a new tournament with division support
CREATE OR REPLACE FUNCTION create_tournament(p_tournament_data JSONB)
RETURNS SETOF public.tournaments -- Returns the newly created tournament row
LANGUAGE plpgsql
SECURITY INVOKER
AS $$
DECLARE
    v_new_tournament_id UUID;
    v_auth_user_id UUID := auth.uid(); -- Get the ID of the currently authenticated user
    v_custom_settings JSONB;
    v_managing_club_id UUID;
BEGIN
    -- Build custom_settings JSONB object for fields that don't have direct columns
    v_custom_settings := jsonb_build_object(
        'sport_type', p_tournament_data ->> 'sport_type',
        'director_name', p_tournament_data ->> 'director_name',
        'director_email', p_tournament_data ->> 'director_email',
        'director_phone', p_tournament_data ->> 'director_phone',
        'tournament_format', p_tournament_data ->> 'tournament_format',
        'description', p_tournament_data ->> 'description',
        'facebook_url', p_tournament_data ->> 'facebook_url',
        'twitter_url', p_tournament_data ->> 'twitter_url',
        'instagram_url', p_tournament_data ->> 'instagram_url',
        'website_url', p_tournament_data ->> 'website_url',
        'max_teams', p_tournament_data ->> 'max_teams',
        'games_per_team', p_tournament_data ->> 'games_per_team',
        'rules', p_tournament_data ->> 'rules',
        'refund_policy', p_tournament_data ->> 'refund_policy',
        'min_roster_size', p_tournament_data ->> 'min_roster_size',
        'max_roster_size', p_tournament_data ->> 'max_roster_size',
        'awards', p_tournament_data ->> 'awards',
        'has_concessions', p_tournament_data ->> 'has_concessions',
        'has_merchandise', p_tournament_data ->> 'has_merchandise',
        'has_medical', p_tournament_data ->> 'has_medical',
        'admission_fee', p_tournament_data ->> 'admission_fee',
        'parking_info', p_tournament_data ->> 'parking_info',
        'spectator_info', p_tournament_data ->> 'spectator_info',
        'secondary_contact_name', p_tournament_data ->> 'secondary_contact_name',
        'secondary_contact_email', p_tournament_data ->> 'secondary_contact_email',
        'secondary_contact_phone', p_tournament_data ->> 'secondary_contact_phone',
        'secondary_contact_role', p_tournament_data ->> 'secondary_contact_role',
        'game_timing_configurations', p_tournament_data -> 'game_timing_configurations'
    );

    -- Handle managing_club_id (can be null for independent directors)
    v_managing_club_id := CASE 
        WHEN p_tournament_data ->> 'managing_club_id' IS NOT NULL 
        THEN (p_tournament_data ->> 'managing_club_id')::UUID 
        ELSE NULL 
    END;

    INSERT INTO public.tournaments (
        name,
        start_date,
        end_date,
        status,
        director_user_id,
        managing_club_id,
        registration_deadline,
        location_city,
        location_state,
        custom_settings,
        -- New division-centric columns
        divisions,
        early_bird_discount,
        early_bird_deadline,
        late_fee_amount,
        late_registration_start_date
    )
    VALUES (
        p_tournament_data ->> 'name',
        (p_tournament_data ->> 'start_date')::DATE,
        (p_tournament_data ->> 'end_date')::DATE,
        (p_tournament_data ->> 'status')::public.tournament_status,
        v_auth_user_id,
        v_managing_club_id,
        (p_tournament_data ->> 'registration_deadline')::TIMESTAMPTZ,
        p_tournament_data ->> 'city',
        p_tournament_data ->> 'state',
        v_custom_settings,
        -- Values for new division-centric columns
        (p_tournament_data ->> 'divisions')::JSONB,
        (p_tournament_data ->> 'early_bird_discount')::NUMERIC,
        (p_tournament_data ->> 'early_bird_deadline')::TIMESTAMPTZ,
        (p_tournament_data ->> 'late_fee_amount')::NUMERIC,
        (p_tournament_data ->> 'late_registration_start_date')::TIMESTAMPTZ
    ) RETURNING id INTO v_new_tournament_id;

    RETURN QUERY SELECT * FROM public.tournaments WHERE id = v_new_tournament_id;
END;
$$;

COMMENT ON FUNCTION create_tournament(JSONB) IS 'Creates a new tournament with division-centric structure, associating it with the calling user (director). Supports both independent and club-affiliated directors.';
