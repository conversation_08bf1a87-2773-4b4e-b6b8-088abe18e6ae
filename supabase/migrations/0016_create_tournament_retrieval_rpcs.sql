-- Migration: Create additional tournament retrieval RPC functions
-- This migration creates the missing RPC functions for tournament retrieval

-- RPC to get all public tournaments (for getAllTournaments method)
CREATE OR REPLACE FUNCTION get_all_tournaments_public()
RETURNS SETOF public.tournaments
LANGUAGE sql
STABLE
SECURITY INVOKER
AS $$
  SELECT 
    id,
    name,
    start_date,
    end_date,
    director_user_id,
    managing_club_id,
    status,
    registration_deadline,
    location_city,
    location_state,
    rules_url,
    rules_text,
    website,
    logo_url,
    min_games_guaranteed,
    allow_guest_players,
    season_year,
    tournament_creation_fee,
    creation_payment_status,
    creation_payment_intent_id,
    custom_settings,
    -- Include new division-centric columns
    divisions,
    early_bird_discount,
    early_bird_deadline,
    late_fee_amount,
    late_registration_start_date,
    created_at,
    updated_at
  FROM public.tournaments
  WHERE status IN ('registration_open', 'live', 'completed')
  ORDER BY start_date ASC, name ASC;
$$;

-- RPC to get tournament details by ID
CREATE OR REPLACE FUNCTION get_tournament_details(p_tournament_id UUID)
RETURNS SETOF public.tournaments
LANGUAGE sql
STABLE
SECURITY INVOKER
AS $$
  SELECT 
    id,
    name,
    start_date,
    end_date,
    director_user_id,
    managing_club_id,
    status,
    registration_deadline,
    location_city,
    location_state,
    rules_url,
    rules_text,
    website,
    logo_url,
    min_games_guaranteed,
    allow_guest_players,
    season_year,
    tournament_creation_fee,
    creation_payment_status,
    creation_payment_intent_id,
    custom_settings,
    -- Include new division-centric columns
    divisions,
    early_bird_discount,
    early_bird_deadline,
    late_fee_amount,
    late_registration_start_date,
    created_at,
    updated_at
  FROM public.tournaments
  WHERE id = p_tournament_id;
$$;

-- Add comments for documentation
COMMENT ON FUNCTION get_all_tournaments_public() IS 'Retrieves all public tournaments (registration_open, live, completed status) including division-centric columns.';
COMMENT ON FUNCTION get_tournament_details(UUID) IS 'Retrieves tournament details by ID including division-centric columns.';
