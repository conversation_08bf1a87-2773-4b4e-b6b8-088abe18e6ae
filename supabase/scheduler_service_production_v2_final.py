# scheduler_service_production_v2_final.py
# Finalized Conceptual Python Service for Match & Referee Scheduling Logic
# Includes refined check_constraints function (with Team Rest Time)
# and updated generate_match_schedule to handle team_schedules.
# Added initial implementation for seed_playoff_bracket with real get_ranked_team.
# Placeholder logic for referee assignment remains.

import os
import datetime
import json
import logging
from collections import defaultdict, Counter
from typing import List, Dict, Any, Optional, Tuple

# Use try-except for Supabase import to handle potential runtime issues gracefully
try:
    from supabase import create_client, Client, PostgrestAPIResponse
except ImportError:
    logging.error("Supabase library not found. Please install it: pip install supabase")
    # Define dummy types if import fails to prevent NameErrors later, though functionality will be broken
    class PostgrestAPIResponse: pass
    class Client: pass
    create_client = None # type: ignore

from dotenv import load_dotenv
import pytz # Keep for explicit timezone handling

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - [%(funcName)s] %(message)s')

# --- Supabase Connection (Initialized globally for the module) ---
# Load environment variables ONCE when the module is imported
load_dotenv()
SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_KEY = os.getenv("SUPABASE_SERVICE_KEY")

supabase: Optional[Client] = None
if not SUPABASE_URL or not SUPABASE_KEY:
    logging.error("CRITICAL: Supabase URL or Service Key environment variables not set in scheduler service.")
elif create_client is None:
     logging.error("CRITICAL: Supabase library failed to import. Cannot initialize client.")
else:
    try:
        supabase = create_client(SUPABASE_URL, SUPABASE_KEY)
        logging.info("Supabase client initialized successfully in scheduler service.")
    except Exception as e:
        logging.error(f"Failed to initialize Supabase client in scheduler service: {e}")
        supabase = None


# --- Configuration & Constants ---
DEFAULT_TRAVEL_TIME_MINUTES = 30 # Kept for potential future use
DEFAULT_GAME_DURATION = 90
DEFAULT_TRANSITION_MINUTES = 15
DEFAULT_HALFTIME_MINUTES = 5
DEFAULT_OPERATIONAL_START = datetime.time(7, 0)
DEFAULT_OPERATIONAL_END = datetime.time(22, 0)
SLOT_GENERATION_INTERVAL_MINUTES = 15
DEFAULT_MIN_REST_MINUTES = 60 # Default minimum rest between games for a team

DEFAULT_FORMAT_TO_FIELD_TYPE_MAP = {
    '4v4': ['4v4 Mini'],
    '7v7': ['7v7', '9v9'],
    '9v9': ['9v9', '11v11 Full-Size'],
    '11v11': ['11v11 Full-Size']
}

# --- Helper Functions ---
def parse_time(time_str: Optional[str], default: Optional[datetime.time] = None) -> Optional[datetime.time]:
    """Safely parse HH:MM:SS or HH:MM strings, return default if invalid/None."""
    if not time_str: return default
    try:
        parts = time_str.split(':')
        if len(parts) >= 2:
            hour, minute = int(parts[0]), int(parts[1])
            second = int(parts[2]) if len(parts) > 2 else 0
            # Handle 24:00 edge case gracefully, mapping it to end-of-day for comparison
            if hour == 24 and minute == 0 and second == 0:
                 # Represent as the very end of the day for comparisons
                 return datetime.time(23, 59, 59, 999999)
            if hour >= 24 or minute >= 60 or second >= 60:
                 raise ValueError("Invalid time component")
            # Include microseconds if needed, or keep as 0
            return datetime.time(hour, minute, second) # Microseconds default to 0
        # If splitting doesn't yield at least 2 parts, it's invalid
        logging.warning(f"Could not parse time string (invalid format): {time_str}, using default.")
        return default
    except (ValueError, TypeError): # Catch potential int conversion errors or index errors
        logging.warning(f"Could not parse time string (value error): {time_str}, using default.")
        return default

def get_utc_datetime(date_part: datetime.date, time_part: Optional[datetime.time]) -> Optional[datetime.datetime]:
    """Combine date and time into a timezone-aware UTC datetime object."""
    if date_part and time_part:
        # Assume date/time components are naive, combine and make UTC aware
        try:
            # Handle the case where time_part might be end-of-day representation
            if time_part == datetime.time(23, 59, 59, 999999):
                 # Combine with date, then add almost a full day, subtract a microsecond
                 base_dt = datetime.datetime.combine(date_part, datetime.time(0,0))
                 return (base_dt + datetime.timedelta(days=1) - datetime.timedelta(microseconds=1)).replace(tzinfo=datetime.timezone.utc)
            else:
                 return datetime.datetime.combine(date_part, time_part).replace(tzinfo=datetime.timezone.utc)
        except ValueError as e: # Handles potential issues with date/time combination
             logging.error(f"Error combining date {date_part} and time {time_part}: {e}")
             return None
    return None

def parse_iso_datetime(dt_str: Optional[str]) -> Optional[datetime.datetime]:
    """Parse ISO 8601 string with timezone into aware datetime object."""
    if not dt_str: return None
    try:
        # Handle Z or +HH:MM offset by ensuring offset is present for fromisoformat
        if dt_str.endswith('Z'):
            dt_str = dt_str[:-1] + '+00:00'
        # Handle cases where offset might be missing (assume UTC, adjust if needed)
        # Check for T separating date and time
        if 'T' in dt_str and '+' not in dt_str and '-' not in dt_str[dt_str.find('T'):]:
             # Check if microseconds are present
             if '.' in dt_str:
                  base, micro = dt_str.split('.')
                  micro = micro[:6] # Truncate microseconds to 6 digits max
                  dt_str = f"{base}.{micro}+00:00"
             else:
                  dt_str += '+00:00'
        dt = datetime.datetime.fromisoformat(dt_str)
        # If timezone is naive, assume UTC
        if dt.tzinfo is None:
            dt = dt.replace(tzinfo=datetime.timezone.utc)
        return dt.astimezone(datetime.timezone.utc) # Ensure it's UTC
    except (ValueError, TypeError, AttributeError): # Added AttributeError for safety
        logging.warning(f"Could not parse ISO datetime string: {dt_str}")
        return None

def parse_custom_settings(settings_jsonb: Optional[Any]) -> Dict[str, Any]:
    """Safely parse JSONB settings, returning an empty dict on failure."""
    if isinstance(settings_jsonb, dict): return settings_jsonb
    if isinstance(settings_jsonb, str):
        try: return json.loads(settings_jsonb)
        except json.JSONDecodeError: logging.warning(f"Could not decode custom_settings JSON: {settings_jsonb}")
    return {}

def check_supabase_error(response: PostgrestAPIResponse, operation_desc: str):
    """Checks for errors in Supabase response and logs/raises."""
    # Adapt for Supabase Python client v1 / v2 error handling if needed
    # Check if the response object itself indicates an error status (like non-2xx)
    status_code = getattr(response, 'status_code', None) # Use getattr for safety
    error_attr = getattr(response, 'error', None)

    # Check based on Supabase v2 library structure (has status_code and data)
    # Ensure status_code is treated as an integer if it exists
    if status_code is not None:
        try:
            status_int = int(status_code)
            if status_int < 200 or status_int >= 300:
                error_msg_from_data = getattr(response, 'data', {}).get('message', 'No error message in data.') if isinstance(getattr(response, 'data', None), dict) else str(getattr(response, 'data', 'No data field.'))
                logging.error(f"Supabase non-success status during {operation_desc}: {status_int} - {error_msg_from_data}")
                raise Exception(f"Supabase error during {operation_desc}: Status {status_int} - {error_msg_from_data}")
        except (ValueError, TypeError):
             logging.error(f"Supabase response status_code ('{status_code}') is not a valid integer during {operation_desc}.")
             # Optionally raise an exception here depending on how critical this is
             # raise Exception(f"Invalid status_code format during {operation_desc}: {status_code}")

    # Check for explicit error attribute
    elif error_attr:
        error_details = f"{error_attr.message} (Code: {error_attr.code}, Hint: {error_attr.hint})" if hasattr(error_attr, 'message') else str(error_attr)
        logging.error(f"Supabase error attribute during {operation_desc}: {error_details}")
        raise Exception(f"Supabase error during {operation_desc}: {error_details}")


# --- Data Fetching ---
def fetch_scheduling_data(tournament_id: str) -> Dict[str, Any]:
    """Fetches all necessary data from Supabase for match scheduling, with error checking."""
    if not supabase: raise Exception("Supabase client not initialized in scheduler service.")
    logging.info(f"Fetching scheduling data for tournament: {tournament_id}")
    data: Dict[str, Any] = {'errors': [], 'warnings': []}
    try:
        # Tournament
        t_resp = supabase.table("tournaments").select("*, managing_club_id, custom_settings, start_date, end_date, season_year").eq("id", tournament_id).maybe_single().execute()
        check_supabase_error(t_resp, "fetching tournament")
        if not t_resp.data: data['errors'].append("Tournament not found"); return data
        data['tournament'] = t_resp.data
        data['tournament']['custom_settings'] = parse_custom_settings(data['tournament'].get('custom_settings'))

        # Age Groups
        ag_resp = supabase.table("tournament_age_groups").select("*, custom_settings").eq("tournament_id", tournament_id).execute()
        check_supabase_error(ag_resp, "fetching age groups")
        data['age_groups'] = {ag['id']: ag for ag in ag_resp.data}
        for ag_id in data['age_groups']: data['age_groups'][ag_id]['custom_settings'] = parse_custom_settings(data['age_groups'][ag_id].get('custom_settings'))

        # Registrations (using view)
        reg_resp = supabase.table("view_registrations_details").select("*").eq("tournament_id", tournament_id).execute()
        check_supabase_error(reg_resp, "fetching registrations")
        data['registrations'] = reg_resp.data
        team_ids = list(set([reg['team_id'] for reg in reg_resp.data if reg.get('team_id')]))
        registration_ids = list(set([reg['registration_id'] for reg in reg_resp.data if reg.get('registration_id')]))

        # Team Requests (Linked to registrations)
        data['team_requests'] = defaultdict(list)
        if registration_ids:
             # Fetch requests linked to these registrations
             # Need team_id associated with registration_id to map correctly
             req_resp = supabase.table("team_requests").select("*, registrations!inner(team_id, id)").in_("registration_id", registration_ids).execute()
             check_supabase_error(req_resp, "fetching team requests")
             for req in req_resp.data:
                 # Extract team_id from the nested registrations data
                 reg_data = req.get('registrations')
                 if reg_data and reg_data.get('team_id'):
                     data['team_requests'][reg_data['team_id']].append(req)
                 else:
                     logging.warning(f"Team request {req.get('id')} is missing registration link or team_id in link.")

        # Team Staff
        data['team_staff'] = defaultdict(list)
        if team_ids:
            staff_resp = supabase.table("team_staff").select("team_id, profile_id").in_("team_id", team_ids).execute()
            check_supabase_error(staff_resp, "fetching team staff")
            for staff in staff_resp.data: data['team_staff'][staff['team_id']].append(staff['profile_id'])

        # Fields, Venues, Types
        f_resp = supabase.table("fields").select("*, field_type:field_type_id(id, name), venue:venue_id(id, name, operational_start_time, operational_end_time)").execute()
        check_supabase_error(f_resp, "fetching fields/venues/types")
        data['fields'] = {}
        data['venues'] = {}
        for f in f_resp.data:
            venue_data = f.get('venue'); field_type_data = f.get('field_type')
            if not venue_data: logging.warning(f"Field {f.get('id')} missing venue link, skipping."); continue
            if venue_data['id'] not in data['venues']: data['venues'][venue_data['id']] = venue_data
            field_data = f.copy()
            # Ensure 'venue' and 'field_type' keys are removed or handled if they are complex objects
            if 'venue' in field_data: del field_data['venue']
            if 'field_type' in field_data: del field_data['field_type']

            field_data['venue_id'] = venue_data['id']
            # Get venue operational times
            field_data['venue_start_time'] = parse_time(venue_data.get('operational_start_time'), DEFAULT_OPERATIONAL_START)
            field_data['venue_end_time'] = parse_time(venue_data.get('operational_end_time'), DEFAULT_OPERATIONAL_END)
            # Get field-specific overrides
            field_data['field_start_time'] = parse_time(f.get('operational_start_time')) # Null if not set
            field_data['field_end_time'] = parse_time(f.get('operational_end_time')) # Null if not set
            # Get field type info
            field_data['field_type_id'] = field_type_data['id'] if field_type_data else None
            field_data['field_type_name'] = field_type_data['name'] if field_type_data else None
            data['fields'][f['id']] = field_data

        # Field Unavailability
        unavail_resp = supabase.table("field_unavailability").select("*").eq("tournament_id", tournament_id).execute() # Filter by tournament
        check_supabase_error(unavail_resp, "fetching field unavailability")
        data['field_unavailability'] = defaultdict(list)
        for unavail in unavail_resp.data:
             field_id_for_unavail = unavail.get('field_id')
             if field_id_for_unavail:
                  data['field_unavailability'][field_id_for_unavail].append(unavail)
             else:
                  logging.warning(f"Field unavailability record {unavail.get('id')} missing field_id.")

        logging.info(f"Successfully fetched data for tournament {tournament_id}.")
    except Exception as e:
        logging.error(f"!!! Critical error fetching scheduling data for {tournament_id}: {e}", exc_info=True)
        data['errors'].append(f"Critical fetch error: {e}")
    return data


# --- Time Slot Generation ---
def generate_time_slots(scheduling_data: Dict[str, Any]) -> Dict[str, List[datetime.datetime]]:
    """Generates potential UTC game START time slots for each field."""
    slots = defaultdict(list)
    tournament = scheduling_data.get('tournament')
    if not tournament or not scheduling_data.get('fields'):
        logging.warning("Cannot generate time slots: Missing tournament or fields data.")
        return slots

    try:
        start_date = datetime.date.fromisoformat(tournament['start_date'])
        end_date = datetime.date.fromisoformat(tournament['end_date'])
        slot_interval = datetime.timedelta(minutes=SLOT_GENERATION_INTERVAL_MINUTES)
    except (ValueError, TypeError, KeyError) as e:
        logging.error(f"Invalid tournament date format or missing dates: {e}")
        return slots

    all_fields = scheduling_data.get('fields', {})
    all_unavailability = scheduling_data.get('field_unavailability', {})

    for field_id, field in all_fields.items():
        # Determine operational times: Field override > Venue time > Default
        op_start_time = field.get('field_start_time') or field.get('venue_start_time') or DEFAULT_OPERATIONAL_START
        op_end_time = field.get('field_end_time') or field.get('venue_end_time') or DEFAULT_OPERATIONAL_END

        # Parse field-specific unavailability blocks
        parsed_unavailability = []
        for unavail in all_unavailability.get(field_id, []):
            u_start = parse_iso_datetime(unavail.get('unavailable_start'))
            u_end = parse_iso_datetime(unavail.get('unavailable_end'))
            if u_start and u_end and u_start < u_end:
                parsed_unavailability.append((u_start, u_end))
            else:
                logging.warning(f"Invalid unavailability block for field {field_id}: Start={unavail.get('unavailable_start')}, End={unavail.get('unavailable_end')}")

        current_date = start_date
        while current_date <= end_date:
            day_start_dt = get_utc_datetime(current_date, op_start_time)
            day_end_dt = get_utc_datetime(current_date, op_end_time)

            if not day_start_dt or not day_end_dt or day_start_dt >= day_end_dt:
                # Skip if operational times are invalid for the day
                current_date += datetime.timedelta(days=1)
                continue

            current_slot_start = day_start_dt
            while current_slot_start < day_end_dt:
                # Check this specific slot interval start time against unavailability blocks
                is_available = True
                # The interval we check is [current_slot_start, current_slot_start + slot_interval)
                # Any unavailability block overlapping this interval makes the start time invalid
                potential_interval_end = current_slot_start + slot_interval
                for unavail_start, unavail_end in parsed_unavailability:
                    # Check for overlap: interval starts before unavailability ends AND interval ends after unavailability starts
                    if current_slot_start < unavail_end and potential_interval_end > unavail_start:
                        is_available = False
                        break # No need to check other blocks for this slot

                if is_available:
                    slots[field_id].append(current_slot_start)

                current_slot_start += slot_interval

            current_date += datetime.timedelta(days=1)

    for field_id in slots:
        slots[field_id].sort() # Ensure slots are chronological per field

    logging.info(f"Generated {sum(len(s) for s in slots.values())} potential start slots across {len(slots)} fields.")
    return slots


# Global counter for constraint checks
constraint_check_counter = 0

# --- *** REFINED Constraint Checking Function (Includes Team Rest Time) *** ---
def check_constraints(
    match_pairing: Dict[str, Any],
    potential_start_dt: datetime.datetime,
    field: Dict[str, Any],
    scheduling_data: Dict[str, Any],
    coach_schedules: Dict[str, List[Tuple[datetime.datetime, datetime.datetime, str]]],
    field_schedules: Dict[str, List[Tuple[datetime.datetime, datetime.datetime]]],
    team_schedules: Dict[str, List[Tuple[datetime.datetime, datetime.datetime]]] # <<< NEW ARGUMENT
) -> Tuple[bool, Optional[datetime.timedelta], str]:
    """
    Checks scheduling constraints for a match pairing at a proposed start time on a given field.
    Includes check for minimum team rest time.

    Args:
        match_pairing: Dict containing team1_id, team2_id, age_group_id.
        potential_start_dt: The proposed UTC start time for the match.
        field: Dict containing details of the proposed field.
        scheduling_data: Dict containing tournament, age_groups, settings, requests etc.
        coach_schedules: Dict mapping coach_id to list of (start, end, venue_id) tuples of their scheduled matches.
        field_schedules: Dict mapping field_id to list of (start, end) tuples of matches scheduled on that field.
        team_schedules: Dict mapping team_id to list of (start, end) tuples of their scheduled matches so far.

    Returns:
        Tuple (is_valid, game_block_duration_delta, failure_reason)
    """
    # Increment the constraint check counter
    global constraint_check_counter
    constraint_check_counter += 1

    failure_reason = "Constraint check passed." # Default success message

    # RTM: FR-SCHED (Implicit - Requires Age Group Data)
    age_group_id = match_pairing.get('age_group_id')
    if not age_group_id: return False, None, "Constraint Check Failed: Pairing missing age_group_id."
    age_group = scheduling_data.get('age_groups', {}).get(age_group_id)
    if not age_group:
        return False, None, f"Constraint Check Failed: Age group data missing for pairing AG ID: {age_group_id}"

    t_settings = scheduling_data.get('tournament', {}).get('custom_settings', {})
    ag_settings = age_group.get('custom_settings', {})

    # --- 1. Get Game Timings & Calculate Block ---
    # RTM: FR-SETUP-5 (Timing Config)
    try:
        # Check Age Group settings first, then Tournament, then default
        game_duration = int(ag_settings.get('game_duration_minutes', t_settings.get('game_duration_minutes', DEFAULT_GAME_DURATION)))
        halftime = int(ag_settings.get('halftime_minutes', t_settings.get('halftime_minutes', DEFAULT_HALFTIME_MINUTES)))
        transition = int(ag_settings.get('transition_minutes_after', t_settings.get('transition_minutes_after', DEFAULT_TRANSITION_MINUTES)))
        min_rest_minutes = int(t_settings.get('minimum_team_rest_minutes', DEFAULT_MIN_REST_MINUTES)) # Get rest time setting
    except (ValueError, TypeError) as e:
        logging.warning(f"Invalid timing setting detected: {e}")
        return False, None, f"Constraint Check Failed: Invalid timing settings in config ({e})"

    game_block_minutes = game_duration + halftime + transition
    if game_block_minutes <= 0:
        return False, None, f"Constraint Check Failed: Invalid calculated game block duration ({game_block_minutes} mins)"

    game_block_delta = datetime.timedelta(minutes=game_block_minutes)
    potential_end_dt = potential_start_dt + game_block_delta
    min_rest_delta = datetime.timedelta(minutes=min_rest_minutes)

    # --- 2. Check Field Availability (Time Overlap) ---
    # RTM: FR-SCHED (Constraint: Field Availability)
    field_id = field.get('id')
    if not field_id: return False, None, "Constraint Check Failed: Field ID missing."
    for existing_start, existing_end in field_schedules.get(field_id, []):
         # Check if potential game [start, end) overlaps with existing game [start, end)
         if potential_start_dt < existing_end and potential_end_dt > existing_start:
              # Return the delta even on failure, might be useful contextually
              return False, game_block_delta, f"Field {field_id} time slot conflict (overlaps {existing_start.strftime('%H:%M')}-{existing_end.strftime('%H:%M')})"

    # --- 3. Check Field Format Compatibility ---
    # RTM: FR-SCHED (Constraint: Field Format), FR-SETUP-4
    required_format = age_group.get('format')
    field_type_name = field.get('field_type_name')
    # Check Tournament settings first, then default
    format_map = t_settings.get('format_to_field_type_map', DEFAULT_FORMAT_TO_FIELD_TYPE_MAP)
    allowed_field_types = format_map.get(required_format) if required_format else None

    if not required_format: return False, None, f"Constraint Check Failed: Age group {age_group.get('id')} missing required format."
    if not field_type_name: return False, None, f"Constraint Check Failed: Field {field_id} missing field type name."
    if not allowed_field_types or field_type_name not in allowed_field_types:
        return False, game_block_delta, f"Constraint Check Failed: Format '{required_format}' incompatible with Field Type '{field_type_name}'"

    # --- 4. Check Field Status ---
    # RTM: FR-SCHED (Constraint: Field Status)
    field_status = field.get('field_status', 'Open').strip().lower()
    if field_status != 'open':
        return False, game_block_delta, f"Constraint Check Failed: Field {field_id} status is '{field.get('field_status')}' (not Open)"

    # --- 5. Check Team Requests (Hard Constraints) ---
    # RTM: FR-SCHED (Constraint: Team Requests)
    team1_id = match_pairing.get('team1_id'); team2_id = match_pairing.get('team2_id')
    for team_id in [team1_id, team2_id]:
        if not team_id: continue # Skip if placeholder team
        for req in scheduling_data.get('team_requests', {}).get(team_id, []):
            try:
                req_day_str = req.get('request_day'); req_start_str = req.get('start_time'); req_end_str = req.get('end_time')
                request_type = req.get('request')
                req_day = datetime.date.fromisoformat(req_day_str) if req_day_str else None
                # Ensure comparison happens in UTC if request times have offsets
                req_start_dt = parse_iso_datetime(req_start_str)
                req_end_dt = parse_iso_datetime(req_end_str)

                # If request is day-specific, only apply it on that day
                if req_day and potential_start_dt.date() != req_day:
                    continue

                if request_type == 'Cannot Play Before' and req_start_dt and potential_start_dt < req_start_dt:
                    return False, game_block_delta, f"Team {team_id} request conflict: Cannot Play Before {req_start_dt.strftime('%H:%M %Z on %Y-%m-%d')}"
                elif request_type == 'Cannot Play After' and req_start_dt and potential_start_dt >= req_start_dt:
                    # Note: >= because the request is "Cannot Play *After* StartTime". If game starts exactly at StartTime, it's already "after" the allowed period began.
                    return False, game_block_delta, f"Team {team_id} request conflict: Cannot Play After {req_start_dt.strftime('%H:%M %Z on %Y-%m-%d')}"
                elif request_type == 'Cannot Play Between' and req_start_dt and req_end_dt and potential_start_dt < req_end_dt and potential_end_dt > req_start_dt:
                    # Game overlaps the forbidden interval
                    return False, game_block_delta, f"Team {team_id} request conflict: Cannot Play Between {req_start_dt.strftime('%H:%M')} and {req_end_dt.strftime('%H:%M')}"
            except Exception as e:
                logging.warning(f"Could not process team request {req.get('id')} for team {team_id}: {e}", exc_info=True);
                continue # Skip malformed requests

    # --- 6. Check Coach Conflicts (Direct Time Overlap ONLY) ---
    # RTM: FR-SCHED (Constraint: Coach Conflict)
    coaches = list(set(scheduling_data.get('team_staff',{}).get(team1_id, []) + scheduling_data.get('team_staff',{}).get(team2_id, [])))
    for coach_id in coaches:
        if not coach_id: continue # Skip if staff entry is somehow invalid
        for existing_start, existing_end, _ in coach_schedules.get(coach_id, []): # Ignore venue_id for this check
             # Direct Overlap Check: [pot_start, pot_end) vs [exist_start, exist_end)
             if potential_start_dt < existing_end and potential_end_dt > existing_start:
                  return False, game_block_delta, f"Coach {coach_id} time conflict (overlaps {existing_start.strftime('%H:%M')}-{existing_end.strftime('%H:%M')})"

    # --- 7. Check Team Rest Time ---
    # RTM: FR-RULES (Fairness - Implicit), NFR_USA (User expects reasonable rest)
    for team_id in [team1_id, team2_id]:
        if not team_id: continue # Skip placeholder teams
        last_game_end_time = None
        # Find the latest end time for this team among already scheduled matches *in this run*
        for scheduled_start, scheduled_end in team_schedules.get(team_id, []):
            if last_game_end_time is None or scheduled_end > last_game_end_time:
                last_game_end_time = scheduled_end

        if last_game_end_time is not None:
            # Check if potential start time provides enough rest after the last known game ends
            if potential_start_dt < (last_game_end_time + min_rest_delta):
                 required_rest_str = f"{min_rest_minutes} mins"
                 last_end_str = last_game_end_time.strftime('%H:%M')
                 pot_start_str = potential_start_dt.strftime('%H:%M')
                 return False, game_block_delta, f"Team {team_id} rest time conflict (Needs {required_rest_str} after {last_end_str}, proposed start {pot_start_str})"

    # --- All checks passed ---
    return True, game_block_delta, failure_reason


# --- Match Pairing Generation ---
def create_match_pairings(scheduling_data: Dict[str, Any]) -> Tuple[List[Dict[str, Any]], Dict[str, Any]]:
    """Generates round robin pairings per group, respects club avoidance and seeding.

    Args:
        scheduling_data: Dict containing tournament data, teams, etc.

    Returns:
        Tuple containing:
        - List of match pairings
        - Dict of bracket validation results with warnings and errors
    """
    # RTM: FR-BRACKET-8 (Auto Pairing), FR-BRACKET-11 (Club Avoidance)
    # RTM: FR8 (Auto Bracket/Pool Generation)
    # RTM: FR8.1 (Bracket Size Validation Rules)
    # RTM: FR8.2 (Odd-Team Warnings)
    # RTM: FR8.3 (Preferred Even Group Logic)
    # RTM: FR11 (Fairness in Bracket Generation - Seeding)
    pairings = []
    teams_by_group = defaultdict(list)
    t_settings = scheduling_data.get('tournament', {}).get('custom_settings', {})
    avoid_club_matchups = t_settings.get('avoid_club_matchups_group_stage', False)

    # Validation results to track bracket size issues
    validation_results = {
        "warnings": [],
        "errors": [],
        "group_stats": {},
        "odd_team_groups": [],
        "small_groups": [],  # Groups with fewer than 3 teams
        "large_groups": []   # Groups with more than 8 teams
    }

    # Group teams by their assigned group ID from registrations
    for reg in scheduling_data.get('registrations', []):
        if reg.get('assigned_group_id') and reg.get('team_id'):
            teams_by_group[reg['assigned_group_id']].append({
                'team_id': reg['team_id'],
                'club_id': reg.get('club_id'), # Fetch club_id from registration view
                'age_group_id': reg['tournament_age_group_id'],
                'seeding_rank': reg.get('seeding_rank')  # Include seeding rank for proper ordering
            })
        # Log if a registration is missing group or team ID for pairing purposes
        elif not reg.get('assigned_group_id') and reg.get('team_id'):
            logging.warning(f"Team {reg['team_id']} not assigned to a group, cannot generate pairings.")

    # Generate pairings within each group
    for group_id, teams_in_group in teams_by_group.items():
        team_count = len(teams_in_group)
        age_group_id = teams_in_group[0]['age_group_id'] if teams_in_group else None

        # Store group statistics
        validation_results["group_stats"][group_id] = {
            "team_count": team_count,
            "age_group_id": age_group_id,
            "has_byes": team_count % 2 != 0,  # Odd number of teams means byes are needed
            "format": determine_bracket_format(team_count)
        }

        # Validate group size
        if team_count < 2:
            validation_results["errors"].append(f"Group {group_id} has fewer than 2 teams, no pairings generated.")
            logging.info(f"Group {group_id} has fewer than 2 teams, no pairings generated.")
            continue
        elif team_count < 3:
            validation_results["warnings"].append(f"Group {group_id} has only 2 teams. Consider merging with another group.")
            validation_results["small_groups"].append(group_id)
        elif team_count > 8:
            validation_results["warnings"].append(f"Group {group_id} has {team_count} teams, which exceeds recommended maximum of 8. Consider splitting into multiple groups.")
            validation_results["large_groups"].append(group_id)

        # Check for odd number of teams
        if team_count % 2 != 0:
            validation_results["warnings"].append(f"Group {group_id} has {team_count} teams (odd number). Some teams will have byes.")
            validation_results["odd_team_groups"].append(group_id)

        # Sort teams by seeding rank if available
        # This ensures higher-seeded teams get better schedules and potentially more rest time
        teams_in_group_sorted = sorted(teams_in_group, key=lambda t: t.get('seeding_rank', 999))

        # Generate round-robin rounds for proper scheduling
        # The generate_round_robin_rounds function will use the seeding rank for proper ordering
        rounds = generate_round_robin_rounds(teams_in_group_sorted, avoid_club_matchups)

        # Create pairings from the rounds
        match_number = 1
        for round_idx, round_pairings in enumerate(rounds):
            for pairing in round_pairings:
                t1, t2 = pairing

                # Handle byes (where one team is None)
                if t1 is None or t2 is None:
                    # Create a match for byes to track them properly
                    if t1 is not None:
                        pairings.append({
                            'team1_id': t1['team_id'],
                            'team2_id': None,
                            'age_group_id': t1['age_group_id'],
                            'group_id': group_id,
                            'round_name': 'group',
                            'round_number': round_idx + 1,  # 1-based round number
                            'match_number': match_number,
                            'status': 'scheduled',
                            'is_bye': True
                        })
                        match_number += 1
                    elif t2 is not None:
                        pairings.append({
                            'team1_id': None,
                            'team2_id': t2['team_id'],
                            'age_group_id': t2['age_group_id'],
                            'group_id': group_id,
                            'round_name': 'group',
                            'round_number': round_idx + 1,  # 1-based round number
                            'match_number': match_number,
                            'status': 'scheduled',
                            'is_bye': True
                        })
                        match_number += 1
                    continue

                # Check club avoidance constraint
                # Ensure both teams have a club_id before comparing
                if avoid_club_matchups and t1.get('club_id') is not None and t1.get('club_id') == t2.get('club_id'):
                    logging.debug(f"Skipping club matchup: Team {t1['team_id']} vs Team {t2['team_id']} (Club: {t1['club_id']})")
                    continue

                # Add valid pairing
                pairings.append({
                    'team1_id': t1['team_id'],
                    'team2_id': t2['team_id'],
                    'age_group_id': t1['age_group_id'], # Assume teams in group share age group ID
                    'group_id': group_id,
                    'round_name': 'group', # Explicitly set for group stage
                    'status': 'scheduled', # Default status for newly generated pairings
                    'round_number': round_idx + 1,  # 1-based round number
                    'match_number': match_number,
                    'is_bye': False  # Regular match, not a bye
                })
                match_number += 1

    logging.info(f"Generated {len(pairings)} potential group stage pairings across {len(teams_by_group)} groups.")
    return pairings, validation_results


def determine_bracket_format(team_count: int) -> str:
    """Determines the appropriate bracket format based on team count.

    Args:
        team_count: Number of teams in the group/bracket

    Returns:
        String describing the recommended format
    """
    if team_count < 3:
        return "Insufficient teams"
    elif team_count == 3:
        return "Round robin only"
    elif team_count == 4:
        return "Round robin + final"
    elif team_count == 5:
        return "Single group round robin"
    elif team_count == 6:
        return "2 groups of 3 or single group"
    elif team_count <= 8:
        return "2 groups of 4 + semifinals & finals"
    elif team_count <= 12:
        return "3 groups of 3-4 + wildcard advance"
    else:
        return "4 groups of 4 + QF, SF, Final"


def generate_round_robin_rounds(teams: List[Dict[str, Any]], avoid_club_matchups: bool = False) -> List[List[Tuple[Dict[str, Any], Optional[Dict[str, Any]]]]]:
    """Generates round-robin rounds for a group of teams.

    Uses the circle method to generate rounds where each team plays every other team exactly once.
    Handles both even and odd numbers of teams (adding a bye for odd numbers).
    Implements club avoidance logic by attempting to swap pairings when same-club matchups are detected.

    Args:
        teams: List of team registration dicts
        avoid_club_matchups: Whether to avoid matching teams from the same club

    Returns:
        List of rounds, where each round is a list of pairings (team1, team2) tuples
    """
    # RTM: FR17.1 (Bye Awareness Logic)
    # RTM: FR11 (Club Avoidance Logic)
    team_count = len(teams)

    # Sort teams by seeding_rank if available to ensure higher-seeded teams get better schedules
    teams_sorted = sorted(teams, key=lambda t: t.get('seeding_rank', 999))

    # If odd number of teams, add a None for bye
    if team_count % 2 != 0:
        teams_for_scheduling = teams_sorted.copy() + [None]
        team_count += 1
    else:
        teams_for_scheduling = teams_sorted.copy()

    # Circle method for round robin
    rounds = []
    for round_num in range(team_count - 1):
        round_pairings = []
        club_conflicts = []

        # First pass: identify all pairings and potential club conflicts
        for i in range(team_count // 2):
            team1_idx = i
            team2_idx = team_count - 1 - i

            team1 = teams_for_scheduling[team1_idx]
            team2 = teams_for_scheduling[team2_idx]

            # Check for club conflicts
            is_club_conflict = (avoid_club_matchups and
                               team1 is not None and team2 is not None and
                               team1.get('club_id') and team2.get('club_id') and
                               team1['club_id'] == team2['club_id'])

            if is_club_conflict:
                club_conflicts.append(i)  # Track the index of the conflict
                logging.debug(f"Club conflict detected in round {round_num+1}: {team1['team_id']} vs {team2['team_id']} (Club: {team1['club_id']})")

            round_pairings.append((team1, team2))

        # Second pass: attempt to resolve club conflicts by swapping pairings
        if club_conflicts and len(club_conflicts) >= 2:
            # We need at least 2 conflicts to swap and resolve
            for i in range(0, len(club_conflicts) - 1, 2):
                idx1 = club_conflicts[i]
                idx2 = club_conflicts[i + 1]

                # Get the teams involved
                team1a, team1b = round_pairings[idx1]
                team2a, team2b = round_pairings[idx2]

                # Check if swapping would resolve the conflicts
                # We need to ensure the new pairings don't create new club conflicts
                new_conflict1 = (team1a is not None and team2a is not None and
                                team1a.get('club_id') and team2a.get('club_id') and
                                team1a['club_id'] == team2a['club_id'])

                new_conflict2 = (team1b is not None and team2b is not None and
                                team1b.get('club_id') and team2b.get('club_id') and
                                team1b['club_id'] == team2b['club_id'])

                if not new_conflict1 and not new_conflict2:
                    # Swap the pairings
                    round_pairings[idx1] = (team1a, team2b)
                    round_pairings[idx2] = (team2a, team1b)
                    logging.debug(f"Resolved club conflicts in round {round_num+1} by swapping pairings")

        # If we still have unresolved conflicts, log them
        remaining_conflicts = 0
        for team1, team2 in round_pairings:
            if (team1 is not None and team2 is not None and
                team1.get('club_id') and team2.get('club_id') and
                team1['club_id'] == team2['club_id']):
                remaining_conflicts += 1
                logging.warning(f"Unresolved club matchup in round {round_num+1}: {team1['team_id']} vs {team2['team_id']} (Club: {team1['club_id']})")

        if remaining_conflicts > 0:
            logging.info(f"Round {round_num+1} has {remaining_conflicts} unresolved club conflicts")

        rounds.append(round_pairings)

        # Rotate teams (keep first team fixed, rotate others)
        teams_for_scheduling = [teams_for_scheduling[0]] + [teams_for_scheduling[-1]] + teams_for_scheduling[1:-1]

    return rounds

# --- Playoff Seeding Helper ---
def get_ranked_team(tournament_id: str, group_id: str, rank: int) -> Optional[Dict]:
    """
    Fetches the team details for a specific rank within a group's standings
    by querying the 'standings' table.

    Args:
        tournament_id: The ID of the tournament.
        group_id: The ID of the specific group.
        rank: The desired rank (e.g., 1 for winner, 2 for runner-up).

    Returns:
        A dictionary containing team details (e.g., {'id': team_uuid, 'name': team_name})
        or None if the rank is not found or an error occurs.
    """
    if not supabase:
        logging.error("Cannot get ranked team: Supabase client not initialized.")
        return None
    if rank <= 0:
        logging.warning(f"Invalid rank requested ({rank}) for group {group_id}. Rank must be positive.")
        return None

    try:
        # Query the standings table/view
        # Assumes 'standings' table has 'team_id', 'name' (team name), and 'group_position'
        # Filters by tournament, group, and the specific rank (group_position)
        logging.debug(f"Querying standings for rank {rank}, group {group_id}, tournament {tournament_id}")
        response = supabase.table("standings").select("team_id, name") \
            .eq("tournament_id", tournament_id) \
            .eq("group_id", group_id) \
            .eq("group_position", rank) \
            .maybe_single() \
            .execute()

        # We call check_supabase_error AFTER the execute(), as execute() itself might raise an error
        # on network issues, but check_supabase_error handles specific DB/PostgREST errors
        check_supabase_error(response, f"fetching rank {rank} team for group {group_id}")

        if response.data:
            team_id = response.data.get('team_id')
            team_name = response.data.get('name')
            if team_id:
                logging.info(f"Found Rank {rank} Team {team_id} ('{team_name}') for Group {group_id}")
                return {'id': team_id, 'name': team_name}
            else:
                logging.warning(f"Standings data found for rank {rank}, group {group_id}, but missing 'team_id'. Data: {response.data}")
                return None
        else:
            # It's not necessarily an error if a rank isn't found (e.g., group only had 1 team)
            logging.info(f"No team found at rank {rank} for group {group_id} in tournament {tournament_id}.")
            return None

    except Exception as e:
        # Catch potential exceptions during the DB call or error check
        logging.error(f"Error executing query for rank {rank} team for group {group_id}: {e}", exc_info=True)
        return None


# --- Playoff Seeding Implementation ---
def seed_playoff_bracket(tournament_id: str, scheduling_data: Dict[str, Any], field_schedules=None, coach_schedules=None, team_schedules=None, group_stage_end_time: datetime.datetime=None):
    """
    Generates placeholder matches for playoff rounds based on group standings.
    Uses the SQL procedure seed_playoff_bracket to create the playoff bracket structure.
    """
    # RTM: FR-BRACKET-8 (Playoff Seeding), FR-BRACKET-11 (Seeding Fairness)
    if not supabase: raise Exception("Supabase client not initialized.")
    logging.info(f"Attempting to seed playoff brackets for tournament {tournament_id}")

    errors = []
    playoff_matches_created = 0

    # Group registrations by age_group_id to process each division
    registrations_by_division = defaultdict(list)
    for reg in scheduling_data.get('registrations', []):
        if reg.get('tournament_age_group_id'):
            registrations_by_division[reg['tournament_age_group_id']].append(reg)

    # Iterate through each Division
    for age_group_id, regs_in_division in registrations_by_division.items():
        age_group_data = scheduling_data.get('age_groups', {}).get(age_group_id)
        if not age_group_data:
            logging.warning(f"Skipping playoff seeding for division {age_group_id}: Age group data missing.")
            continue

        logging.info(f"Processing playoff seeding for Division: {age_group_data.get('name', age_group_id)}")

        try:
            # Call the SQL procedure to seed the playoff bracket
            query = "CALL public.seed_playoff_bracket(:tournament_id, :age_group_id)"
            params = {
                "tournament_id": tournament_id,
                "age_group_id": age_group_id
            }

            # Execute the query
            result = supabase.rpc("seed_playoff_bracket", params).execute()

            # Check for errors
            if hasattr(result, 'error') and result.error:
                error_msg = f"Error seeding playoff bracket for division {age_group_data.get('name', age_group_id)}: {result.error}"
                logging.error(error_msg)
                errors.append(error_msg)
            else:
                # Count the number of matches created
                count_query = "SELECT COUNT(*) FROM public.matches WHERE tournament_id = :tournament_id AND tournament_age_group_id = :age_group_id AND round_name IN ('round_of_16', 'quarterfinal', 'semifinal', 'final', 'consolation')"
                count_params = {
                    "tournament_id": tournament_id,
                    "age_group_id": age_group_id
                }

                count_result = supabase.rpc("execute_sql", {"query": count_query, "params": count_params}).execute()
                if hasattr(count_result, 'data') and count_result.data:
                    playoff_matches_created += count_result.data[0]['count']

                logging.info(f"Successfully seeded playoff bracket for division {age_group_data.get('name', age_group_id)}")
        except Exception as e:
            error_msg = f"Error seeding playoff bracket for division {age_group_data.get('name', age_group_id)}: {str(e)}"
            logging.error(error_msg, exc_info=True)
            errors.append(error_msg)

    # Log final summary
    logging.info(f"Finished playoff seeding for tournament {tournament_id}. Total matches generated: {playoff_matches_created}.")
    return errors


# --- Fairness Analysis Function ---
def analyze_schedule_fairness(scheduling_data: Dict[str, Any], team_schedules: Dict[str, List[Tuple[datetime.datetime, datetime.datetime]]], field_schedules: Dict[str, List[Tuple[datetime.datetime, datetime.datetime]]]) -> Dict[str, Any]:
    """Analyzes the fairness of a generated schedule.

    Args:
        scheduling_data: Dict containing tournament, age_groups, settings, etc.
        team_schedules: Dict mapping team_id to list of (start, end) tuples of their scheduled matches.
        field_schedules: Dict mapping field_id to list of (start, end) tuples of matches scheduled on that field.

    Returns:
        Dict containing fairness metrics and warnings.
    """
    # RTM: FR-RULES (Fairness), FR-SCHED-17 (Fairness Validation)
    # RTM: FR17.1 (Bye Awareness Logic)
    # RTM: FR17.2 (Rest Period Balancing)
    fairness_report = {
        "warnings": [],
        "metrics": {},
        "team_metrics": {},
        "bye_analysis": {}
    }

    # 1. Analyze rest time distribution
    team_rest_times = {}
    team_game_counts = {}
    team_earliest_game = {}
    team_latest_game = {}

    for team_id, schedule in team_schedules.items():
        if not schedule:
            continue

        # Sort schedule by start time
        sorted_schedule = sorted(schedule, key=lambda x: x[0])
        team_game_counts[team_id] = len(sorted_schedule)
        team_earliest_game[team_id] = sorted_schedule[0][0]
        team_latest_game[team_id] = sorted_schedule[-1][1]

        # Calculate rest times between games
        rest_times = []
        for i in range(1, len(sorted_schedule)):
            prev_game_end = sorted_schedule[i-1][1]
            next_game_start = sorted_schedule[i][0]
            rest_time = (next_game_start - prev_game_end).total_seconds() / 60  # in minutes
            rest_times.append(rest_time)

        if rest_times:
            team_rest_times[team_id] = {
                "min": min(rest_times),
                "max": max(rest_times),
                "avg": sum(rest_times) / len(rest_times)
            }

    # 2. Check for significant rest time disparities
    if team_rest_times:
        min_rest_values = [data["min"] for data in team_rest_times.values()]
        max_min_rest_diff = max(min_rest_values) - min(min_rest_values) if min_rest_values else 0

        if max_min_rest_diff > 60:  # More than 1 hour difference in minimum rest time
            fairness_report["warnings"].append(f"Significant rest time disparity: {max_min_rest_diff:.0f} minutes difference in minimum rest times between teams")

        # Check for teams with short rest periods (less than 2 hours)
        teams_with_short_rest = [team_id for team_id, data in team_rest_times.items() if data["min"] < 120]
        if teams_with_short_rest:
            fairness_report["warnings"].append(f"{len(teams_with_short_rest)} teams have less than 2 hours rest between games")

        # Calculate rest time imbalance between teams
        avg_rest_values = [data["avg"] for data in team_rest_times.values()]
        global_avg_rest = sum(avg_rest_values) / len(avg_rest_values) if avg_rest_values else 0
        rest_time_imbalance = []

        for team_id, data in team_rest_times.items():
            # If team's average rest is 30% less than global average, flag it
            if data["avg"] < global_avg_rest * 0.7:
                rest_time_imbalance.append({
                    "team_id": team_id,
                    "avg_rest": data["avg"],
                    "global_avg": global_avg_rest,
                    "difference_pct": ((global_avg_rest - data["avg"]) / global_avg_rest) * 100
                })

        # Add warning for rest time imbalance
        if rest_time_imbalance:
            for imbalance in rest_time_imbalance:
                fairness_report["warnings"].append(
                    f"Team {imbalance['team_id']} has {imbalance['difference_pct']:.0f}% less rest time than average"
                )

    # 3. Check for game count disparities
    if team_game_counts:
        min_games = min(team_game_counts.values())
        max_games = max(team_game_counts.values())

        if max_games - min_games > 1:
            fairness_report["warnings"].append(f"Game count disparity: Some teams have {max_games} games while others have {min_games}")

    # 4. Analyze teams with byes (teams with fewer games in odd-team groups)
    teams_with_byes = []
    teams_by_group = defaultdict(list)

    # Group teams by their group ID
    for reg in scheduling_data.get('registrations', []):
        if reg.get('assigned_group_id') and reg.get('team_id'):
            teams_by_group[reg['assigned_group_id']].append(reg['team_id'])

    # Find groups with odd number of teams
    odd_team_groups = [group_id for group_id, teams in teams_by_group.items() if len(teams) % 2 != 0]

    # For each odd-team group, find teams with fewer games (likely have byes)
    for group_id in odd_team_groups:
        group_teams = teams_by_group[group_id]
        group_game_counts = {team_id: team_game_counts.get(team_id, 0) for team_id in group_teams}
        if group_game_counts:
            max_group_games = max(group_game_counts.values())
            for team_id, count in group_game_counts.items():
                if count < max_group_games:
                    teams_with_byes.append({
                        "team_id": team_id,
                        "group_id": group_id,
                        "game_count": count,
                        "max_group_games": max_group_games
                    })

    # Add bye analysis to report
    fairness_report["bye_analysis"] = {
        "odd_team_groups": odd_team_groups,
        "teams_with_byes": teams_with_byes
    }

    # Add warning for teams with byes
    if teams_with_byes:
        fairness_report["warnings"].append(f"{len(teams_with_byes)} teams have byes in the schedule")

    # 5. Check for early/late game distribution
    early_threshold = datetime.time(9, 0)  # 9:00 AM
    late_threshold = datetime.time(17, 0)  # 5:00 PM
    early_game_teams = []
    late_game_teams = []

    for team_id, earliest in team_earliest_game.items():
        if earliest.time() < early_threshold:
            early_game_teams.append(team_id)

    for team_id, latest in team_latest_game.items():
        if latest.time() > late_threshold:
            late_game_teams.append(team_id)

    # Check if some teams have both early and late games while others don't
    both_early_and_late = [team for team in early_game_teams if team in late_game_teams]
    if both_early_and_late and (len(both_early_and_late) < len(team_schedules)):
        fairness_report["warnings"].append(f"{len(both_early_and_late)} teams have both early (<9:00) and late (>17:00) games while others don't")

    # 6. Field quality distribution
    # Assuming field_id order might indicate quality (e.g., f1 is main field)
    # This is a simplistic approach - in reality, you might have field quality ratings
    team_field_usage = {}
    for field_id, schedule in field_schedules.items():
        for start, end in schedule:
            # Find teams playing in this field at this time
            for team_id, team_schedule in team_schedules.items():
                if any(s[0] == start for s in team_schedule):  # Team has a game starting at this time
                    if team_id not in team_field_usage:
                        team_field_usage[team_id] = []
                    team_field_usage[team_id].append(field_id)

    # Store metrics for reporting
    fairness_report["metrics"] = {
        "rest_time_disparity": max_min_rest_diff if team_rest_times else 0,
        "game_count_disparity": max_games - min_games if team_game_counts else 0,
        "teams_with_early_games": len(early_game_teams),
        "teams_with_late_games": len(late_game_teams),
        "teams_with_both_early_and_late": len(both_early_and_late),
        "teams_with_byes": len(teams_with_byes),
        "odd_team_groups": len(odd_team_groups)
    }

    fairness_report["team_metrics"] = {
        team_id: {
            "game_count": team_game_counts.get(team_id, 0),
            "min_rest_time": team_rest_times.get(team_id, {}).get("min", None),
            "avg_rest_time": team_rest_times.get(team_id, {}).get("avg", None),
            "earliest_game": team_earliest_game.get(team_id, None).strftime("%Y-%m-%d %H:%M") if team_id in team_earliest_game else None,
            "latest_game": team_latest_game.get(team_id, None).strftime("%Y-%m-%d %H:%M") if team_id in team_latest_game else None,
            "fields_used": team_field_usage.get(team_id, []),
            "has_bye": any(bye["team_id"] == team_id for bye in teams_with_byes)
        } for team_id in team_schedules.keys()
    }

    return fairness_report

# --- Performance Optimized Scheduling Algorithm ---
def optimize_match_schedule(match_pairings: List[Dict[str, Any]], potential_start_slots: Dict[str, List[datetime.datetime]],
                          scheduling_data: Dict[str, Any], max_iterations: int = 1000) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]], Dict[str, List[Tuple[datetime.datetime, datetime.datetime]]], Dict[str, List[Tuple[datetime.datetime, datetime.datetime]]], Dict[str, List[Tuple[datetime.datetime, datetime.datetime, str]]], int]:
    """
    Optimized scheduling algorithm that uses a more sophisticated approach to find a better schedule.
    Uses a combination of greedy algorithm with limited backtracking to improve scheduling outcomes.

    Args:
        match_pairings: List of match pairings to schedule
        potential_start_slots: Dict mapping field_id to list of potential start times
        scheduling_data: Dict containing tournament data, fields, teams, etc.
        max_iterations: Maximum number of iterations to try before giving up

    Returns:
        Tuple containing:
        - List of scheduled matches
        - List of unscheduled matches
        - Dict of field schedules
        - Dict of team schedules
        - Dict of coach schedules
    """
    # Initialize tracking structures
    scheduled_matches = []
    unscheduled_matches = []
    field_schedules = defaultdict(list)
    team_schedules = defaultdict(list)
    coach_schedules = defaultdict(list)

    # Reset constraint check counter
    global constraint_check_counter
    constraint_check_counter = 0

    # Sort pairings by priority (e.g., playoff matches first, then by division importance)
    # This ensures more important matches get scheduled first
    sorted_pairings = sorted(match_pairings, key=lambda p: (
        0 if p.get('round_name') in ['semifinal', 'final'] else 1,  # Playoff matches first
        p.get('age_group_id', '')  # Then by age group (could be refined with actual priority)
    ))

    # Create a flattened list of all field/time slot combinations for easier iteration
    all_slots = []
    for field_id, start_times in potential_start_slots.items():
        field_data = scheduling_data['fields'].get(field_id)
        if not field_data:
            continue
        for start_time in start_times:
            all_slots.append((field_id, field_data, start_time))

    # Sort slots by time (earlier slots first) to maximize field usage
    all_slots.sort(key=lambda s: s[2])

    # Process each pairing
    for pairing in sorted_pairings:
        is_scheduled = False
        best_slot = None
        best_score = -1

        # Try each potential slot and score it
        for field_id, field_data, start_time in all_slots:
            is_valid, game_block_delta, reason = check_constraints(
                pairing, start_time, field_data, scheduling_data,
                coach_schedules, field_schedules, team_schedules
            )

            if is_valid:
                # Calculate a score for this slot (lower is better)
                score = 0

                # Prefer earlier slots
                score += start_time.hour * 10 + start_time.minute / 6

                # Prefer fields with more usage (to concentrate games)
                score -= len(field_schedules[field_id]) * 5

                # Prefer slots that give teams more rest time
                team1_id = pairing.get('team1_id')
                team2_id = pairing.get('team2_id')

                if team1_id and team_schedules[team1_id]:
                    last_game_end = max(end for _, end in team_schedules[team1_id])
                    rest_time = (start_time - last_game_end).total_seconds() / 60
                    # Penalize short rest times, reward longer ones up to a point
                    if rest_time < 120:
                        score += (120 - rest_time) / 10
                    else:
                        score -= min(rest_time - 120, 60) / 20  # Reward up to 1 hour extra rest

                if team2_id and team_schedules[team2_id]:
                    last_game_end = max(end for _, end in team_schedules[team2_id])
                    rest_time = (start_time - last_game_end).total_seconds() / 60
                    if rest_time < 120:
                        score += (120 - rest_time) / 10
                    else:
                        score -= min(rest_time - 120, 60) / 20

                # If this is the best slot so far, remember it
                if best_slot is None or score < best_score:
                    best_slot = (field_id, field_data, start_time, game_block_delta)
                    best_score = score

        # If we found a valid slot, schedule the match
        if best_slot:
            field_id, field_data, start_time, game_block_delta = best_slot
            end_time = start_time + game_block_delta

            # Create match data
            match_data = {
                'tournament_id': scheduling_data['tournament']['id'],
                'tournament_age_group_id': pairing.get('age_group_id'),
                'team1_id': pairing.get('team1_id'),
                'team2_id': pairing.get('team2_id'),
                'field_id': field_id,
                'scheduled_time': start_time.isoformat(),
                'status': 'scheduled',
                'round_name': pairing.get('round_name', 'group'),
                'group_id': pairing.get('group_id'),
                'match_number': pairing.get('match_number', 1)
            }

            # Add to scheduled matches
            scheduled_matches.append(match_data)

            # Update schedules
            field_schedules[field_id].append((start_time, end_time))
            if pairing.get('team1_id'):
                team_schedules[pairing['team1_id']].append((start_time, end_time))
            if pairing.get('team2_id'):
                team_schedules[pairing['team2_id']].append((start_time, end_time))

            # Update coach schedules
            coaches = list(set(
                scheduling_data.get('team_staff', {}).get(pairing.get('team1_id', ''), []) +
                scheduling_data.get('team_staff', {}).get(pairing.get('team2_id', ''), [])
            ))
            for coach_id in coaches:
                if coach_id:
                    coach_schedules[coach_id].append((start_time, end_time, field_data.get('venue_id', '')))

            is_scheduled = True

        # If we couldn't schedule this match, add it to unscheduled
        if not is_scheduled:
            unscheduled_matches.append(pairing)

    # Add constraint check count to performance metrics
    return scheduled_matches, unscheduled_matches, field_schedules, team_schedules, coach_schedules, constraint_check_counter

# --- Main Scheduling Function ---
def generate_match_schedule(tournament_id: str) -> Dict[str, Any]:
    """Generates group stage schedule and seeds playoff structure."""
    # RTM: FR-SCHED-14 (Auto Schedule Generation)
    results: Dict[str, Any] = {"scheduled_count": 0, "unscheduled_count": 0, "errors": [], "unscheduled_details": [], "fairness_report": {}, "performance_metrics": {}}
    logging.info(f"--- Starting Match Scheduling Process for Tournament: {tournament_id} ---")
    if not supabase: results['errors'].append("Supabase client not initialized."); return results

    # Track performance metrics
    start_time = datetime.datetime.now()
    performance_metrics = {
        "start_time": start_time.isoformat(),
        "total_time_ms": 0,
        "optimization_time_ms": 0,
        "db_operations_time_ms": 0,
        "playoff_seeding_time_ms": 0,
        "fairness_analysis_time_ms": 0,
        "matches_processed": 0,
        "fields_processed": 0,
        "time_slots_processed": 0,
        "constraints_checked": 0,
        "memory_usage_kb": 0
    }

    # Try to get memory usage if psutil is available
    try:
        import psutil
        process = psutil.Process()
        performance_metrics["memory_usage_kb"] = process.memory_info().rss / 1024
    except ImportError:
        logging.info("psutil not available for memory tracking")

    try:
        # --- 1. Fetch Data ---
        # RTM: FR-SCHED (Implicit - Needs Data)
        scheduling_data = fetch_scheduling_data(tournament_id)
        if scheduling_data.get('errors'):
            results['errors'].extend(scheduling_data['errors'])
            if "Tournament not found" in scheduling_data.get('errors', []):
                 return results
            # Continue if other non-critical fetch errors occurred? Optional.

        # --- 2. Generate Potential Slots & Pairings ---
        # RTM: FR-SCHED (Implicit), FR-BRACKET-8
        potential_start_slots = generate_time_slots(scheduling_data)
        if not potential_start_slots:
            results['errors'].append("No available time slots generated for any field.")
            match_pairings, bracket_validation = create_match_pairings(scheduling_data)
            results['unscheduled_count'] = len(match_pairings)
            results['unscheduled_details'] = [{"type": "match_pairing", "identifier": f"Group Match: {p.get('team1_id')} vs {p.get('team2_id')}", "reason": "No available time slots found"} for p in match_pairings]
            # Still attempt playoff seeding even if group games fail? Probably not.
            return results

        match_pairings, bracket_validation = create_match_pairings(scheduling_data)

        # Add bracket validation results to the main results
        results['bracket_validation'] = bracket_validation

        # Add bracket warnings to the main warnings
        if bracket_validation.get('warnings'):
            results['warnings'] = results.get('warnings', []) + bracket_validation['warnings']

        # Add bracket errors to the main errors
        if bracket_validation.get('errors'):
            results['errors'] = results.get('errors', []) + bracket_validation['errors']

        if not match_pairings:
             results['warnings'] = results.get('warnings', []) + ["No match pairings generated (check registrations/groups)."]

        # --- 3. Use Optimized Scheduling Algorithm ---
        logging.info(f"Attempting to schedule {len(match_pairings)} group pairings using optimized algorithm...")

        # Log bracket validation information
        if bracket_validation.get('odd_team_groups'):
            logging.warning(f"Found {len(bracket_validation['odd_team_groups'])} groups with odd number of teams. Some teams will have byes.")

        if bracket_validation.get('small_groups'):
            logging.warning(f"Found {len(bracket_validation['small_groups'])} groups with fewer than 3 teams.")

        if bracket_validation.get('large_groups'):
            logging.warning(f"Found {len(bracket_validation['large_groups'])} groups with more than 8 teams.")

        # Count fields and time slots for performance metrics
        performance_metrics["fields_processed"] = len(potential_start_slots)
        time_slots_count = sum(len(slots) for slots in potential_start_slots.values())
        performance_metrics["time_slots_processed"] = time_slots_count
        performance_metrics["matches_processed"] = len(match_pairings)

        # Use the optimized scheduling algorithm
        optimization_start_time = datetime.datetime.now()
        scheduled_matches_payload, unscheduled_pairings, field_schedules, team_schedules, coach_schedules, constraint_checks = optimize_match_schedule(
            match_pairings, potential_start_slots, scheduling_data
        )

        # Track optimization performance metrics
        optimization_end_time = datetime.datetime.now()
        optimization_time_ms = (optimization_end_time - optimization_start_time).total_seconds() * 1000
        performance_metrics["optimization_time_ms"] = optimization_time_ms
        performance_metrics["constraints_checked"] = constraint_checks

        # Find the last group game end time for playoff scheduling
        last_group_game_end_time = datetime.datetime.min.replace(tzinfo=datetime.timezone.utc)
        for field_id, schedule in field_schedules.items():
            for _, end_time in schedule:
                if end_time > last_group_game_end_time:
                    last_group_game_end_time = end_time

        # Track unscheduled pairings for reporting
        results['unscheduled_count'] = len(unscheduled_pairings)
        for pairing in unscheduled_pairings:
            identifier = f"Group Match: {pairing.get('team1_id','P1')} vs {pairing.get('team2_id','P2')} (AG:{pairing.get('age_group_id')})"
            results['unscheduled_details'].append({
                "type": "match_pairing",
                "identifier": identifier,
                "reason": "Could not find a suitable time slot that satisfies all constraints"
            })
            logging.warning(f"Could not schedule pairing: {identifier}. Reason: Could not find a suitable time slot.")

        # --- 4. Run Fairness Analysis ---
        # RTM: FR-SCHED-17 (Fairness Validation)
        fairness_start_time = datetime.datetime.now()
        fairness_report = analyze_schedule_fairness(scheduling_data, team_schedules, field_schedules)
        fairness_end_time = datetime.datetime.now()
        performance_metrics["fairness_analysis_time_ms"] = (fairness_end_time - fairness_start_time).total_seconds() * 1000

        results['fairness_report'] = fairness_report

        # Add fairness warnings to the main results
        if fairness_report.get('warnings'):
            results['warnings'] = results.get('warnings', []) + fairness_report['warnings']
            logging.info(f"Fairness analysis generated {len(fairness_report['warnings'])} warnings")

        # --- 5. Persist Group Stage Schedule ---
        logging.info("Attempting DB update for group stage...")
        db_start_time = datetime.datetime.now()
        try:
            del_resp = supabase.table("matches").delete().match({
                "tournament_id": tournament_id,
                "round_name": "group"
            }).not_.in_("status", ['completed', 'in_progress', 'penalty_shootout']).execute()
            check_supabase_error(del_resp, "deleting old group schedule")
            logging.info(f"Deletion check executed for existing replaceable group matches.")
        except Exception as e:
             logging.error(f"Failed to delete old group matches: {e}", exc_info=True)
             results['errors'].append(f"Failed to clear old schedule: {e}")

        if scheduled_matches_payload:
            try:
                ins_resp = supabase.table("matches").insert(scheduled_matches_payload).execute()
                check_supabase_error(ins_resp, "inserting new group schedule")
                num_inserted = len(ins_resp.data) if hasattr(ins_resp, 'data') else 0
                results['scheduled_count'] = num_inserted
                if num_inserted != len(scheduled_matches_payload):
                    results['errors'].append(f"Mismatch inserting group matches! Attempted: {len(scheduled_matches_payload)}, Inserted: {num_inserted}")
                logging.info(f"Successfully inserted {num_inserted} group stage matches.")
            except Exception as e:
                logging.error(f"Failed to insert new group matches: {e}", exc_info=True)
                results['errors'].append(f"Failed to insert new schedule: {e}")
                results['scheduled_count'] = 0
                # If insert fails, mark all pairings as unscheduled for reporting
                results['unscheduled_count'] = len(match_pairings)
                results['unscheduled_details'] = [{"type": "match_pairing", "identifier": f"Group Match: {p.get('team1_id')} vs {p.get('team2_id')}", "reason": f"DB Insert Failed: {e}"} for p in match_pairings]

        else:
            # If no payload, make sure count reflects this
            # Get previous unscheduled count BEFORE potentially adding DB insert failures
            current_unscheduled_count = len(results['unscheduled_details'])
            results['scheduled_count'] = 0
            results['unscheduled_count'] = current_unscheduled_count # Keep existing unscheduled pairings
            logging.info("No new group stage matches were scheduled to insert.")

        # Track DB operation performance
        db_end_time = datetime.datetime.now()
        performance_metrics["db_operations_time_ms"] = (db_end_time - db_start_time).total_seconds() * 1000

        # --- 5. Seed Playoff Bracket ---
        # Only run if group stage games were actually scheduled (or maybe allow seeding even if 0 group games?)
        # Also depends on standings being available. For now, run if group stage had an end time.
        # Also check if there were critical DB errors during group stage persistence
        if not results['errors'] and last_group_game_end_time > datetime.datetime.min.replace(tzinfo=datetime.timezone.utc):
             # Track playoff seeding performance
             playoff_start_time = datetime.datetime.now()

             # Call the NEW seed_playoff_bracket function
             playoff_errors = seed_playoff_bracket(
                 tournament_id, scheduling_data, field_schedules, coach_schedules, team_schedules, last_group_game_end_time
             )

             # Record playoff seeding performance
             playoff_end_time = datetime.datetime.now()
             performance_metrics["playoff_seeding_time_ms"] = (playoff_end_time - playoff_start_time).total_seconds() * 1000

             if playoff_errors: results['errors'].extend(playoff_errors)
        else:
             logging.info("Skipping playoff seeding due to errors or no group games scheduled.")


        # --- 6. Final Result Calculation ---
        # Ensure unscheduled count reflects reality if inserts failed
        if "Failed to insert new schedule" in " ".join(results.get('errors',[])):
             results['unscheduled_count'] = len(match_pairings)
        else:
             results['unscheduled_count'] = len(results['unscheduled_details'])

        if results['unscheduled_details']: logging.warning(f"{results['unscheduled_count']} items could not be scheduled.")

    except Exception as e:
        logging.error(f"!!! Critical error during match scheduling process for {tournament_id}: {e}", exc_info=True)
        results['errors'].append(f"Critical Scheduler Error: {e}")

    # Calculate total execution time
    end_time = datetime.datetime.now()
    performance_metrics["total_time_ms"] = (end_time - start_time).total_seconds() * 1000

    # Add performance metrics to results
    results["performance_metrics"] = performance_metrics

    logging.info(f"--- Match Scheduling Process Finished for {tournament_id} ---")
    logging.info(f"Performance metrics: Total time: {performance_metrics['total_time_ms']:.2f}ms, Optimization: {performance_metrics['optimization_time_ms']:.2f}ms")
    return results

# --- Referee Assignment Implementation ---
def generate_referee_assignments(tournament_id: str) -> Dict[str, Any]:
    """Assigns referees to scheduled matches based on qualifications, availability, and fairness.

    Args:
        tournament_id: The tournament ID

    Returns:
        Dict containing assignment results, counts, and any errors
    """
    # RTM: FR-SCHED (Referee Assignment)
    if not supabase: raise Exception("Supabase client not initialized.")

    # Import the referee assignment service
    try:
        from referee_assignment_service import generate_referee_assignments as generate_referee_assignments_service

        # Call the service function
        results = generate_referee_assignments_service(tournament_id)

        # Log the results
        logging.info(f"Referee assignment completed: {results.get('assigned_count', 0)} assigned, {results.get('unassigned_count', 0)} unassigned.")

        return results
    except ImportError:
        logging.error("Failed to import referee_assignment_service. Falling back to basic implementation.")

        # Basic implementation as fallback
        results = {
            "assigned_count": 0,
            "unassigned_count": 0,
            "errors": ["Failed to import referee_assignment_service. Using basic implementation."],
            "warnings": [],
            "assignments": [],
            "conflicts": []
        }

        try:
            # Fetch matches that need referees
            matches_resp = supabase.table("matches").select("*").match({
                "tournament_id": tournament_id,
                "status": "scheduled"
            }).is_("referee_id", "null").execute()
            check_supabase_error(matches_resp, "fetching matches needing referees")

            matches = matches_resp.data
            if not matches:
                results["warnings"].append("No matches found needing referee assignments.")
                return results

            # Fetch available referees
            referees_resp = supabase.table("referees").select("*").execute()
            check_supabase_error(referees_resp, "fetching referees")

            referees = referees_resp.data
            if not referees:
                results["errors"].append("No referees found.")
                return results

            # Simple assignment - just assign the first referee to each match
            if referees and matches:
                referee = referees[0]
                assignments_to_update = []

                for match in matches:
                    assignments_to_update.append({
                        "id": match["id"],
                        "referee_id": referee["profile_id"]
                    })

                    results["assignments"].append({
                        "match_id": match["id"],
                        "referee_id": referee["profile_id"],
                        "scheduled_time": match["scheduled_time"]
                    })
                    results["assigned_count"] += 1

                # Update matches with referee assignments
                if assignments_to_update:
                    update_resp = supabase.table("matches").upsert(assignments_to_update).execute()
                    check_supabase_error(update_resp, "updating referee assignments")

                    logging.info(f"Successfully assigned {results['assigned_count']} referees using basic implementation.")
        except Exception as e:
            error_msg = f"Error in basic referee assignment implementation: {str(e)}"
            logging.error(error_msg, exc_info=True)
            results["errors"].append(error_msg)

        return results

    except Exception as e:
        error_msg = f"Error generating referee assignments: {str(e)}"
        logging.error(error_msg, exc_info=True)
        results["errors"].append(error_msg)

    return results


# --- Main Orchestration Function (Called by Background Task) ---
def run_full_schedule_process(tournament_id: str):
    """Orchestrates the scheduling and assignment process."""
    if not supabase:
        logging.error("Supabase client not available in background task.")
        return { "tournament_id": tournament_id, "match_scheduling": {"errors": ["Scheduler service DB connection failed."]}, "referee_assignment": {"errors": ["Skipped due to DB connection failure."]} }

    match_result = generate_match_schedule(tournament_id)
    ref_result = {"errors": ["Skipped - Match scheduling had errors or scheduled 0 games."]}

    # Only attempt referee assignment if match scheduling was successful and scheduled games
    if not match_result.get('errors') and match_result.get('scheduled_count', 0) > 0:
        try:
            ref_result = generate_referee_assignments(tournament_id)
        except Exception as e:
             logging.error(f"Error during referee assignment for {tournament_id}: {e}", exc_info=True)
             ref_result = {"errors": [f"Referee assignment failed: {e}"], "assigned_count": 0, "unassigned_count": 0}
    else:
        logging.warning(f"Skipping Referee Assignment for {tournament_id} due to match scheduling results: Errors={match_result.get('errors')}, Scheduled={match_result.get('scheduled_count')}")

    final_result = {
        "tournament_id": tournament_id,
        "match_scheduling": match_result,
        "referee_assignment": ref_result,
        "completed_at": datetime.datetime.now(datetime.timezone.utc).isoformat()
    }
    logging.info(f"Background task finished for {tournament_id}. Match Scheduled: {match_result.get('scheduled_count', 0)}. Ref Assigned: {ref_result.get('assigned_count', 0)}")
    return final_result

# Note: This service logic is called by tasks.py which is triggered by scheduler_api_production_v2.py
