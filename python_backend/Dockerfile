FROM python:3.9-slim

# Set working directory
WORKDIR /app

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV ENVIRONMENT=production

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    gcc \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
COPY requirements.txt /app/requirements.txt  # Explicitly copy to /app/requirements.txt
RUN pip install --upgrade pip \
    && pip install --no-cache-dir -r requirements.txt \
    # gunicorn is often included in requirements.txt for FastAPI projects; psutil is optional
    # If gunicorn is not in requirements.txt, it should be added there or installed here explicitly.
    # For now, assuming gunicorn is in requirements.txt as it's a common practice.
    # If not, add: && pip install gunicorn

# Copy application code from the app directory
RUN mkdir -p /app/src/app/core # Explicitly create all target directories

# Copy files and directories individually
COPY app/__init__.py /app/src/app/
COPY app/main.py /app/src/app/
COPY app/schemas.py /app/src/app/
COPY app/core/__init__.py /app/src/app/core/
COPY app/core/supabase_client.py /app/src/app/core/
# Add other files/directories from python_backend/app if they exist and are needed

# Create a non-root user (optional, but good practice)
# RUN useradd -m appuser
# USER appuser

# WORKDIR is /app. main.py is now at /app/src/app/main.py. Module path for Gunicorn is src.app.main:app.
# The command assumes Gunicorn is installed (either via requirements.txt or explicitly above)
# Using uvicorn worker for FastAPI with Gunicorn is common.
CMD ["gunicorn", "-k", "uvicorn.workers.UvicornWorker", "--bind", "0.0.0.0:8000", "src.app.main:app"]
