# backend/requirements.txt

# Web Framework & Server
# fastapi>=0.80.0,<0.100.0 # Pin versions for stability - consolidated below
# uvicorn[standard]>=0.18.0,<0.21.0 # Includes performance extras - consolidated below

# Supabase & Auth
supabase>=2.0.0,<3.0.0  # Using Supabase Python Client v2+
pyjwt[crypto]>=2.4.0,<3.0.0 # For JWT decoding, includes cryptography
email-validator>=2.0.0 # For email validation in Pydantic models

# Background Tasks & Broker

# Utilities
pytz>=2022.1 # For timezone handling

# Add other libraries used by your scheduler_service logic if any
# e.g., numpy, pandas (if doing complex data manipulation - increases image size)

# API and Authentication
fastapi>=0.95.0,<1.0.0
uvicorn[standard]>=0.22.0,<1.0.0 # Assuming [standard] extras are desired with the newer version
pydantic>=1.10.7,<2.0.0
python-dotenv>=1.0.0,<2.0.0

# Async Task Processing
celery>=5.2.7,<6.0.0
redis>=4.5.4,<5.0.0

# Testing
pytest>=7.0.0,<8.0.0
pytest-mock>=3.10.0,<4.0.0

# Monitoring
requests>=2.28.0,<3.0.0
