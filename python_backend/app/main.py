import os
import logging # Import logging
from fastapi import FastAPI, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware # Import CORS middleware
from dotenv import load_dotenv
from typing import Optional, Any, Annotated
from supabase import Client as SupabaseClient # For type hinting

# It's good practice to load environment variables early
load_dotenv()

# Set up a logger for this module
logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO) # Ensure basicConfig is called if not already configured globally

# Import Supabase client and Pydantic models
from .core.supabase_client import get_supabase_client
from .schemas import ClubAdminRegistrationPayload, UserResponse, StatusResponse

# Initialize FastAPI app
app = FastAPI(
    title="Soccer Tournament Management API",
    description="API service for handling complex operations like club admin registration.",
    version="0.1.0"
)

# CORS Middleware Configuration
# Define the origins allowed to make cross-site requests.
# For development, this will be your Flutter web app's address.
origins = [
    "http://localhost:8114", # Flutter dev server (adjust if your port is different)
    "http://127.0.0.1:8114",
    # Add other origins if needed (e.g., your deployed Flutter app URL)
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"], # Allows all methods (GET, POST, OPTIONS, etc.)
    allow_headers=["*"], # Allows all headers
)

# Dependency for Supabase client
async def get_db() -> SupabaseClient:
    client = get_supabase_client()
    if client is None:
        # This will be caught by FastAPI and returned as a 500-level error
        # Or, you can raise HTTPException directly if you want to control the response.
        raise HTTPException(status_code=503, detail="Supabase client not available. Check server logs.")
    return client

# Type alias for the dependency
SupabaseDep = Annotated[SupabaseClient, Depends(get_db)]

@app.get("/health", summary="Health Check", tags=["General"])
async def health_check():
    """
    Simple health check endpoint to confirm the API is running.
    """
    return {"status": "ok", "message": "API is healthy"}

@app.post(
    "/api/v1/register-club-admin",
    response_model=UserResponse,
    responses={
        400: {"model": StatusResponse, "description": "Invalid input"},
        409: {"model": StatusResponse, "description": "Conflict (e.g., email already exists)"},
        500: {"model": StatusResponse, "description": "Internal server error"},
        503: {"model": StatusResponse, "description": "Supabase client not available"}
    },
    summary="Register a new Club Administrator and their Club",
    tags=["Authentication", "Club Management"]
)
async def register_club_admin(
    payload: ClubAdminRegistrationPayload,
    supabase: SupabaseDep
):
    """
    Registers a new club administrator and creates their club.
    This endpoint calls a Supabase RPC function (`create_club_admin_account`)
    using the service role key for necessary permissions.
    """
    new_club_id = None
    new_user_id = None
    try:
        # Step 1: Create the Club
        club_insert_data = {'name': payload.club_name}
        # Note: Supabase automatically adds created_at, updated_at if columns exist and have defaults
        club_creation_response = supabase.table('clubs').insert(club_insert_data).execute()

        if not club_creation_response.data or (hasattr(club_creation_response, 'error') and club_creation_response.error):
            error_msg = "Failed to create club."
            if hasattr(club_creation_response, 'error') and club_creation_response.error:
                error_msg += f" DB error: {club_creation_response.error.message}"
            raise HTTPException(status_code=500, detail={"status": "error", "message": error_msg})
        new_club_id = club_creation_response.data[0]['id']

        # Step 2: Create the Admin User using Supabase Auth Admin API
        # email_confirm: False means an email confirmation will be required.
        try:
            admin_user_attributes = {
                "email": payload.admin_email,
                "password": payload.admin_password,
                "email_confirm": False,
                "user_metadata": {
                    'first_name': payload.admin_first_name,
                    'last_name': payload.admin_last_name,
                    'role': 'club_admin'
                }
            }
            admin_user_auth_response = supabase.auth.admin.create_user(
                attributes=admin_user_attributes
            )
            new_user_id = admin_user_auth_response.user.id

            # Step 2.5: Explicitly send invitation/confirmation email
            # This is added because admin.create_user with email_confirm:False might not auto-send.
            try:
                # Construct the correct redirect URL. For development, this matches Flutter's AuthConstants.
                # In a production environment, this might come from an environment variable.
                # Ensure this matches what's configured in your Supabase Auth settings for allowed redirect URLs.
                redirect_url = "http://localhost:8114/auth/callback" # TODO: Make this configurable for production
                
                logger.info(f"Attempting to send invitation/confirmation email to {payload.admin_email} for user ID {new_user_id} with redirect to {redirect_url}")
                supabase.auth.admin.invite_user_by_email(
                    payload.admin_email,
                    options={"redirect_to": redirect_url}
                )
                logger.info(f"Successfully initiated invitation/confirmation email to {payload.admin_email}")
            except Exception as invite_error:
                # Log this error, but decide if it's critical enough to halt the whole process.
                # For now, we'll log it as a warning and proceed, as the user and club are created.
                # The admin might need to manually resend an invite from Supabase dashboard if this fails.
                logger.warning(f"Failed to send invitation/confirmation email to {payload.admin_email}: {invite_error}", exc_info=True)
                # Optionally, you could add a specific message to the final success response indicating email send failed.
                # For now, we let the main flow continue.

        except Exception as auth_error: # Catches GoTrueAdminApiError (from supabase-py) or other exceptions
            # If user creation fails, we should ideally roll back club creation.
            # For simplicity now, just error out. A more robust solution might delete the club.
            # Example: supabase.table('clubs').delete().eq('id', new_club_id).execute()
            if "User already exists" in str(auth_error) or (hasattr(auth_error, 'message') and "User already registered" in auth_error.message):
                 raise HTTPException(status_code=409, detail={"status": "error", "message": "Email already registered."})
            raise HTTPException(status_code=500, detail={"status": "error", "message": f"Failed to create admin user: {str(auth_error)}"})

        # Step 3: Update the Profile for the Admin User
        # Assumes a trigger (e.g., handle_new_user) has already created a basic profile row
        # when the auth user was created. We now update it with full details.
        profile_update_data = {
            'first_name': payload.admin_first_name,
            'last_name': payload.admin_last_name,
            'role': 'club_admin',
            'managing_club_id': new_club_id
            # Email is usually part of the auth.users and copied by trigger, so not updated here unless necessary.
            # ID is used in .eq() to specify which row to update.
        }
        profile_update_response = supabase.table('profiles').update(profile_update_data).eq('id', new_user_id).execute()

        if hasattr(profile_update_response, 'error') and profile_update_response.error:
            # If profile update fails, attempt to clean up: delete the auth user and the club.
            # This is important for atomicity.
            # Note: These cleanup operations can also fail. More robust transactional logic might be needed for production.
            # logger.error(f"Failed to update profile for {new_user_id}. Attempting rollback of user and club.")
            try:
                supabase.auth.admin.delete_user(new_user_id)
            except Exception as user_delete_error:
                # logger.error(f"Failed to delete auth user {new_user_id} during rollback: {user_delete_error}")
                pass # Log and continue to club deletion attempt
            try:
                supabase.table('clubs').delete().eq('id', new_club_id).execute()
            except Exception as club_delete_error:
                # logger.error(f"Failed to delete club {new_club_id} during rollback: {club_delete_error}")
                pass # Log error

            error_msg = f"Failed to update profile for club admin. DB error: {profile_update_response.error.message}"
            raise HTTPException(status_code=500, detail={"status": "error", "message": error_msg})
        
        # Check if any row was actually updated.
        # .data will be empty if `eq('id', new_user_id)` found no matching row (trigger failed).
        if not profile_update_response.data:
            # This means the trigger likely didn't create the profile, or it was deleted.
            # This is a more critical state. Attempt rollback.
            try:
                supabase.auth.admin.delete_user(new_user_id)
            except Exception as user_delete_error: pass
            try:
                supabase.table('clubs').delete().eq('id', new_club_id).execute()
            except Exception as club_delete_error: pass
            raise HTTPException(status_code=500, detail={"status": "error", "message": "Profile for new user not found for update. Trigger may have failed."})


        # Step 4: Create Club Director Entry
        director_insert_data = {
            'club_id': new_club_id,
            'user_id': new_user_id,
            'status': 'approved' # Auto-approve the creator
        }
        director_creation_response = supabase.table('club_directors').insert(director_insert_data).execute()

        if not director_creation_response.data or (hasattr(director_creation_response, 'error') and director_creation_response.error):
            # More complex rollback needed here (profile, user, club)
            error_msg = "Failed to create club director entry for admin."
            if hasattr(director_creation_response, 'error') and director_creation_response.error:
                 error_msg += f" DB error: {director_creation_response.error.message}"
            raise HTTPException(status_code=500, detail={"status": "error", "message": error_msg})

        return UserResponse(user_id=new_user_id, email=payload.admin_email)

    except HTTPException as http_exc:
        # Re-raise HTTPExceptions that we've explicitly raised
        raise http_exc
    except Exception as e:
        # Catch any other unexpected exceptions during the process
        logger.error(f"Unexpected error in register_club_admin endpoint: {e}", exc_info=True) # Log with stack trace
        # Attempt to clean up if partial creation occurred (optional, can be complex)
        # if new_user_id: supabase.auth.admin.delete_user(new_user_id) # Be careful with this
        # if new_club_id and not new_user_id: supabase.table('clubs').delete().eq('id', new_club_id).execute()
        raise HTTPException(status_code=500, detail={"status": "error", "message": f"An unexpected server error occurred: {str(e)}"})


if __name__ == "__main__":
    import uvicorn
    # This is for local development running `python app/main.py`
    # For production, Gunicorn will be used as per Dockerfile
    uvicorn.run(app, host="0.0.0.0", port=8000, log_level="info")
