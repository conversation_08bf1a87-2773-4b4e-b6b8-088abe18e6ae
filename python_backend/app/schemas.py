from pydantic import BaseModel, EmailStr, Field
from typing import Optional, Dict, Any
import uuid

class ClubAdminRegistrationPayload(BaseModel):
    club_name: str = Field(..., min_length=1, description="Name of the club")
    admin_first_name: str = Field(..., min_length=1, description="First name of the club administrator")
    admin_last_name: str = Field(..., min_length=1, description="Last name of the club administrator")
    admin_email: EmailStr = Field(..., description="Email address of the club administrator")
    admin_password: str = Field(..., min_length=6, description="Password for the club administrator account (min 6 characters)")

class UserResponse(BaseModel):
    user_id: uuid.UUID = Field(..., description="Unique identifier of the created user")
    email: EmailStr = Field(..., description="Email of the created user")
    message: str = Field(default="Club admin and club created successfully. Please check email for confirmation.", description="Success message")

class StatusResponse(BaseModel):
    status: str = Field(..., description="Status of the operation (e.g., 'success', 'error')")
    message: str = Field(..., description="Detailed message about the operation")
    details: Optional[Dict[str, Any]] = Field(default=None, description="Optional additional details")

# Example of a more detailed error response if needed
# class ErrorDetail(BaseModel):
#     loc: Optional[list[str]] = None
#     msg: str
#     type: str

# class HTTPValidationError(BaseModel):
#     detail: list[ErrorDetail]
