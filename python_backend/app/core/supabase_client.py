import os
import logging
from typing import Optional
from supabase import create_client, Client as Supabase<PERSON>lient
# from dotenv import load_dotenv # Removed: main.py should handle loading .env before this module is heavily used.

# Global instance and error tracking
supabase_client_instance: Optional[SupabaseClient] = None
initialization_error: Optional[str] = None

def initialize_supabase_client():
    global supabase_client_instance, initialization_error
    logging.info("Attempting to initialize Supabase client from supabase_client.py...")

    # Read environment variables inside the initialization function
    # This ensures they are read after load_dotenv() in main.py has likely executed
    local_supabase_url: Optional[str] = os.getenv("SUPABASE_URL")
    local_supabase_service_key: Optional[str] = os.getenv("SUPABASE_SERVICE_ROLE_KEY")

    if not local_supabase_url or not local_supabase_service_key:
        initialization_error = "Supabase URL or Service Role Key environment variables not found by supabase_client.py during initialization."
        logging.error(f"CRITICAL: {initialization_error}")
        supabase_client_instance = None # Ensure it's None if init fails
        return

    try:
        supabase_client_instance = create_client(local_supabase_url, local_supabase_service_key)
        logging.info("Supabase client initialized successfully with service role key by supabase_client.py.")
        initialization_error = None # Clear any previous error if successful now
    except Exception as e:
        initialization_error = f"Failed to initialize Supabase client in supabase_client.py: {e}"
        logging.error(initialization_error, exc_info=True)
        supabase_client_instance = None

# Initialize the client when the module is loaded
initialize_supabase_client()

def get_supabase_client() -> Optional[SupabaseClient]:
    """
    Returns the initialized Supabase client instance.
    If initialization failed, this will return None.
    """
    if initialization_error and not supabase_client_instance:
        # This ensures that if initialization failed, subsequent calls don't appear to succeed.
        logging.warning(f"Returning None for Supabase client due to previous initialization error: {initialization_error}")
        return None
    return supabase_client_instance

# Optional: A dependency for FastAPI routes to get the client
# async def get_supabase_dependency() -> SupabaseClient:
#     client = get_supabase_client()
#     if client is None:
#         # This will be caught by FastAPI and returned as a 500-level error
#         # Or, you can raise HTTPException(status_code=503, detail="Supabase client not available")
#         # if you want to control the response more directly from here.
#         raise Exception("Supabase client is not available. Check server logs for initialization errors.")
#     return client
