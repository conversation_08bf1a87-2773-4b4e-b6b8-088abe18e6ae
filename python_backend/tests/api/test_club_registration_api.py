import pytest
from fastapi.testclient import TestClient
from unittest.mock import MagicMock, patch

# Assuming your FastAPI app instance is named 'app' in 'python_backend.app.main'
# and get_db is the dependency providing the Supabase client.
from python_backend.app.main import app, get_db
from python_backend.app.schemas import UserResponse # For response validation

# --- Mock Supabase Client Structures ---

class MockPostgrestResponse:
    """Mocks the response from supabase.table().insert/update/delete().execute()"""
    def __init__(self, data=None, error_dict=None, status_code=200):
        self.data = data
        self.error = None
        if error_dict:
            # Simulate a simple error object structure
            self.error = MagicMock()
            self.error.message = error_dict.get("message", "")
            self.error.code = error_dict.get("code", "")
            self.error.details = error_dict.get("details", "")
            self.error.hint = error_dict.get("hint", "")
        self.status_code = status_code

class MockUser:
    """Mocks the user object returned by supabase.auth.admin.create_user()"""
    def __init__(self, id, email, **kwargs):
        self.id = id
        self.email = email
        for key, value in kwargs.items():
            setattr(self, key, value)

class MockAuthAdminUserResponse:
    """Mocks the response object from supabase.auth.admin.create_user()"""
    def __init__(self, user):
        self.user = user

# --- Pytest Fixtures ---

@pytest.fixture
def mock_supabase_client_success():
    """Mocks a Supabase client that simulates successful operations."""
    client = MagicMock()

    # Mock for table('clubs').insert().execute()
    mock_clubs_table_ops = MagicMock()
    mock_clubs_table_ops.insert().execute.return_value = MockPostgrestResponse(
        data=[{'id': 'test_club_uuid_123'}], status_code=201
    )
    
    # Mock for auth.admin.create_user()
    mock_user = MockUser(id='test_user_uuid_456', email='<EMAIL>')
    client.auth.admin.create_user.return_value = MockAuthAdminUserResponse(user=mock_user)
    
    # Mock for auth.admin.invite_user_by_email() - typically returns None or raises error
    client.auth.admin.invite_user_by_email.return_value = None 
    
    # Mock for table('profiles').update().eq().execute()
    mock_profiles_table_ops = MagicMock()
    mock_profiles_table_ops.update().eq().execute.return_value = MockPostgrestResponse(
        data=[{'id': 'test_user_uuid_456'}], status_code=200 # Supabase update returns data
    )
    
    # Mock for table('club_directors').insert().execute()
    mock_directors_table_ops = MagicMock()
    mock_directors_table_ops.insert().execute.return_value = MockPostgrestResponse(
        data=[{'club_id': 'test_club_uuid_123', 'user_id': 'test_user_uuid_456'}], status_code=201
    )

    # Side effect for client.table(table_name)
    def table_side_effect(table_name_arg):
        if table_name_arg == 'clubs':
            return mock_clubs_table_ops
        elif table_name_arg == 'profiles':
            return mock_profiles_table_ops
        elif table_name_arg == 'club_directors':
            return mock_directors_table_ops
        else:
            # Return a default MagicMock if an unexpected table is called
            return MagicMock() 
            
    client.table.side_effect = table_side_effect
    
    return client

@pytest.fixture
def test_app_client(mock_supabase_client_success):
    """Creates a FastAPI TestClient with the Supabase dependency overridden."""
    app.dependency_overrides[get_db] = lambda: mock_supabase_client_success
    client = TestClient(app)
    yield client
    app.dependency_overrides.clear() # Clean up overrides after test

# --- Test Cases ---

def test_register_club_admin_successful(test_app_client, mock_supabase_client_success):
    """Tests successful club admin registration."""
    payload = {
        "club_name": "Test Club FC",
        "admin_first_name": "Test",
        "admin_last_name": "Admin",
        "admin_email": "<EMAIL>",
        "admin_password": "securepassword123"
    }
    response = test_app_client.post("/api/v1/register-club-admin", json=payload)
    
    assert response.status_code == 200, f"Expected 200 OK, got {response.status_code}. Response: {response.text}"
    data = response.json()
    assert data["user_id"] == "test_user_uuid_456" # From mock_user in fixture
    assert data["email"] == payload["admin_email"]

    # Verify Supabase client method calls
    mock_supabase_client_success.table.assert_any_call('clubs')
    mock_supabase_client_success.table('clubs').insert.assert_called_once_with({'name': payload["club_name"]})
    
    mock_supabase_client_success.auth.admin.create_user.assert_called_once_with(
        attributes={
            "email": payload["admin_email"],
            "password": payload["admin_password"],
            "email_confirm": False,
            "user_metadata": {
                'first_name': payload["admin_first_name"],
                'last_name': payload["admin_last_name"],
                'role': 'club_admin'
            }
        }
    )
    mock_supabase_client_success.auth.admin.invite_user_by_email.assert_called_once_with(payload["admin_email"])
    
    mock_supabase_client_success.table.assert_any_call('profiles')
    # For update, check .eq() was called with the correct user_id
    # The actual update data can be checked if needed by inspecting call_args on the mock_profiles_table_ops.update().execute call
    
    mock_supabase_client_success.table.assert_any_call('club_directors')
    mock_supabase_client_success.table('club_directors').insert.assert_called_once_with({
        'club_id': 'test_club_uuid_123', # From club creation mock
        'user_id': 'test_user_uuid_456', # From user creation mock
        'status': 'approved'
    })

# --- Placeholder for Future Tests ---

# def test_register_club_admin_duplicate_club_name(test_app_client, mock_supabase_client_duplicate_club):
#     # Setup mock_supabase_client_duplicate_club to raise unique constraint on clubs.insert
#     # ...
#     pass

# def test_register_club_admin_duplicate_email(test_app_client, mock_supabase_client_duplicate_email):
#     # Setup mock_supabase_client_duplicate_email to raise error on auth.admin.create_user
#     # ...
#     pass

# def test_register_club_admin_profile_update_fails(test_app_client, mock_supabase_client_profile_fail):
#     # Setup mock_supabase_client_profile_fail to error on profiles.update
#     # ...
#     pass

# def test_register_club_admin_director_insert_fails(test_app_client, mock_supabase_client_director_fail):
#     # Setup mock_supabase_client_director_fail to error on club_directors.insert
#     # ...
#     pass

# def test_register_club_admin_invalid_payload(test_app_client):
#     # Send payload that fails Pydantic validation (e.g., missing fields)
#     # payload = {"club_name": "Test Club"} # Missing other required fields
#     # response = test_app_client.post("/api/v1/register-club-admin", json=payload)
#     # assert response.status_code == 422 # Unprocessable Entity
#     pass

# def test_register_club_admin_supabase_client_unavailable(test_app_client_supabase_down):
#     # Setup test_app_client_supabase_down to make get_db raise HTTPException(503)
#     # ...
#     pass
