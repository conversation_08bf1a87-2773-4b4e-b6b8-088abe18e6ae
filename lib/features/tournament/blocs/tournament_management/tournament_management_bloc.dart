import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:logging/logging.dart';
import 'package:soccer_frontend/features/tournament/services/tournament_api_service.dart';
import 'package:soccer_frontend/data/models/tournament.dart';

part 'tournament_management_event.dart';
part 'tournament_management_state.dart';

class TournamentManagementBloc extends Bloc<TournamentManagementEvent, TournamentManagementState> {
  final TournamentApiService _tournamentApiService;
  final Logger _log = Logger('TournamentManagementBloc');

  TournamentManagementBloc({
    required TournamentApiService tournamentApiService,
  }) : _tournamentApiService = tournamentApiService,
       super(TournamentManagementInitial()) {
    
    on<LoadTournamentManagement>(_onLoadTournamentManagement);
    on<UpdateTournamentStatus>(_onUpdateTournamentStatus);
    on<RefreshTournamentData>(_onRefreshTournamentData);
    on<DeleteTournament>(_onDeleteTournament);
  }

  Future<void> _onLoadTournamentManagement(
    LoadTournamentManagement event,
    Emitter<TournamentManagementState> emit,
  ) async {
    _log.info('Loading tournament management for ID: ${event.tournamentId}');
    emit(TournamentManagementLoading());

    try {
      final tournament = await _tournamentApiService.getTournamentById(event.tournamentId);
      
      // Load additional management data
      final registrationCount = await _getRegistrationCount(event.tournamentId);
      final matchCount = await _getMatchCount(event.tournamentId);
      final venueCount = await _getVenueCount(event.tournamentId);
      
      emit(TournamentManagementLoaded(
        tournament: tournament,
        registrationCount: registrationCount,
        matchCount: matchCount,
        venueCount: venueCount,
        canEdit: _canEditTournament(tournament),
        canDelete: _canDeleteTournament(tournament),
        canSchedule: _canScheduleTournament(tournament),
        canStartRegistration: _canStartRegistration(tournament),
      ));
    } catch (e, s) {
      _log.severe('Error loading tournament management: $e', e, s);
      emit(TournamentManagementError(message: 'Failed to load tournament: $e'));
    }
  }

  Future<void> _onUpdateTournamentStatus(
    UpdateTournamentStatus event,
    Emitter<TournamentManagementState> emit,
  ) async {
    if (state is! TournamentManagementLoaded) return;
    
    final currentState = state as TournamentManagementLoaded;
    _log.info('Updating tournament status from ${currentState.tournament.status} to ${event.newStatus}');
    
    emit(TournamentManagementUpdating(currentState));

    try {
      final updatedTournament = await _tournamentApiService.updateTournament(
        currentState.tournament.id!,
        {'status': event.newStatus},
      );
      
      emit(currentState.copyWith(
        tournament: updatedTournament,
        canEdit: _canEditTournament(updatedTournament),
        canDelete: _canDeleteTournament(updatedTournament),
        canSchedule: _canScheduleTournament(updatedTournament),
        canStartRegistration: _canStartRegistration(updatedTournament),
      ));
      
      _log.info('Tournament status updated successfully');
    } catch (e, s) {
      _log.severe('Error updating tournament status: $e', e, s);
      emit(TournamentManagementError(message: 'Failed to update tournament status: $e'));
    }
  }

  Future<void> _onRefreshTournamentData(
    RefreshTournamentData event,
    Emitter<TournamentManagementState> emit,
  ) async {
    if (state is! TournamentManagementLoaded) return;
    
    final currentState = state as TournamentManagementLoaded;
    add(LoadTournamentManagement(currentState.tournament.id!));
  }

  Future<void> _onDeleteTournament(
    DeleteTournament event,
    Emitter<TournamentManagementState> emit,
  ) async {
    if (state is! TournamentManagementLoaded) return;
    
    final currentState = state as TournamentManagementLoaded;
    _log.info('Deleting tournament: ${currentState.tournament.id}');
    
    emit(TournamentManagementDeleting(currentState));

    try {
      await _tournamentApiService.deleteTournament(currentState.tournament.id!);
      emit(TournamentManagementDeleted());
      _log.info('Tournament deleted successfully');
    } catch (e, s) {
      _log.severe('Error deleting tournament: $e', e, s);
      emit(TournamentManagementError(message: 'Failed to delete tournament: $e'));
    }
  }

  // Helper methods to determine tournament capabilities
  bool _canEditTournament(Tournament tournament) {
    return tournament.status == 'planning' || tournament.status == 'registration_open';
  }

  bool _canDeleteTournament(Tournament tournament) {
    return tournament.status == 'planning';
  }

  bool _canScheduleTournament(Tournament tournament) {
    return tournament.status == 'registration_closed' || tournament.status == 'registration_open';
  }

  bool _canStartRegistration(Tournament tournament) {
    return tournament.status == 'planning';
  }

  // Helper methods to get counts (placeholder implementations)
  Future<int> _getRegistrationCount(String tournamentId) async {
    try {
      // TODO: Implement actual API call to get registration count
      return 0;
    } catch (e) {
      _log.warning('Error getting registration count: $e');
      return 0;
    }
  }

  Future<int> _getMatchCount(String tournamentId) async {
    try {
      // TODO: Implement actual API call to get match count
      return 0;
    } catch (e) {
      _log.warning('Error getting match count: $e');
      return 0;
    }
  }

  Future<int> _getVenueCount(String tournamentId) async {
    try {
      // TODO: Implement actual API call to get venue count
      return 0;
    } catch (e) {
      _log.warning('Error getting venue count: $e');
      return 0;
    }
  }
}
