part of 'tournament_management_bloc.dart';

abstract class TournamentManagementState {
  const TournamentManagementState();
}

class TournamentManagementInitial extends TournamentManagementState {}

class TournamentManagementLoading extends TournamentManagementState {}

class TournamentManagementLoaded extends TournamentManagementState {
  final Tournament tournament;
  final int registrationCount;
  final int matchCount;
  final int venueCount;
  final bool canEdit;
  final bool canDelete;
  final bool canSchedule;
  final bool canStartRegistration;

  const TournamentManagementLoaded({
    required this.tournament,
    required this.registrationCount,
    required this.matchCount,
    required this.venueCount,
    required this.canEdit,
    required this.canDelete,
    required this.canSchedule,
    required this.canStartRegistration,
  });

  TournamentManagementLoaded copyWith({
    Tournament? tournament,
    int? registrationCount,
    int? matchCount,
    int? venueCount,
    bool? canEdit,
    bool? canDelete,
    bool? canSchedule,
    bool? canStartRegistration,
  }) {
    return TournamentManagementLoaded(
      tournament: tournament ?? this.tournament,
      registrationCount: registrationCount ?? this.registrationCount,
      matchCount: matchCount ?? this.matchCount,
      venueCount: venueCount ?? this.venueCount,
      canEdit: canEdit ?? this.canEdit,
      canDelete: canDelete ?? this.canDelete,
      canSchedule: canSchedule ?? this.canSchedule,
      canStartRegistration: canStartRegistration ?? this.canStartRegistration,
    );
  }

  String get statusDisplayName {
    switch (tournament.status) {
      case 'planning':
        return 'Planning';
      case 'registration_open':
        return 'Registration Open';
      case 'registration_closed':
        return 'Registration Closed';
      case 'scheduled':
        return 'Scheduled';
      case 'active':
        return 'Active';
      case 'completed':
        return 'Completed';
      case 'cancelled':
        return 'Cancelled';
      default:
        return tournament.status.toUpperCase();
    }
  }

  List<String> get availableStatusTransitions {
    switch (tournament.status) {
      case 'planning':
        return ['registration_open', 'cancelled'];
      case 'registration_open':
        return ['registration_closed', 'cancelled'];
      case 'registration_closed':
        return ['scheduled', 'registration_open'];
      case 'scheduled':
        return ['active'];
      case 'active':
        return ['completed'];
      case 'completed':
        return [];
      case 'cancelled':
        return [];
      default:
        return [];
    }
  }
}

class TournamentManagementUpdating extends TournamentManagementState {
  final TournamentManagementLoaded previousState;

  const TournamentManagementUpdating(this.previousState);
}

class TournamentManagementDeleting extends TournamentManagementState {
  final TournamentManagementLoaded previousState;

  const TournamentManagementDeleting(this.previousState);
}

class TournamentManagementDeleted extends TournamentManagementState {}

class TournamentManagementError extends TournamentManagementState {
  final String message;

  const TournamentManagementError({required this.message});
}
