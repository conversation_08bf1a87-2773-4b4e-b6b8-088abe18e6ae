part of 'tournament_management_bloc.dart';

abstract class TournamentManagementEvent {
  const TournamentManagementEvent();
}

class LoadTournamentManagement extends TournamentManagementEvent {
  final String tournamentId;

  const LoadTournamentManagement(this.tournamentId);
}

class UpdateTournamentStatus extends TournamentManagementEvent {
  final String newStatus;

  const UpdateTournamentStatus(this.newStatus);
}

class RefreshTournamentData extends TournamentManagementEvent {
  const RefreshTournamentData();
}

class DeleteTournament extends TournamentManagementEvent {
  const DeleteTournament();
}

class EditTournamentRequested extends TournamentManagementEvent {
  const EditTournamentRequested();
}

class ScheduleTournamentRequested extends TournamentManagementEvent {
  const ScheduleTournamentRequested();
}

class StartRegistrationRequested extends TournamentManagementEvent {
  const StartRegistrationRequested();
}

class CloseRegistrationRequested extends TournamentManagementEvent {
  const CloseRegistrationRequested();
}

class ViewRegistrationsRequested extends TournamentManagementEvent {
  const ViewRegistrationsRequested();
}

class ViewScheduleRequested extends TournamentManagementEvent {
  const ViewScheduleRequested();
}

class ManageVenuesRequested extends TournamentManagementEvent {
  const ManageVenuesRequested();
}

class ExportTournamentDataRequested extends TournamentManagementEvent {
  const ExportTournamentDataRequested();
}
