import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:soccer_frontend/features/tournament/blocs/my_tournaments/my_tournaments_bloc.dart';
import 'package:soccer_frontend/features/tournament/services/tournament_api_service.dart';
import 'package:soccer_frontend/features/auth/blocs/auth_bloc.dart';

class DirectorDashboardScreen extends StatelessWidget {
  static const String routeName = '/director-dashboard';

  const DirectorDashboardScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => MyTournamentsBloc(
        tournamentApiService: TournamentApiService(),
      )..add(LoadMyTournaments()),
      child: const DirectorDashboardView(),
    );
  }
}

class DirectorDashboardView extends StatelessWidget {
  const DirectorDashboardView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Tournament Director Dashboard'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              context.read<MyTournamentsBloc>().add(LoadMyTournaments());
            },
            tooltip: 'Refresh',
          ),
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: () {
              context.read<AuthBloc>().add(AuthSignOutRequested());
            },
            tooltip: 'Sign Out',
          ),
        ],
      ),
      body: BlocConsumer<MyTournamentsBloc, MyTournamentsState>(
        listener: (context, state) {
          if (state is MyTournamentsError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
        builder: (context, state) {
          if (state is MyTournamentsLoading) {
            return const Center(child: CircularProgressIndicator());
          } else if (state is MyTournamentsLoaded) {
            return _buildDashboard(context, state);
          } else if (state is MyTournamentsError) {
            return _buildErrorView(context, state);
          }
          return const Center(child: Text('Loading...'));
        },
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          context.go('/create-tournament');
        },
        icon: const Icon(Icons.add),
        label: const Text('Create Tournament'),
      ),
    );
  }

  Widget _buildDashboard(BuildContext context, MyTournamentsLoaded state) {
    if (state.tournaments.isEmpty) {
      return _buildEmptyState(context);
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildStatsOverview(context, state),
          const SizedBox(height: 24),
          _buildTournamentsList(context, state),
        ],
      ),
    );
  }

  Widget _buildStatsOverview(BuildContext context, MyTournamentsLoaded state) {
    final totalTournaments = state.tournaments.length;
    final activeTournaments = state.tournaments.where((t) => 
      t.status == 'active' || t.status == 'registration_open').length;
    final completedTournaments = state.tournaments.where((t) => 
      t.status == 'completed').length;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Overview',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    context,
                    'Total Tournaments',
                    totalTournaments.toString(),
                    Icons.event,
                    Colors.blue,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildStatCard(
                    context,
                    'Active',
                    activeTournaments.toString(),
                    Icons.play_circle,
                    Colors.green,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildStatCard(
                    context,
                    'Completed',
                    completedTournaments.toString(),
                    Icons.check_circle,
                    Colors.grey,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(BuildContext context, String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12.0),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, size: 24, color: color),
          const SizedBox(height: 4),
          Text(
            value,
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: Theme.of(context).textTheme.bodySmall,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildTournamentsList(BuildContext context, MyTournamentsLoaded state) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Your Tournaments',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: state.tournaments.length,
              separatorBuilder: (context, index) => const Divider(),
              itemBuilder: (context, index) {
                final tournament = state.tournaments[index];
                return _buildTournamentCard(context, tournament);
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTournamentCard(BuildContext context, tournament) {
    return ListTile(
      leading: CircleAvatar(
        backgroundColor: _getStatusColor(tournament.status),
        child: Icon(
          _getStatusIcon(tournament.status),
          color: Colors.white,
          size: 20,
        ),
      ),
      title: Text(
        tournament.name,
        style: const TextStyle(fontWeight: FontWeight.w500),
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '${DateFormat.yMMMd().format(tournament.startDate)} - ${DateFormat.yMMMd().format(tournament.endDate)}',
          ),
          const SizedBox(height: 2),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
            decoration: BoxDecoration(
              color: _getStatusColor(tournament.status).withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              _getStatusDisplayName(tournament.status),
              style: TextStyle(
                fontSize: 12,
                color: _getStatusColor(tournament.status),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
      onTap: () {
        context.go('/tournament/${tournament.id}');
      },
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.event_note,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'No Tournaments Yet',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Create your first tournament to get started',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () {
              context.go('/create-tournament');
            },
            icon: const Icon(Icons.add),
            label: const Text('Create Tournament'),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorView(BuildContext context, MyTournamentsError state) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error, size: 64, color: Colors.red),
          const SizedBox(height: 16),
          Text(
            'Error loading tournaments',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 8),
          Text(state.message),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              context.read<MyTournamentsBloc>().add(LoadMyTournaments());
            },
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  // Helper methods
  Color _getStatusColor(String status) {
    switch (status) {
      case 'planning':
        return Colors.grey;
      case 'registration_open':
        return Colors.blue;
      case 'registration_closed':
        return Colors.orange;
      case 'scheduled':
        return Colors.purple;
      case 'active':
        return Colors.green;
      case 'completed':
        return Colors.teal;
      case 'cancelled':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  IconData _getStatusIcon(String status) {
    switch (status) {
      case 'planning':
        return Icons.edit;
      case 'registration_open':
        return Icons.app_registration;
      case 'registration_closed':
        return Icons.lock;
      case 'scheduled':
        return Icons.schedule;
      case 'active':
        return Icons.play_circle;
      case 'completed':
        return Icons.check_circle;
      case 'cancelled':
        return Icons.cancel;
      default:
        return Icons.event;
    }
  }

  String _getStatusDisplayName(String status) {
    switch (status) {
      case 'planning':
        return 'Planning';
      case 'registration_open':
        return 'Registration Open';
      case 'registration_closed':
        return 'Registration Closed';
      case 'scheduled':
        return 'Scheduled';
      case 'active':
        return 'Active';
      case 'completed':
        return 'Completed';
      case 'cancelled':
        return 'Cancelled';
      default:
        return status.toUpperCase();
    }
  }
}
