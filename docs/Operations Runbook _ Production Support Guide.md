\# Operations Runbook / Production Support Guide

\*\*Version:\*\* 1.0  
\*\*Date:\*\* April 28, 2025  
\*\*Related Requirements:\*\* NFR1 (Performance), NFR3 (Reliability), NFR8 (Testability)

\#\# 1\. Introduction

\#\#\# 1.1 Purpose  
This document provides operational procedures and troubleshooting guidance for maintaining and supporting the Soccer Tournament Management Platform in production. It is intended for use by the operations team, SREs, and on-call support personnel responsible for system health and availability.

\#\#\# 1.2 Scope  
This runbook covers monitoring, common alert handling, troubleshooting steps, and routine maintenance procedures for the key components: Flutter Frontend (hosting), Supabase Backend, Python Backend Service (API & Workers), Redis, and Stripe Integration points.

\#\#\# 1.3 Key Contacts & Escalation

\* \*\*Primary On-Call:\*\* \[Name/Team, Contact Method\]  
\* \*\*Secondary On-Call:\*\* \[Name/Team, Contact Method\]  
\* \*\*Development Lead:\*\* \[Name, Contact Method\]  
\* \*\*Supabase Support:\*\* \[Link to Supabase Support Portal/Contact\]  
\* \*\*GCP Support:\*\* \[Link to GCP Support Portal/Contact\]  
\* \*\*Stripe Support:\*\* \[Link to Stripe Support Portal/Contact\]

\*(Escalation Path: Detail the process for escalating unresolved issues, e.g., On-Call \-\> Dev Lead \-\> Vendor Support)\*

\#\# 2\. System Overview

\*(Refer to the Architecture Design Document for a detailed diagram and component descriptions)\*

\* \*\*Frontend:\*\* Flutter Web application, likely hosted on a static web host (e.g., Firebase Hosting, GCP Cloud Storage).  
\* \*\*Backend (Core):\*\* Supabase (Managed PostgreSQL DB, Auth, Realtime, Edge Functions).  
\* \*\*Backend (Services):\*\* Python FastAPI (API) and Celery (Workers) running as containerized services on GCP Cloud Run.  
\* \*\*Cache/Broker:\*\* Redis (likely GCP Memorystore).  
\* \*\*Payments:\*\* Stripe integration via API calls and Webhooks.

\#\# 3\. Monitoring & Alerting

\#\#\# 3.1 Monitoring Dashboards  
\* \*\*GCP Monitoring:\*\* Dashboards for Cloud Run (CPU, Memory, Request Count, Latency, Error Rates \- 5xx), Memorystore (CPU, Memory, Connections).  
\* \*\*Supabase Dashboard:\*\* Monitor database health (CPU, RAM, IOPS, Connections), API request rates, error rates, function execution times.  
\* \*\*Stripe Dashboard:\*\* Monitor payment success rates, webhook delivery rates, API errors.  
\* \*\*(Optional) Application Performance Monitoring (APM):\*\* If integrated (e.g., Sentry, Datadog), use its dashboards for detailed error tracking and performance tracing in both Flutter and Python services.  
\* \*\*(Optional) Celery Monitoring:\*\* Use Flower UI (if deployed) or Celery command-line tools (\`celery \-A celery\_app status\`) to monitor worker status and task queues.

\#\#\# 3.2 Key Metrics & Alerts

| Metric                                  | Component        | Threshold (Example)      | Alert Severity | Action                                                                 |  
| :-------------------------------------- | :--------------- | :----------------------- | :------------- | :--------------------------------------------------------------------- |  
| \*\*API Error Rate (5xx)\*\* | Python FastAPI   | \> 1% over 5 mins         | High           | Investigate API logs (Cloud Logging), check dependencies (DB, Redis). |  
| \*\*API Latency (p95)\*\* | Python FastAPI   | \> 1000ms over 5 mins     | Medium         | Check resource utilization (CPU/Mem), DB query performance, dependencies. |  
| \*\*Worker Error Rate\*\* | Python Celery    | \> 5 failed tasks/hr      | Medium         | Investigate worker logs (Cloud Logging), check task inputs, DB/Redis health. |  
| \*\*Celery Queue Length\*\* | Redis / Celery   | \> 100 pending tasks      | Medium         | Check worker health/scaling. Investigate slow tasks.                   |  
| \*\*Supabase DB CPU Utilization\*\* | Supabase DB      | \> 80% sustained          | High           | Investigate slow queries, consider scaling DB resources.                |  
| \*\*Supabase DB Connections\*\* | Supabase DB      | \> 90% of limit           | High           | Check for connection leaks in services, consider pooling/scaling.      |  
| \*\*Supabase Function Error Rate\*\* | Supabase Functions | \> 5% over 15 mins        | Medium         | Investigate function logs (especially Stripe webhook).                   |  
| \*\*Stripe Webhook Failure Rate\*\* | Stripe / Supabase | \> 5% failure rate        | High           | Check Supabase Function logs (\`stripe-webhook\`), network connectivity.   |  
| \*\*\`webhook\_events\` table \`status=failed\`\*\* | Supabase DB      | Any new rows with status 'failed' | Medium         | Investigate error message in \`webhook\_events\`, potentially retry manually. |  
| \*\*Cloud Run Instance Count\*\* | GCP Cloud Run    | Reaches max instances    | Warning        | Review scaling settings, investigate potential performance bottlenecks.   |  
| \*\*Redis Memory Usage\*\* | Redis            | \> 85%                    | High           | Investigate usage patterns, consider scaling Redis instance.           |

\#\#\# 3.3 Log Aggregation  
\* \*\*Cloud Logging:\*\* Centralized logging for Cloud Run (FastAPI API, Celery Workers). Filter by service name (\`tournament-scheduler-api\`, \`tournament-scheduler-worker\`).  
\* \*\*Supabase Logs:\*\* Access via Supabase Dashboard (Platform \-\> Logs \-\> Postgres Logs, Function Logs, Auth Logs).  
\* \*\*Flutter Frontend:\*\* Client-side errors might be reported via an error tracking service (e.g., Sentry) if configured.

\#\# 4\. Common Alert Handling & Troubleshooting

\#\#\# 4.1 Alert: High API Error Rate (5xx)  
1\.  \*\*Check Cloud Logging:\*\* Filter logs for the \`tournament-scheduler-api\` service around the time of the alert. Look for stack traces or specific error messages.  
2\.  \*\*Check Dependencies:\*\* Verify the status of Supabase DB, Redis (Memorystore), and any other external services the API relies on.  
3\.  \*\*Check Resource Utilization:\*\* Examine CPU/Memory usage for the Cloud Run service. Scale up resources if necessary.  
4\.  \*\*Review Recent Deployments:\*\* Correlate the spike with recent code changes. Consider rollback (\`rollback\_tournament\_scheduler.sh\`) if a recent deployment is suspect.

\#\#\# 4.2 Alert: High Celery Worker Error Rate / Queue Length  
1\.  \*\*Check Worker Logs:\*\* Examine Cloud Logging for the \`tournament-scheduler-worker\` service. Look for specific task failures and tracebacks.  
2\.  \*\*Check Redis:\*\* Ensure the Redis instance (Memorystore) is running and accessible. Check connection counts and memory usage.  
3\.  \*\*Check Supabase DB:\*\* Verify database availability and performance. Slow DB queries can cause tasks to time out or fail.  
4\.  \*\*Inspect Failed Tasks:\*\* If possible (using Celery tools or custom logging), identify the specific task IDs and input parameters causing failures.  
5\.  \*\*Check Worker Resources:\*\* Monitor CPU/Memory usage of the worker instances in Cloud Run. Scale up if necessary.  
6\.  \*\*Review Task Code:\*\* Examine the code for the failing tasks (\`tasks.py\`, \`scheduler\_service...py\`, \`referee\_assignment\_service.py\`) for potential bugs.

\#\#\# 4.3 Alert: Supabase DB High CPU / Connections  
1\.  \*\*Identify Slow Queries:\*\* Use the Supabase Dashboard (SQL Editor \-\> Query Performance or Logs) to identify long-running or frequently executed queries.  
2\.  \*\*Analyze Queries:\*\* Use \`EXPLAIN ANALYZE\` to understand query plans and identify missing indexes or inefficient joins.  
3\.  \*\*Check Connection Pooling:\*\* Ensure backend services (FastAPI/Celery using \`supabase-py\`) are using connection pooling effectively if applicable, or manage connections properly.  
4\.  \*\*Scale Supabase Instance:\*\* If optimization is insufficient, consider upgrading the Supabase project plan for more resources.

\#\#\# 4.4 Alert: Stripe Webhook Failures  
1\.  \*\*Check Stripe Dashboard:\*\* Review the Webhooks section in the Stripe dashboard for delivery attempt details and error messages returned by the endpoint.  
2\.  \*\*Check Supabase Function Logs:\*\* Examine the logs for the \`stripe-webhook\` function in the Supabase Dashboard for specific errors during execution.  
3\.  \*\*Check \`webhook\_events\` Table:\*\* Query the table for events with \`status \= 'failed'\` and examine the \`error\_message\` column.  
4\.  \*\*Verify Webhook Secret:\*\* Ensure the \`STRIPE\_WEBHOOK\_SECRET\` environment variable in the Supabase function settings matches the one configured in Stripe.  
5\.  \*\*Test Endpoint:\*\* Manually send a test webhook from the Stripe dashboard to verify basic connectivity and signature verification.

\#\#\# 4.5 User Reports "Cannot Login"  
1\.  \*\*Check Supabase Auth Status:\*\* Verify Supabase Auth service status on the Supabase status page.  
2\.  \*\*Check User Account:\*\* Ask the user for their email. Check the \`auth.users\` table in Supabase to see if the account exists, is confirmed, and not blocked.  
3\.  \*\*Check Logs:\*\* Review Supabase Auth logs for specific login failures related to the user's email/ID.  
4\.  \*\*Password Reset:\*\* Guide the user through the password reset process (FR1.3).  
5\.  \*\*Frontend Issue:\*\* Check browser console logs for errors if the issue seems client-side. Verify the Flutter app version.

\#\#\# 4.6 User Reports "Scheduling Failed"  
1\.  \*\*Get Tournament ID:\*\* Obtain the specific \`tournament\_id\` from the user (or via admin tools).  
2\.  \*\*Check Job Status:\*\* Use the API (\`GET /schedule/status/{job\_id}\`) if the job ID is known, or query the Celery result backend (Redis) if direct access is available.  
3\.  \*\*Check Worker Logs:\*\* Search Cloud Logging for the relevant \`tournament\_id\` and associated task ID within the \`tournament-scheduler-worker\` logs. Look for errors or specific reasons for failure (e.g., constraint violations, validation errors).  
4\.  \*\*Check Supabase Data:\*\* Verify the input data for the tournament (teams, fields, settings) in the Supabase DB for inconsistencies.  
5\.  \*\*Check Supabase RPCs:\*\* Manually run \`api\_validate\_brackets\` and \`api\_get\_fairness\_report\` for the tournament/groups via the Supabase SQL Editor to check for underlying data issues.

\#\# 5\. Maintenance Procedures

\* \*\*Dependency Updates (Python):\*\*  
    \* Regularly check for updates: \`pip list \--outdated\` or use security scanning tools.  
    \* Update \`requirements.txt\`.  
    \* Test thoroughly in staging: \`run\_tests\_in\_docker.sh\`.  
    \* Deploy updated service image via CI/CD.  
\* \*\*Dependency Updates (Flutter):\*\*  
    \* Regularly check for updates: \`flutter pub outdated\`.  
    \* Update \`pubspec.yaml\`.  
    \* Run \`flutter pub get\`.  
    \* Test thoroughly (unit, widget, integration, E2E).  
    \* Build and deploy updated web application.  
\* \*\*Supabase Schema Migrations:\*\*  
    \* Develop changes locally using Supabase CLI (\`supabase migration new ...\`).  
    \* Test migrations against a staging Supabase project.  
    \* Apply migrations to production (\`supabase migration up\`) during a maintenance window or using a blue/green strategy if possible. Coordinate with backend/frontend deployments if schema changes require code updates.  
\* \*\*Supabase Function Updates:\*\*  
    \* Update function code (\`supabase/functions/...\`).  
    \* Deploy using \`supabase functions deploy \<function\_name\>\`.  
\* \*\*System Backups:\*\*  
    \* Supabase provides automated backups. Verify backup frequency and retention policy meet requirements. Understand the Point-in-Time Recovery (PITR) capabilities.  
    \* Consider periodic manual backups for critical milestones if needed.  
\* \*\*Log Rotation/Archiving:\*\* Configure log retention policies in GCP Cloud Logging and Supabase according to compliance and operational needs.  
\* \*\*Audit Log Management:\*\* Implement a strategy (e.g., periodic archival to cold storage or deletion) for the \`audit\_log\` table to prevent excessive growth.

\#\# 6\. Deployment and Rollback

\* \*\*Deployment:\*\* Follow the steps outlined in \`DEPLOYMENT\_CHECKLIST.md\` and utilize the provided deployment scripts (\`deploy\_\*.sh\`). Preferably automated via CI/CD pipeline (\`.github/workflows/deploy\_gcp.yaml\`).  
\* \*\*Rollback:\*\*  
    \* \*\*Python Service:\*\* Use Cloud Run's revision management to quickly revert to a previous stable container image version. The \`rollback\_tournament\_scheduler.sh\` script provides a more manual approach if needed.  
    \* \*\*Flutter Frontend:\*\* Revert to a previous build artifact in the static hosting provider.  
    \* \*\*Supabase Schema:\*\* Requires careful planning. Restore from backup (data loss possible since last backup) or apply a "down" migration script if one was created alongside the "up" migration.  
    \* \*\*Supabase Functions:\*\* Redeploy the previous version using \`supabase functions deploy \<function\_name\> \--version \<previous\_version\_hash\>\`.

\---

