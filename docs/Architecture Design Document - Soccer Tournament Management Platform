**Architecture Design Document** printed out in Markdown:

---

# Architecture Design Document - Soccer Tournament Management Platform

**Version:** 3.0  
**Date:** April 28, 2025  
**Based On:** Analysis of project codebase including Flutter frontend, Python backend (FastAPI/Celery), Supabase configuration/SQL, deployment scripts, and documentation (107 files reviewed).

---

## 1. Introduction

### 1.1 Purpose
This document provides a high-level overview of the system architecture for the Soccer Tournament Management Platform. It outlines the major components, their interactions, technology choices, key data flows, and significant design patterns used. It serves as a foundational reference for developers, operations personnel, and stakeholders to understand the system's structure and design principles.

### 1.2 Scope
The architecture encompasses the entire platform designed for managing soccer tournaments, including:
- **Frontend:** A Flutter-based web application (with potential for mobile extension) providing the user interface for all roles.
- **Backend (Core/BaaS):** Supabase:
  - User Authentication (Supabase Auth/GoTrue)
  - Primary Data Persistence (PostgreSQL database)
  - Real-time data synchronization (Supabase Realtime)
  - Serverless functions (Supabase Edge Functions, e.g., Stripe webhook handling)
  - Row Level Security (RLS) for data access control
- **Backend (Services):** A dedicated Python microservice responsible for:
  - **API Server (FastAPI):** Task endpoints, JWT validation
  - **Task Queue (Celery):** Background job execution
  - **Core Logic Modules:** Scheduling, constraint checking, pairing, fairness analysis, referee assignment
- **Supporting Infrastructure:**
  - **Redis:** Message broker and result backend
  - **Docker:** Containerization
  - **GCP Cloud Run:** Hosting for Python services
- **Third-Party Integrations:** Stripe payment processing

### 1.3 Goals and Objectives
- Scalable, reliable platform for tournament management
- Automated, constraint-aware scheduling and referee assignment
- User-role tailored interfaces
- Secure data and payment handling
- Real-time updates for matches and standings
- Efficient development, testing, and deployment

### 1.4 Non-Goals
- Detailed UI/UX designs
- Deep algorithmic research papers
- Multi-sport support
- Full offline mode beyond basic caching
- Live collaborative editing (multi-user schedule editing)

### 1.5 Glossary
*(Refer to SRS Section 1.4)*

### 1.6 References
- Software Requirements Specification (SRS) V5.0
- Detailed Design Documents (Auth, Scheduling, Referee Assignment, Payments)
- Code Repository Structure
- Supabase SQL Schema
- Python and Flutter Source Code
- Deployment Scripts

### 1.7 Overview
This document details the architectural decisions, major components, their interactions, technology stack, design patterns, deployment view, and potential future evolution.

---

## 2. Architectural Goals and Constraints

### 2.1 Goals (Quality Attributes)
- **Scalability**
- **Reliability & Availability**
- **Maintainability**
- **Security**
- **Performance**
- **Usability**
- **Testability**

### 2.2 Constraints
- **Frontend:** Flutter (Web-first, mobile-capable)
- **Core Backend:** Supabase (Postgres, Auth, Realtime, Edge Functions)
- **Auxiliary Backend:** Python (FastAPI API, Celery Workers)
- **Infrastructure:** GCP (Cloud Run, Memorystore), Docker
- **External Services:** Stripe for payment processing
- **Development Resources:** Expertise of current team

---

## 3. System Overview and Components

### 3.1 Architecture Diagram (Conceptual)

```mermaid
graph TD
    subgraph User_Layer
        User[User (Admin, TD, Coach, etc.)] --> Flutter_App{Flutter Web/Mobile App}
    end
    subgraph Frontend_Layer
        Flutter_App
    end
    subgraph Backend_Layer
        subgraph Supabase [Supabase BaaS]
            SB_Auth[Auth (GoTrue)]
            SB_DB[(PostgreSQL DB + RLS + Functions)]
            SB_Realtime[Realtime]
            SB_Functions[Edge Functions]
            SB_Storage[(Storage)]
        end
        subgraph Python_Service [Python Backend Service]
            FastAPI[FastAPI API]
            Celery_Workers[Celery Workers]
            Redis[(Redis Broker)]
        end
    end
    subgraph External_Services
        Stripe[Stripe API]
    end
    Flutter_App -- HTTPS/REST --> FastAPI
    Flutter_App -- HTTPS/WebSockets --> Supabase
    FastAPI -- AMQP/Redis --> Celery_Workers
    FastAPI -- TCP/IP --> Redis
    FastAPI -- Supabase SDK --> SB_DB
    FastAPI -- Supabase SDK --> SB_Auth
    Celery_Workers -- AMQP/Redis --> Redis
    Celery_Workers -- Supabase SDK --> SB_DB
    SB_Functions -- HTTPS --> Stripe
    Stripe -- HTTPS (Webhook) --> SB_Functions
```

### 3.2 Component Descriptions

- **Flutter Frontend:**  
  - Dart, Flutter SDK, Bloc, GoRouter, supabase-flutter SDK
  - Handles UI, state management, user interactions, API communication

- **Supabase Backend-as-a-Service (BaaS):**  
  - PostgreSQL (with RLS policies)
  - GoTrue Authentication
  - Realtime WebSocket updates
  - Edge Functions (e.g., Stripe webhook handling)

- **Python Backend Service:**  
  - FastAPI for API endpoints
  - Celery workers for asynchronous scheduling/referee assignment
  - Supabase SDK integration with service role

- **Redis:**  
  - AMQP task broker for Celery
  - Low-latency communication for task results

- **Stripe:**  
  - Payment processing
  - Webhook-based event handling through Supabase Edge Functions

---

## 4. Component Interactions and Data Flow

- **Flutter → Supabase:** Authentication, CRUD operations, Realtime subscriptions
- **Flutter → FastAPI:** Task triggering and status polling
- **FastAPI → Celery → Redis → Worker:** Asynchronous background processing
- **Python → Supabase:** Service Role-secured data operations
- **Stripe → Supabase:** Event-driven payment workflows

---

## 5. Technology Choices Rationale

- **Flutter:** Cross-platform UI, single codebase
- **Supabase:** Full-featured BaaS (Auth, Postgres, Realtime)
- **Python/FastAPI/Celery:** Fast, reliable API and task handling
- **Redis:** Fast broker for distributed tasks
- **Stripe:** Secure, developer-friendly payments
- **Docker + GCP Cloud Run:** Scalable deployment model

---

## 6. Key Design Patterns and Considerations

- **Backend-for-Frontend (BFF - partial)**
- **Asynchronous Task Queue (Celery/Redis)**
- **Hybrid Microservices Architecture**
- **Repository Pattern (Flutter)**
- **Dependency Injection (FastAPI/Flutter)**
- **Environment Configuration Management**
- **Database-centric Business Logic (Supabase SQL)**

---

## 7. Deployment View

- **Frontend:**  
  - `flutter build web`
  - Hosted on GCP Cloud Storage, Firebase, or Netlify

- **Backend:**  
  - Dockerized FastAPI + Celery
  - Hosted on GCP Cloud Run
  - Connected to Redis (Memorystore)

- **Database (Supabase):**  
  - SQL migrations via CLI
  - Functions deployed via Supabase CLI

- **CI/CD:**  
  - GitHub Actions for testing and deployments

---

## 8. Future Considerations / Evolution

- Decompose Python service into multiple microservices
- Event-driven architecture (Postgres triggers, pub/sub)
- API Gateway introduction (e.g., GCP API Gateway)
- Full observability (OpenTelemetry tracing)

---

