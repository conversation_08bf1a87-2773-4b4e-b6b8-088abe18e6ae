\# Software Requirements Specification (SRS) \- V5.0 (Enhanced)

\*\*Project:\*\* Soccer Tournament Management Platform

\*\*Version:\*\* 5.0

\*\*Date:\*\* April 28, 2025

\*\*Table of Contents:\*\*

1\.  \*\*Introduction\*\*  
    \*   1.1 Purpose  
    \*   1.2 Scope  
    \*   1.3 Target Audience  
    \*   1.4 Definitions, Acronyms, and Abbreviations (Glossary)  
    \*   1.5 Document Conventions  
    \*   1.6 References  
    \*   1.7 Overview  
2\.  \*\*Overall Description\*\*  
    \*   2.1 Product Perspective  
    \*   2.2 Product Features (Summary)  
    \*   2.3 User Classes and Characteristics  
        \*   2.3.1 System Administrator  
        \*   2.3.2 Tournament Director (TD)  
        \*   2.3.3 Club Administrator  
        \*   2.3.4 Referee  
        \*   2.3.5 Coach / Team Manager  
        \*   2.3.6 Player Parent / Player  
        \*   2.3.7 Unauthenticated User  
    \*   2.4 Operating Environment  
    \*   2.5 Design and Implementation Constraints  
    \*   2.6 Assumptions and Dependencies  
3\.  \*\*System Features (Functional Requirements)\*\*  
    \*   3.1 Authentication and Authorization (FR1)  
    \*   3.2 Profile Management (FR2)  
    \*   3.3 Club Management (FR3)  
    \*   3.4 Tournament Setup and Configuration (FR4)  
    \*   3.5 Team Registration and Rostering (FR5)  
    \*   3.6 Bracket Validation and Fairness Analysis (FR6)  
    \*   3.7 Scheduling (Group Stage & Playoffs) (FR7)  
    \*   3.8 Referee Assignment (FR8)  
    \*   3.9 Score Reporting and Standings (FR9)  
    \*   3.10 Payment Processing (Stripe) (FR10)  
    \*   3.11 Administration Panel (FR11)  
    \*   3.12 Audit Logging (FR12)  
    \*   3.13 Notifications (Implied Requirement) (FR13)  
4\.  \*\*External Interface Requirements\*\*  
    \*   4.1 User Interface (Flutter Frontend)  
    \*   4.2 Backend API (FastAPI Service)  
    \*   4.3 Supabase API (Database, Auth, Functions)  
    \*   4.4 Stripe API (Payments)  
5\.  \*\*Non-Functional Requirements\*\*  
    \*   5.1 Performance (NFR1)  
    \*   5.2 Security (NFR2)  
    \*   5.3 Reliability (NFR3)  
    \*   5.4 Usability (NFR4)  
    \*   5.5 Maintainability (NFR5)  
    \*   5.6 Scalability (NFR6)  
    \*   5.7 Portability (NFR7)  
    \*   5.8 Testability (NFR8)  
    \*   5.9 Deployment (NFR9)  
    \*   5.10 Legal and Compliance (NFR10)  
6\.  \*\*Data Model Overview\*\*  
    \*   6.1 Key Entities  
    \*   6.2 Relationships  
7\.  \*\*User Role Perspectives\*\*  
    \*   7.1 System Administrator Perspective  
    \*   7.2 Tournament Director Perspective  
    \*   7.3 Club Administrator Perspective  
    \*   7.4 Referee Perspective  
    \*   7.5 Coach / Team Manager Perspective  
    \*   7.6 Player Parent / Player Perspective  
8\.  \*\*Appendices\*\*  
    \*   A. Traceability Matrix  
    \*   B. Referenced Documents (List of \`.md\`, \`.sql\`, etc.)

\---

\#\# 1\. Introduction

\#\#\# 1.1 Purpose  
This SRS defines the requirements for the Soccer Tournament Management Platform. It serves as the definitive guide for developers, testers, project managers, and stakeholders regarding the system's expected functionality, interfaces, performance, constraints, and quality attributes.

\#\#\# 1.2 Scope  
The platform is a web-centric application suite designed to manage youth soccer tournaments from initial setup through completion. It encompasses user registration/authentication, club/team management, tournament creation, automated scheduling (group stage, playoffs), automated referee assignment, payment processing (via Stripe), real-time score tracking, automated standings, bracket visualization, and administrative oversight. The system utilizes a \*\*Flutter frontend (primary)\*\*, a Supabase backend (Database, Auth, Functions), and a separate Python backend service (FastAPI/Celery on GCP) for complex computations. Legacy React components exist but are outside the scope of the primary target application defined herein.

\#\#\# 1.3 Target Audience  
Stakeholders including Tournament Organizers, Club Administrators, Referees, Coaches, Team Managers, Parents, Players, System Administrators, Developers, and QA Testers.

\#\#\# 1.4 Definitions, Acronyms, and Abbreviations (Glossary)  
\*   \*\*API:\*\* Application Programming Interface  
\*   \*\*AR:\*\* Assistant Referee  
\*   \*\*Bloc:\*\* Business Logic Component (Flutter State Management)  
\*   \*\*Celery:\*\* Distributed Task Queue (Python)  
\*   \*\*CI/CD:\*\* Continuous Integration / Continuous Deployment  
\*   \*\*CRUD:\*\* Create, Read, Update, Delete  
\*   \*\*DB:\*\* Database  
\*   \*\*FastAPI:\*\* Python Web Framework  
\*   \*\*FR:\*\* Functional Requirement  
\*   \*\*GCP:\*\* Google Cloud Platform  
\*   \*\*GoRouter:\*\* Flutter Navigation Package  
\*   \*\*GUI:\*\* Graphical User Interface  
\*   \*\*JWT:\*\* JSON Web Token  
\*   \*\*NFR:\*\* Non-Functional Requirement  
\*   \*\*PKCE:\*\* Proof Key for Code Exchange (OAuth 2.0)  
\*   \*\*RLS:\*\* Row Level Security  
\*   \*\*RPC:\*\* Remote Procedure Call  
\*   \*\*SRS:\*\* Software Requirements Specification  
\*   \*\*Supabase:\*\* Backend-as-a-Service Platform  
\*   \*\*TD:\*\* Tournament Director  
\*   \*\*UI:\*\* User Interface  
\*   \*\*UX:\*\* User Experience  
\*   \*\*Webhook:\*\* Automated message sent when something happens.

\#\#\# 1.5 Document Conventions  
Requirements are identified with unique IDs (e.g., \`FR1.1\`, \`NFR2.3\`). The term "system" refers to the entire platform unless otherwise specified. The term "user" refers to any individual interacting with the system. Flutter is assumed as the primary frontend unless explicitly stated otherwise.

\#\#\# 1.6 References  
All uploaded files (code, SQL, \`.md\`, \`.yaml\`, \`.json\`, etc. \- 107 total) serve as reference material. Key documents include \`supabase\_full\_schema\_and\_logic\_production\_v2.sql\`, \`scheduler\_service\_production\_v2\_final.py\`, \`scheduler\_api\_production\_v2.py\`, \`Flutter lib/\`, various READMEs and API docs.

\#\#\# 1.7 Overview  
Section 2 describes the overall system context. Section 3 details specific functional requirements. Section 4 outlines external interfaces. Section 5 defines non-functional quality attributes. Section 6 gives a data model overview. Section 7 provides user perspectives. Section 8 contains appendices.

\---

\#\# 2\. Overall Description

\#\#\# 2.1 Product Perspective  
The platform is a modern, cloud-native system replacing manual processes for soccer tournament management. It integrates a user-friendly Flutter frontend with robust backend services hosted on Supabase and GCP.

\#\#\# 2.2 Product Features (Summary)  
See section 1.2 Scope. Key differentiators include automated, constraint-based scheduling and referee assignment, integrated payments, real-time updates, and comprehensive role management.

\#\#\# 2.3 User Classes and Characteristics  
\*   \*\*2.3.1 System Administrator:\*\* Technical user responsible for system configuration, user management (all roles), club verification, monitoring, troubleshooting, and potentially manual data correction. Requires high-level system understanding.  
\*   \*\*2.3.2 Tournament Director (TD):\*\* Primary organizer role. Creates/configures tournaments for their affiliated club(s), manages tournament settings (incl. bracket rules), oversees team registration, triggers/reviews scheduling and referee assignment, manages live tournament operations (score validation, overrides), views dashboards. Requires understanding of tournament rules and platform workflow.  
\*   \*\*2.3.3 Club Administrator:\*\* Manages club profile, verifies club coaches/managers, potentially oversees team creation/registration within their club. Requires administrative permissions for their club.  
\*   \*\*2.3.4 Referee:\*\* Views assigned matches, potentially submits scores electronically, manages availability. Requires basic system navigation skills.  
\*   \*\*2.3.5 Coach / Team Manager:\*\* Registers team (if allowed), manages team roster, views schedule/standings for their team, potentially submits scores. Needs access related to their specific team(s).  
\*   \*\*2.3.6 Player Parent / Player:\*\* Views schedules/standings relevant to their associated player/team, manages own profile (if registered). Primarily view-only access related to specific teams/players.  
\*   \*\*2.3.7 Unauthenticated User:\*\* Can access public landing/login pages, potentially view public tournament information (TBD).

\#\#\# 2.4 Operating Environment  
Flutter frontend targets modern Web browsers (Chrome, Firefox, Safari, Edge) primarily; potential for iOS/Android native apps. Backend hosted on Supabase (global regions) and GCP (specific region, e.g., \`us-east1\`). Requires stable internet connection.

\#\#\# 2.5 Design and Implementation Constraints  
\*   Must use specified tech stack: Flutter/Dart, Supabase (Postgres, Auth, Functions), Python/FastAPI/Celery, Redis, Docker, GCP Cloud Run, Stripe.  
\*   Adherence to Supabase RLS for data security is critical.  
\*   Python backend services must be containerized for deployment.  
\*   Scheduling/Assignment logic must be processed asynchronously via Celery.  
\*   API communication requires JWT authentication.

\#\#\# 2.6 Assumptions and Dependencies  
\*   \*\*Assumptions:\*\*  
    \*   Users have basic computer/web skills.  
    \*   Tournament directors have complete setup data (teams, divisions, fields).  
    \*   Tournaments typically occur over short durations (e.g., weekends).  
    \*   Default youth soccer rules (e.g., 3-1-0 points) are acceptable unless customized.  
    \*   \*\*Clarification:\*\* The primary link between a Club Admin and their club is via the \`profiles.managing\_club\_id\` field. Tournament Directors affiliate with clubs via the \`club\_directors\` table, allowing multiple directors per club and requiring an approval status.  
\*   \*\*Dependencies:\*\*  
    \*   Stable APIs from Supabase and Stripe.  
    \*   Reliable GCP infrastructure (Cloud Run, Memorystore).  
    \*   Users have appropriate devices/browsers.  
    \*   Necessary secrets (API keys, JWT secret) are securely managed and accessible to relevant services.  
    \*   Email delivery configured within Supabase for auth emails.

\---

\#\# 3\. System Features (Functional Requirements)

\#\#\# 3.1 Authentication and Authorization (FR1)  
\*   \*\*FR1.1 User Registration:\*\* Priority: \*\*High\*\*. Users can register via email/password (Flutter \`auth\_screen.dart\`). Email confirmation required. Role selection during registration (Club Admin role uses \`club\_registration\_screen.dart\`). Supabase Auth handles user creation. \`handle\_new\_user\` trigger creates corresponding \`profiles\` entry.  
\*   \*\*FR1.2 User Login:\*\* Priority: \*\*High\*\*. Users can log in using email/password (Supabase Auth).  
\*   \*\*FR1.3 Password Reset:\*\* Priority: \*\*High\*\*. Users can request password reset via email. Flow involves email link click, callback handling (\`callback\_screen.dart\`), and secure password update (\`reset\_password\_screen.dart\`).  
\*   \*\*FR1.4 Email Confirmation:\*\* Priority: \*\*High\*\*. New users receive confirmation link. Callback (\`callback\_screen.dart\`) validates token, updates user status in Supabase Auth. Redirect to \`confirmation\_success\_screen.dart\`.  
\*   \*\*FR1.5 Secure Callback Handling:\*\* Priority: \*\*High\*\*. System uses \`/auth/callback\` route (\`callback\_screen.dart\`) to securely handle redirects from Supabase for PKCE code exchange, email confirmation, and password recovery. Uses \`AuthNotifier\` and GoRouter redirects. Robust handling of different callback types and potential errors (\`InvalidLinkScreen\`).  
\*   \*\*FR1.6 Session Management:\*\* Priority: \*\*High\*\*. Supabase manages session persistence (JWT refresh tokens). Flutter app handles session state via \`AuthNotifier\`.  
\*   \*\*FR1.7 Logout:\*\* Priority: \*\*High\*\*. Users can log out, invalidating the local session (Flutter, \`AuthBloc\`/\`AuthNotifier\`). Supabase handles server-side token invalidation where applicable.  
\*   \*\*FR1.8 Rate Limiting:\*\* Priority: \*\*Medium\*\*. Login/registration/password reset attempts are rate-limited (Flutter \`RateLimiter\`, Supabase server-side limits).  
\*   \*\*FR1.9 Role-Based Access Control (RBAC):\*\* Priority: \*\*High\*\*. Access to data and features is controlled by user roles.  
    \*   \*\*FR1.9.1 RLS Policies:\*\* Supabase RLS policies (\`supabase\_\*.sql\` files) restrict direct database access based on \`auth.uid()\`, user roles (\`profiles.role\`), and affiliations (\`club\_directors\`, \`team\_staff\`, \`profiles.managing\_club\_id\`).  
    \*   \*\*FR1.9.2 API Authorization:\*\* FastAPI endpoints (\`scheduler\_api\_\*.py\`) verify JWT tokens (\`security.py\`) and check for required roles.  
    \*   \*\*FR1.9.3 UI Control:\*\* Flutter UI elements (\`AppDrawer\`, Buttons) are shown/hidden based on user role (\`AuthNotifier\`). GoRouter redirects unauthorized access attempts (\`router.dart\`).  
\*   \*\*FR1.10 JWT Handling:\*\* Priority: \*\*High\*\*. System uses Supabase-issued JWTs. Flutter client stores tokens securely. FastAPI backend validates tokens using \`SUPABASE\_JWT\_SECRET\`.

\#\#\# 3.2 Profile Management (FR2)  
\*   \*\*FR2.1 View Profile:\*\* Priority: \*\*High\*\*. Authenticated users can view their own profile information (\`profile\_screen.dart\`).  
\*   \*\*FR2.2 Edit Profile:\*\* Priority: \*\*High\*\*. Users can edit mutable profile fields (e.g., first/last name, phone) (\`edit\_profile\_screen.dart\`, \`ProfileBloc\`). Updates saved to \`profiles\` table via \`AuthService\`/\`AuthNotifier\`.

\#\#\# 3.3 Club Management (FR3)  
\*   \*\*FR3.1 Club Admin Registration:\*\* Priority: \*\*High\*\*. Dedicated signup flow (\`club\_registration\_screen.dart\` in Flutter) calls a backend API endpoint (\`/api/v1/register-club-admin\` in the Python FastAPI service). This backend service then uses the Supabase client with service role privileges to create the admin user, their profile, the new club, and associate them.  
\*   \*\*FR3.2 Club Verification:\*\* Priority: \*\*Medium\*\*. System Admins can verify/approve clubs (DB field \`clubs.is\_verified\`, requires Admin UI action in \`clubs\_screen.dart\`). RLS policies may depend on verification status.  
\*   \*\*FR3.3 Club Affiliation Management:\*\* Priority: \*\*Medium\*\*. Association between TDs and Clubs (\`club\_directors\` table). TDs request affiliation, Club Admins/Admins approve/deny/invite (\`ProfileScreen\`, \`Admin/Club Admin UI\`, RPCs).

\#\#\# 3.4 Tournament Setup and Configuration (FR4)  
\*   \*\*FR4.1 Tournament Creation:\*\* Priority: \*\*High\*\*. Approved TDs create tournaments via wizard (\`tournament\_wizard\_screen.dart\`), defining name, dates, location, etc. Director must select an \*approved\* affiliated club (\`club\_directors\`) or indicate unaffiliated (may require payment per FR10). Club association stored in \`tournaments.managing\_club\_id\`.  
\*   \*\*FR4.2 Tournament Configuration:\*\* Priority: \*\*High\*\*. TDs configure settings per tournament/age group (\`tournament\_age\_groups.custom\_settings\`). Includes game timing, bracket validation rules, fairness rules, playoff format, scheduling constraints.  
\*   \*\*FR4.3 Field/Venue Management:\*\* Priority: \*\*High\*\*. Admins define venues and fields (\`fields\`, \`venues\` tables).

\#\#\# 3.5 Team Registration and Rostering (FR5)  
\*   \*\*FR5.1 Team Registration:\*\* Priority: \*\*High\*\*. Club Admins/Coaches register teams for specific tournament age groups (\`registrations\` table).  
\*   \*\*FR5.2 Roster Management:\*\* Priority: \*\*High\*\*. Coaches/Managers add players to team rosters (\`team\_members\` table).  
\*   \*\*FR5.3 Age Validation:\*\* Priority: \*\*High\*\*. System enforces age constraints based on \`tournament\_age\_groups.age\_u\` and \`players.date\_of\_birth\`.

\#\#\# 3.6 Bracket Validation and Fairness Analysis (FR6)  
\*   \*\*FR6.1 Bracket Size Validation:\*\* Priority: \*\*High\*\*. System validates group sizes before scheduling (\`api\_validate\_brackets\` RPC). Returns errors/warnings.  
\*   \*\*FR6.2 Fairness Analysis:\*\* Priority: \*\*Medium\*\*. System analyzes fairness metrics after scheduling (\`api\_get\_fairness\_report\` RPC).  
\*   \*\*FR6.3 Reporting:\*\* Priority: \*\*Medium\*\*. Validation/Fairness reports presented to TD in UI (\`FairnessAnalysisPanel.txt\`).

\#\#\# 3.7 Scheduling (Group Stage & Playoffs) (FR7)  
\*   \*\*FR7.1 Async Scheduling Trigger:\*\* Priority: \*\*High\*\*. TD triggers via UI, calls FastAPI \`POST /schedule\`.  
\*   \*\*FR7.2 Data Fetching (Task):\*\* Priority: \*\*High\*\*. Celery task fetches data from Supabase.  
\*   \*\*FR7.3 Time Slot Generation (Task):\*\* Priority: \*\*High\*\*. Task generates available slots.  
\*   \*\*FR7.4 Group Stage Pairings (Task):\*\* Priority: \*\*High\*\*. Task generates round-robin pairings.  
\*   \*\*FR7.5 Constraint Checking (Task):\*\* Priority: \*\*High\*\*. Task assigns matches, checks constraints.  
\*   \*\*FR7.6 Playoff Advancement (DB):\*\* Priority: \*\*High\*\*. Uses \`determine\_advancing\_teams\` RPC.  
\*   \*\*FR7.7 Playoff Seeding (DB):\*\* Priority: \*\*High\*\*. Uses \`seed\_playoff\_bracket\` procedure.  
\*   \*\*FR7.8 Schedule Saving (Task):\*\* Priority: \*\*High\*\*. Task saves matches to \`matches\` table.  
\*   \*\*FR7.9 Job Status Tracking:\*\* Priority: \*\*High\*\*. Frontend polls \`GET /schedule/status/{job\_id}\`.  
\*   \*\*FR7.10 Schedule Viewing:\*\* Priority: \*\*High\*\*. Users view schedules (\`SchedulingScreen\`, \`RealtimeTournamentScreen\`).  
\*   \*\*FR7.11 Placeholder Text Handling:\*\* Priority: \*\*High\*\*. UI must correctly display placeholder text (e.g., "Winner QF1") until actual teams are determined.

\#\#\# 3.8 Referee Assignment (FR8)  
\*   \*\*FR8.1 Async Assignment Trigger:\*\* Priority: \*\*High\*\*. Triggered by backend task.  
\*   \*\*FR8.2 Data Fetching (Task):\*\* Priority: \*\*High\*\*. Task fetches matches, referees, availability.  
\*   \*\*FR8.3 Assignment Algorithm:\*\* Priority: \*\*High\*\*. Python service assigns referees.  
\*   \*\*FR8.4 Assignment Saving (Task):\*\* Priority: \*\*High\*\*. Task saves to \`match\_assignments\`.  
\*   \*\*FR8.5 Workload Analysis:\*\* Priority: \*\*Medium\*\*. Python service analyzes distribution.  
\*   \*\*FR8.6 View Assignments:\*\* Priority: \*\*High\*\*. Referees view via \`RefereesScreen\`.

\#\#\# 3.9 Score Reporting and Standings (FR9)  
\*   \*\*FR9.1 Score Entry:\*\* Priority: \*\*High\*\*. Authorized users enter scores via UI (Needs Implementation). \`update\_match\_score\_manual\` RPC used.  
\*   \*\*FR9.2 Standings Calculation:\*\* Priority: \*\*High\*\*. \`refresh\_standings\` procedure triggered automatically.  
\*   \*\*FR9.3 Tiebreaker Logic:\*\* Priority: \*\*High\*\*. Incorporated into \`determine\_advancing\_teams\` function.  
\*   \*\*FR9.4 Real-time Updates:\*\* Priority: \*\*Medium\*\*. UI (\`RealtimeTournamentScreen\`) uses Supabase Realtime.  
\*   \*\*FR9.5 Manual Overrides:\*\* Priority: \*\*Medium\*\*. TD/Admin UI calls override RPCs.

\#\#\# 3.10 Payment Processing (Stripe) (FR10)  
\*   \*\*FR10.1 Payment Initiation:\*\* Priority: \*\*Medium\*\*. User initiates via UI (TBD).  
\*   \*\*FR10.2 Stripe Webhook Handling:\*\* Priority: \*\*High\*\*. Supabase Edge Function handles Stripe events.  
\*   \*\*FR10.3 Director Membership Fee:\*\* Priority: \*\*Low\*\*. Payment required for unaffiliated TD creating tournament (Uses \`tournaments.creation\_payment\_status\`). Currently waived.

\#\#\# 3.11 Administration Panel (FR11)  
\*   \*\*FR11.1 User Management:\*\* Priority: \*\*High\*\*. Admin UI (\`users\_screen.dart\`) for managing profiles.  
\*   \*\*FR11.2 Club Management:\*\* Priority: \*\*High\*\*. Admin UI (\`clubs\_screen.dart\`) for managing clubs/verification.

\#\#\# 3.12 Audit Logging (FR12)  
\*   \*\*FR12.1 Change Tracking:\*\* Priority: \*\*Medium\*\*. DB trigger (\`log\_audit\_trail\`) logs changes to \`audit\_log\` table.

\#\#\# 3.13 Notifications (Implied Requirement) (FR13)  
\*   \*\*FR13.1 Schedule Publication:\*\* Priority: \*\*Medium\*\*. Notify coaches/managers.  
\*   \*\*FR13.2 Referee Assignment:\*\* Priority: \*\*Medium\*\*. Notify referees.  
\*   \*\*FR13.3 Score Updates:\*\* Priority: \*\*Low\*\*. Notify followers.

\---

\#\# 4\. External Interface Requirements

\#\#\# 4.1 User Interface (Flutter Frontend)  
\*   GUI based on Flutter framework using Material Design.  
\*   Responsive design for Web; adaptable for potential Mobile deployment.  
\*   Utilizes common widgets (\`CommonAppBar\`, \`AppDrawer\`, \`GlassCard\`, \`LoadingOverlay\`, \`OfflineIndicator\`, \`ErrorListener\`, \`BracketView\`, \`ResponsiveContainer\`).  
\*   State management via Flutter Bloc.  
\*   Navigation via GoRouter.  
\*   Interacts with Supabase SDK for Auth/DB/Realtime and potentially FastAPI backend API.  
\*   \*\*Reference:\*\* See \[UI Mockups/Design System Document \- Link Placeholder\] for visual designs and component specifications.

\#\#\# 4.2 Backend API (FastAPI Service)  
\*   Provides RESTful endpoints (\`/schedule\`, \`/schedule/status/{job\_id}\`, \`/health\`).  
\*   Accepts JSON requests, returns JSON responses.  
\*   Secured via Supabase JWT validation.  
\*   Communicates with Supabase DB (Supabase-py) and Redis.  
\*   Deployed as Docker container on GCP Cloud Run.

\#\#\# 4.3 Supabase API (Database, Auth, Functions)  
\*   PostgreSQL Database accessed via SDKs.  
\*   Supabase Auth used for user management/JWTs.  
\*   Supabase Realtime used for live UI updates.  
\*   Supabase Edge Functions used for Stripe Webhook.  
\*   Database RPC Functions exposed for complex operations.

\#\#\# 4.4 Stripe API (Payments)  
\*   Used for processing payments.  
\*   Relies on Stripe Webhooks sending events to Supabase Edge Function.

\---

\#\# 5\. Non-Functional Requirements

\#\#\# 5.1 Performance (NFR1)  
\*   \*\*NFR1.1 Frontend Responsiveness:\*\* \<500ms feedback for UI interactions. Smooth scrolling.  
\*   \*\*NFR1.2 API Response Time:\*\* \<1s (status endpoints), \<2s (async job trigger).  
\*   \*\*NFR1.3 Async Job Completion:\*\* Target \< 5 minutes for typical 50-team tournament scheduling/assignment. Requires benchmarking.  
\*   \*\*NFR1.4 Database Performance:\*\* Target \<1s for typical list fetches and common queries. Indexes and efficient RLS required.  
\*   \*\*NFR1.5 Real-time Latency:\*\* Target \<5s for score/standing updates to propagate to connected clients.

\#\#\# 5.2 Security (NFR2)  
\*   \*\*NFR2.1 Authentication:\*\* Secure password handling (Supabase Auth), PKCE flow for web callbacks.  
\*   \*\*NFR2.2 Authorization:\*\* Strict RLS enforcement. API endpoint role checks via JWT.  
\*   \*\*NFR2.3 Data Encryption:\*\* At-rest and in-transit encryption provided by Supabase/GCP. HTTPS enforced.  
\*   \*\*NFR2.4 Secret Management:\*\* Use platform-provided secret management (GCP Secret Manager, Supabase secrets). No hardcoded secrets.  
\*   \*\*NFR2.5 Input Validation:\*\* Validate inputs in Flutter, FastAPI (Pydantic), and potentially DB functions/triggers.  
\*   \*\*NFR2.6 Webhook Security:\*\* Verify Stripe webhook signatures.  
\*   \*\*NFR2.7 Dependency Security:\*\* Regular dependency scanning.

\#\#\# 5.3 Reliability (NFR3)  
\*   \*\*NFR3.1 Availability:\*\* Target 99.9% uptime leveraging managed services.  
\*   \*\*NFR3.2 Error Handling:\*\* Graceful error handling and logging across all layers.  
\*   \*\*NFR3.3 Task Retries:\*\* Configure Celery tasks for automatic retries on transient errors.  
\*   \*\*NFR3.4 Database Backups:\*\* Utilize Supabase automated backups and PITR features.  
\*   \*\*NFR3.5 Monitoring & Alerting:\*\* Implement health checks, log monitoring (Supabase, GCP), and alerting for critical failures.  
\*   \*\*NFR3.6 Rollback Capability:\*\* Version control (Git) and deployment scripts support rollbacks.  
\*   \*\*NFR3.7 Offline Functionality:\*\*  
    \*   \*\*NFR3.7.1 Indicator:\*\* \`OfflineIndicator\` widget informs users of status.  
    \*   \*\*NFR3.7.2 Read Access:\*\* (Future Enhancement) Allow viewing cached/previously loaded data when offline.  
    \*   \*\*NFR3.7.3 Action Queuing:\*\* (Future Enhancement) Use \`SyncService\` to queue actions (e.g., score reporting) locally and sync upon reconnection.

\#\#\# 5.4 Usability (NFR4)  
\*   \*\*NFR4.1 Learnability:\*\* Intuitive Material Design UI. Clear workflows. User guides.  
\*   \*\*NFR4.2 Efficiency:\*\* Minimize clicks for common tasks. Provide clear feedback. Role-based dashboards.  
\*   \*\*NFR4.3 Accessibility:\*\* Target WCAG AA compliance (requires specific testing).  
\*   \*\*NFR4.4 Consistency:\*\* Consistent UI patterns, terminology, and branding (\`AppTheme\`, shared widgets).

\#\#\# 5.5 Maintainability (NFR5)  
\*   \*\*NFR5.1 Modularity:\*\* Logical code organization (Flutter features/layers, Python services/api/utils).  
\*   \*\*NFR5.2 Readability:\*\* Adherence to Dart/Python style guides. Comments, type hinting.  
\*   \*\*NFR5.3 Testability:\*\* High test coverage (Unit, Widget, Integration, API, DB).  
\*   \*\*NFR5.4 Configurability:\*\* Environment variables for key settings.  
\*   \*\*NFR5.5 Code Complexity:\*\* Aim for simple, refactored code units.

\#\#\# 5.6 Scalability (NFR6)  
\*   \*\*NFR6.1 Backend Service Scaling:\*\* Cloud Run auto-scaling.  
\*   \*\*NFR6.2 Worker Scaling:\*\* Independent Celery worker scaling.  
\*   \*\*NFR6.3 Database Scaling:\*\* Managed Supabase scaling. Optimized queries/indexes.  
\*   \*\*NFR6.4 Redis Scaling:\*\* Managed Memorystore scaling.

\#\#\# 5.7 Portability (NFR7)  
\*   \*\*NFR7.1 Frontend:\*\* Flutter supports Web (primary), potential for Mobile/Desktop.  
\*   \*\*NFR7.2 Backend:\*\* Docker containers deployable to compatible platforms.

\#\#\# 5.8 Testability (NFR8)  
\*   \*\*NFR8.1 Unit Tests:\*\* For Flutter Blocs/Utils, Python modules/utils.  
\*   \*\*NFR8.2 Widget Tests:\*\* For Flutter UI components.  
\*   \*\*NFR8.3 Integration Tests:\*\* For Flutter app flows, Python service interactions.  
\*   \*\*NFR8.4 API Tests:\*\* For FastAPI endpoints.  
\*   \*\*NFR8.5 Database Tests:\*\* For SQL functions/procedures/RLS.  
\*   \*\*NFR8.6 Test Automation:\*\* Integrated into CI/CD pipelines.

\#\#\# 5.9 Deployment (NFR9)  
\*   \*\*NFR9.1 Containerization:\*\* Docker for Python backend.  
\*   \*\*NFR9.2 Orchestration:\*\* Deployment scripts for GCP Cloud Run.  
\*   \*\*NFR9.3 Infrastructure as Code (IaC):\*\* (Optional) Terraform/Pulumi.  
\*   \*\*NFR9.4 CI/CD:\*\* GitHub Actions workflows.  
\*   \*\*NFR9.5 Database Migrations:\*\* Supabase CLI migration files.

\#\#\# 5.10 Legal and Compliance (NFR10)  
\*   \*\*NFR10.1 Privacy Policy:\*\* Available within the app (\`PrivacyPolicyScreen\`). Compliant content required.  
\*   \*\*NFR10.2 Data Handling:\*\* Secure handling of PII. RLS enforcement.  
\*   \*\*NFR10.3 Stripe Compliance:\*\* Adherence via Stripe integration methods.

\---

\#\# 6\. Data Model Overview

\#\#\# 6.1 Key Entities  
\*   \*\*\`profiles\`:\*\* Extends \`auth.users\`, holds role, names, phone, \*\*\`managing\_club\_id\` (for Club Admins)\*\*.  
\*   \*\*\`clubs\`:\*\* Club info, verification status, \*\*\`admin\_user\_id\` (primary contact?)\*\*.  
\*   \*\*\`club\_directors\`:\*\* Many-to-many link between \`profiles\` (TDs) and \`clubs\`, includes \`status\`.  
\*   \*\*\`tournaments\`:\*\* Details, dates, status, \`director\_user\_id\` (creator), \`managing\_club\_id\`.  
\*   \*\*\`tournament\_age\_groups\`:\*\* Divisions within tournament, format, settings (\`custom\_settings\` JSONB).  
\*   \*\*\`tournament\_groups\`:\*\* Specific groups (pools) within age groups.  
\*   \*\*\`teams\`:\*\* Team info, \`club\_id\`.  
\*   \*\*\`players\`:\*\* Player info, DOB, \`guardian\_profile\_id\`.  
\*   \*\*\`registrations\`:\*\* Links \`teams\` to \`tournament\_age\_groups\`, includes payment status, \`assigned\_group\_id\`.  
\*   \*\*\`team\_members\`:\*\* Links \`players\` to \`teams\` for specific \`tournament\_id\` (roster).  
\*   \*\*\`team\_staff\`:\*\* Links \`profiles\` (Coach/Manager) to \`teams\`.  
\*   \*\*\`matches\`:\*\* Game details, teams/placeholders, score, status, time, field, round, \`winner\_id\`.  
\*   \*\*\`standings\`:\*\* Calculated group standings.  
\*   \*\*\`referees\`:\*\* Referee profile extension, availability/preferences link.  
\*   \*\*\`match\_assignments\`:\*\* Links \`referees\` to \`matches\` with \`role\`.  
\*   \*\*\`venues\`, \`fields\`, \`field\_types\`, \`field\_unavailability\`:\*\* Location and field details.  
\*   \*\*\`team\_requests\`:\*\* Team scheduling preferences/conflicts.  
\*   \*\*\`transactions\`:\*\* Payment records.  
\*   \*\*\`audit\_log\`:\*\* Change history.  
\*   \*\*\`webhook\_events\`:\*\* Stripe event log.  
\*   \*\*\`device\_tokens\`:\*\* For push notifications.  
\*   \*\*\`fee\_configurations\`:\*\* Defines different fee types/amounts.  
\*   \*\*\`tie\_breaker\_overrides\`:\*\* Manual tiebreaker decisions.

\#\#\# 6.2 Relationships  
Standard relational model using foreign keys (e.g., \`tournament\_age\_groups.tournament\_id\` \-\> \`tournaments.id\`). Key junction tables: \`club\_directors\`, \`team\_members\`, \`team\_staff\`, \`match\_assignments\`.

\---

\#\# 7\. User Role Perspectives

\*(Updated based on clarifications)\*

\*   \*\*7.1 System Administrator Perspective:\*\*  
    \*   \*Goal:\* Maintain system health, manage users, configure global settings, verify clubs.  
    \*   \*Key Workflows:\* Login \-\> Admin Dashboard \-\> User Mgmt (Assign roles, \`managing\_club\_id\` for Club Admins) \-\> Club Mgmt (Verify clubs, view directors) \-\> Monitor Logs/Webhooks.  
\*   \*\*7.2 Tournament Director Perspective:\*\*  
    \*   \*Goal:\* Create/manage tournaments for \*approved\* affiliated clubs or independently (payment may apply).  
    \*   \*Key Workflows:\* Login \-\> Profile (Manage club affiliation requests/invitations) \-\> TD Dashboard \-\> Select Club Context (if multiple approved clubs) \-\> Create Tournament Wizard (Selects managing club) \-\> Configure \-\> Manage Registrations \-\> Trigger/Review Schedule & Assignments \-\> Manage Live Tournament \-\> View Results.  
\*   \*\*7.3 Club Administrator Perspective:\*\*  
    \*   \*Goal:\* Manage their assigned club (\`profiles.managing\_club\_id\`), its teams, staff, and potentially TD affiliations.  
    \*   \*Key Workflows:\* Register Club/Login \-\> Club Admin Dashboard \-\> Manage Club Details \-\> Manage Teams (Create, Assign Staff) \-\> Register Teams for Tournaments \-\> Manage Director Affiliation Requests (Approve/Deny requests for \*their\* club).  
\*   \*\*7.4 Referee Perspective:\*\*  
    \*   \*Goal:\* View assigned matches, manage availability/preferences.  
    \*   \*Key Workflows:\* Login \-\> Referee Dashboard \-\> Manage Availability/Preferences (UI Needed) \-\> View Assignments \-\> (Potentially) Report Scores.  
\*   \*\*7.5 Coach / Team Manager Perspective:\*\*  
    \*   \*Goal:\* Manage assigned team(s), roster, view schedules/results.  
    \*   \*Key Workflows:\* Login \-\> Coach Dashboard \-\> Select Team \-\> Manage Roster (incl. age validation) \-\> Register for Tournament (if allowed) \-\> Submit Scheduling Requests \-\> View Schedule/Standings \-\> (Potentially) Report Scores.  
\*   \*\*7.6 Player Parent / Player Perspective:\*\*  
    \*   \*Goal:\* View schedules/results for linked players/teams. Manage own profile.  
    \*   \*Key Workflows:\* Login \-\> Player/Parent Dashboard \-\> View Linked Players/Teams \-\> Select Tournament \-\> View Schedule/Standings/Brackets.

\---

\#\# 8\. Appendices

\#\#\# Appendix A: Traceability Matrix

| Req. ID  | Requirement Description                      | Source/Justification   | Related Design/UI                                                              | Related Code Module(s)                                                                                                 | Related Test Case(s)                                                                         | Priority | Status   |  
| :------- | :------------------------------------------- | :--------------------- | :----------------------------------------------------------------------------- | :--------------------------------------------------------------------------------------------------------------------- | :----------------------------------------------------------------------------------------- | :------- | :------- |  
| FR1.1    | User Registration (Email/Pass)             | Core Spec              | \`AuthScreen\` (Register Tab), \`ClubRegistrationScreen\`                          | \`AuthScreen\`, \`ClubRegistrationScreen\`, \`AuthBloc\`, \`AuthService\`, \`handle\_new\_user\` trigger, \`create\_club\_admin\_account\` RPC | \`auth\_bloc\_test.dart\`, \`auth\_screen\_widget\_test.dart\`, \`club\_registration\_test.dart\`        | High     | Included |  
| FR1.2    | User Login                                 | Core Spec              | \`AuthScreen\` (Login Tab)                                                       | \`AuthScreen\`, \`AuthBloc\`, \`AuthService\`                                                                                | \`auth\_bloc\_test.dart\`, \`auth\_screen\_widget\_test.dart\`                                       | High     | Included |  
| FR1.3    | Password Reset                             | Core Spec              | \`AuthScreen\`, \`ResetPasswordScreen\`, \`CheckEmailScreen\`, \`ResetSuccessScreen\` | \`AuthScreen\`, \`ResetPasswordScreen\`, \`AuthBloc\`, \`AuthService\`, \`CallbackScreen\`, \`router.dart\`                       | \`reset\_password\_test.dart\` (Integration), \`reset\_screens\_widget\_test.dart\`               | High     | Included |  
| FR1.4    | Email Confirmation                         | Core Spec              | \`CheckEmailScreen\`, \`CallbackScreen\`, \`ConfirmationSuccessScreen\`              | \`AuthService\`, \`AuthNotifier\`/\`AuthBloc\`, \`CallbackScreen\`, \`router.dart\`, Supabase Auth Config                          | \`signup\_confirm\_test.dart\` (Integration), \`confirmation\_success\_widget\_test.dart\`        | High     | Included |  
| FR1.5    | Secure Callback Handling                   | Core Spec              | \`CallbackScreen\`                                                               | \`CallbackScreen\`, \`AuthNotifier\`/\`AuthBloc\`, \`router.dart\` (redirect logic)                                            | \`callback\_handling\_test.dart\` (Integration), \`callback\_screen\_widget\_test.dart\` (limited) | High     | Included |  
| FR1.6    | Session Management                             | Core Spec / Supabase   | (Implicit)                                                                     | \`AuthNotifier\`/\`AuthBloc\`, \`SupabaseService\`, Supabase Flutter SDK                                                       | \`auth\_notifier\_test.dart\`, Integration Tests                                              | High     | Included |  
| FR1.7    | Logout                                         | Core Spec              | \`CommonAppBar\`, \`AppDrawer\`, \`ProfileScreen\`                                     | \`CommonAppBar\`, \`AppDrawer\`, \`ProfileScreen\`, \`AuthBloc\`, \`AuthService\`                                                | Widget Tests (AppBar/Drawer), Integration Tests                                           | High     | Included |  
| FR1.8    | Rate Limiting (Client/Server)                  | Best Practice        | \`AuthScreen\` (UI Feedback)                                                     | \`AuthScreen\` (State), \`RateLimiter\` utility, \`AuthService\` (Error Handling)                                           | \`rate\_limiter\_test.dart\`, \`auth\_screen\_widget\_test.dart\`                                   | Medium   | Included |  
| FR1.9.1  | RLS Policies                               | Core Spec / Security | N/A (DB Level)                                                                 | \`supabase\_full\_schema...sql\`, \`supabase\_schema\_v2\_part2...sql\`                                                          | Manual RLS Tests, Backend Tests                                                          | High     | Included |  
| FR1.9.2  | API Authorization                              | Core Spec / Security   | N/A (Backend API Level)                                                        | FastAPI (\`security.py\`, endpoint decorators)                                                                         | \`test\_resource\_authorization.py\`, \`test\_scheduler\_api\_auth.py\`                           | High     | Included |  
| FR1.9.3  | UI Role Control                            | Core Spec              | \`AppDrawer\`, Dashboards, Conditional Buttons                                   | \`AppDrawer\`, \`AuthNotifier\`/\`AuthBloc\`, \`router.dart\`, Role-Specific Dashboards                                        | \`router\_test.dart\`, Dashboard Widget Tests, \`app\_drawer\_widget\_test.dart\`                 | High     | Included |  
| FR1.10   | JWT Handling                                   | Core Spec / Security   | (Implicit)                                                                     | Supabase Flutter SDK, FastAPI (\`security.py\`)                                                                          | Integration Tests, API Tests                                                               | High     | Included |  
| FR2.1    | View Profile                                   | Core Spec              | \`ProfileScreen\`                                                                | \`ProfileScreen\`, \`ProfileBloc\`, \`AuthNotifier\`/\`AuthBloc\`, \`AuthService\`                                               | \`profile\_screen\_widget\_test.dart\`                                                          | High     | Included |  
| FR2.2    | Edit Profile                                   | Core Spec              | \`EditProfileScreen\`                                                            | \`EditProfileScreen\`, \`ProfileBloc\`, \`AuthNotifier\`/\`AuthBloc\`, \`AuthService\`                                           | \`edit\_profile\_screen\_widget\_test.dart\`                                                     | High     | Included |  
| FR3.1    | Club Admin Registration                        | Stakeholder Req        | \`ClubRegistrationScreen\`                                                       | \`ClubRegistrationScreen\` (Flutter), \`AuthService\` (Flutter), Python FastAPI endpoint \`/api/v1/register-club-admin\` (\`python\_backend/app/main.py\`), Supabase client (Python) | \`club\_registration\_test.dart\` (Widget/Integration), Python API tests for endpoint        | High     | Included |  
| FR3.2    | Club Verification (Admin Action)               | Stakeholder Req        | \`ClubsScreen\` (Admin UI)                                                       | \`ClubsScreen\`, \`DatabaseService\`, \`admin\_verify\_club\` RPC                                                              | \`clubs\_screen\_widget\_test.dart\` (Admin)                                                     | Medium   | Included |  
| FR3.3    | Club Affiliation Management (TD/Club)          | Stakeholder Req        | \`ProfileScreen\`, Admin/Club UI                                                 | \`AuthNotifier\`, \`ProfileScreen\`, \`club\_directors\` table, Affiliation RPCs (\`request\_...\`, \`invite\_...\`, \`action\_...\`) | \`profile\_screen\_test.dart\`, Affiliation Flow Tests (Integration)                         | Medium   | Included |
| FR4.1    | Tournament Creation Wizard                 | Core Spec              | \`TournamentWizardScreen\`                                                       | \`TournamentWizardScreen\`, \`TournamentBloc\`, \`DatabaseService\`                                                          | \`tournament\_wizard\_test.dart\` (Widget)                                                     | High     | Included |  
| FR4.2    | Tournament Configuration (Settings)            | Core Spec              | Tournament Manage Screen (UI Needed)                                           | Tournament Manage UI, \`TournamentBloc\`, \`DatabaseService\`, \`tournaments\`/\`tournament\_age\_groups\` tables (\`custom\_settings\`) | Tournament Config Tests (Widget/Integration)                                               | High     | Included |  
| FR4.3    | Field/Venue Management (Admin)                 | Core Spec              | Admin Venues/Fields UI (UI Needed)                                             | Admin UI, \`DatabaseService\`, \`venues\`/\`fields\`/\`field\_types\` tables                                                  | Admin UI Tests (Widget/Integration)                                                      | High     | Included |  
| FR5.1    | Team Registration                              | Core Spec              | Team/Club Admin UI (UI Needed)                                                 | Team/Club Admin UI, \`TeamBloc\`, \`DatabaseService\`, \`registrations\` table                                             | Team Registration Tests (Widget/Integration)                                               | High     | Included |  
| FR5.2    | Roster Management                              | Core Spec              | Team/Coach UI (UI Needed)                                                      | Team/Coach UI, \`TeamBloc\`/\`PlayerBloc\`?, \`DatabaseService\`, \`team\_members\`/\`players\` tables                         | Roster Management Tests (Widget/Integration)                                             | High     | Included |  
| FR5.3    | Age Validation                                 | Rule Compliance        | Roster Management UI                                                           | Roster UI, DB Trigger/Function (\`get\_player\_u\_age\`?), Client-side validation                                         | Age Validation Tests (Unit/Integration)                                                   | High     | Included |  
| FR6.1    | Bracket Size Validation                        | Core Spec              | TD UI (Before Scheduling)                                                      | FastAPI \`/validate\_brackets\`? / Supabase RPC \`api\_validate\_brackets\`, Python \`determine\_bracket\_format\`                   | Backend API/RPC Tests                                                                      | High     | Included |  
| FR6.2    | Fairness Analysis                              | Core Spec              | TD UI (\`FairnessAnalysisPanel.txt\`)                                            | \`FairnessAnalysisPanel\`, Supabase View \`fairness\_analysis\`, FastAPI \`/fairness\_report\`? / Supabase RPC \`api\_get\_fairness\_report\` | \`fairness\_analysis\_panel\_test.dart\`, Backend Tests                                       | Medium   | Included |  
| FR6.3    | Reporting Validation/Fairness                  | Core Spec              | TD UI                                                                          | \`TournamentDirectorDashboard\`, \`FairnessAnalysisPanel\`                                                               | TD Dashboard Tests (Widget)                                                                | Medium   | Included |  
| FR7.1    | Async Scheduling Trigger                       | Core Spec              | TD Dashboard (Button)                                                          | TD Dashboard UI, FastAPI \`POST /schedule\`, Celery \`schedule\_tournament\_task\`                                           | API Tests (\`test\_scheduler\_api\`), E2E Test                                               | High     | Included |  
| FR7.2    | Data Fetching (Task)                           | Core Spec              | N/A (Backend Task)                                                             | Python \`fetch\_scheduling\_data.py\`                                                                                        | Backend Unit Tests (\`test\_fetch\_data\`)                                                   | High     | Included |  
| FR7.3    | Time Slot Generation (Task)                    | Core Spec              | N/A (Backend Task)                                                             | Python \`scheduler\_time\_slots.py\`                                                                                         | Backend Unit Tests (\`test\_time\_slots\`)                                                   | High     | Included |  
| FR7.4    | Group Stage Pairings (Task)                    | Core Spec              | N/A (Backend Task)                                                             | Python \`create\_match\_pairings.py\`, \`simple\_round\_robin.py\`                                                               | Backend Unit Tests (\`test\_pairings\`)                                                     | High     | Included |  
| FR7.5    | Constraint Checking (Task)                     | Core Spec              | N/A (Backend Task)                                                             | Python \`check\_constraints.py\`, Core scheduling logic                                                                   | Backend Unit Tests (\`test\_constraints\`)                                                  | High     | Included |  
| FR7.6    | Playoff Advancement (DB)                       | Core Spec              | (Implicit in Standings)                                                        | Supabase Function \`determine\_advancing\_teams\`, Procedure \`advance\_playoff\_teams\`                                       | \`test\_playoff\_advancement.sql\`                                                            | High     | Included |  
| FR7.7    | Playoff Seeding (DB)                           | Core Spec              | (Implicit in Bracket View)                                                     | Supabase Procedure \`seed\_playoff\_bracket\`                                                                              | \`test\_playoff\_advancement.sql\`                                                            | High     | Included |  
| FR7.8    | Schedule Saving (Task)                         | Core Spec              | N/A (Backend Task)                                                             | Python Task (writes to \`matches\` table)                                                                                | Backend Integration Test                                                                   | High     | Included |  
| FR7.9    | Job Status Tracking                            | Core Spec              | TD Dashboard/Scheduling UI                                                     | UI Polling Logic, FastAPI \`GET /schedule/status/{job\_id}\`                                                               | API Tests (\`test\_scheduler\_api\`), UI Tests (Widget/Integration)                         | High     | Included |  
| FR7.10   | Schedule Viewing                               | Core Spec              | \`SchedulingScreen\`, \`RealtimeTournamentScreen\`                                 | \`SchedulingScreen\`, \`RealtimeTournamentScreen\`, \`MatchBloc\`, \`DatabaseService\`                                         | Schedule Viewing Tests (Widget)                                                          | High     | Included |  
| FR7.11   | Placeholder Text Handling                      | UI/UX Feedback         | \`BracketView\`, Schedule UI                                                     | \`BracketView\`, \`MatchScheduleItemComponent\`, \`matches\` table placeholder columns                                       | Bracket/Schedule Widget Tests                                                            | High     | Included |  
| FR8.1    | Async Assignment Trigger                       | Core Spec              | (Backend Task)                                                                 | Celery task (\`referee\_assignment\_task\`?), Python Service                                                              | Backend Integration Test                                                                   | High     | Included |  
| FR8.2    | Data Fetching (Task)                           | Core Spec              | N/A (Backend Task)                                                             | Python \`referee\_assignment\_service.py\` (fetches refs, matches, availability)                                          | Backend Unit Tests                                                                       | High     | Included |  
| FR8.3    | Assignment Algorithm                           | Core Spec              | N/A (Backend Task)                                                             | Python \`referee\_assignment\_service.py\`                                                                                   | Backend Unit Tests (\`test\_assignment\_logic\`)                                             | High     | Included |  
| FR8.4    | Assignment Saving (Task)                       | Core Spec              | N/A (Backend Task)                                                             | Python Task (writes to \`match\_assignments\` table)                                                                      | Backend Integration Test                                                                   | High     | Included |  
| FR8.5    | Workload Analysis                              | Feature Enhancement    | Admin/TD UI (UI Needed)                                                        | Python \`analyze\_referee\_workload\`? / Supabase View?                                                                     | Backend Tests / Manual Verification                                                        | Medium   | Included |  
| FR8.6    | View Assignments                               | Core Spec              | \`RefereesScreen\`, TD UI                                                        | \`RefereesScreen\`, \`RefereeBloc\`, \`DatabaseService\` (uses \`view\_referee\_assignments\`?)                                 | \`referees\_screen\_widget\_test.dart\`                                                        | High     | Included |  
| FR9.1    | Score Entry                                    | Core Spec              | Match Detail/Score Entry UI (UI Needed)                                        | Score Entry UI, \`MatchBloc\`, \`DatabaseService\`, \`update\_match\_score\_manual\` RPC                                      | Score Entry Tests (Widget/Integration)                                                   | High     | Included |  
| FR9.2    | Standings Calculation                          | Core Spec              | (Backend DB)                                                                   | \`refresh\_standings\` procedure, \`calculate\_team\_performance\` function                                                   | DB Function Tests                                                                        | High     | Included |  
| FR9.3    | Tiebreaker Logic                               | Core Spec              | (Implicit in Standings/Playoffs)                                               | \`determine\_advancing\_teams\` function (incorporates tiebreakers)                                                          | \`test\_playoff\_advancement.sql\` (includes tie scenarios)                                   | High     | Included |  
| FR9.4    | Real-time Updates                              | Feature Enhancement    | \`RealtimeTournamentScreen\`                                                     | \`RealtimeTournamentScreen\`, \`RealtimeService\`, Supabase Realtime                                                       | Manual Test, Potential E2E Test                                                           | Medium   | Included |  
| FR9.5    | Manual Overrides                               | Feature Enhancement    | TD/Admin UI (UI Needed)                                                        | Manual Override UI, \`DatabaseService\`, \`update\_match\_score\_manual\`/\`update\_standing\_manual\`? RPCs, \`audit\_log\`      | Manual Override Tests (Widget/Integration)                                               | Medium   | Included |  
| FR10.1   | Payment Initiation                             | Integration Req        | Registration UI (UI Needed)                                                    | Registration UI, Stripe SDK/API calls (Client or Backend)                                                              | Payment Flow Test (Integration/Manual)                                                   | Medium   | Included |  
| FR10.2   | Stripe Webhook Handling                        | Integration Req        | N/A (Backend Function)                                                         | Supabase Edge Function (\`index.ts\`), \`webhook\_events\` table                                                            | Webhook Simulation Test                                                                  | High     | Included |  
| FR10.3   | Director Membership Fee                        | Business Req           | \`TournamentWizardScreen\` (Logic)                                               | \`TournamentWizardScreen\` (Payment Trigger), Payment Flow UI, \`transactions\` table                                     | (Future Test)                                                                            | Low      | Included |  
| FR11.1   | User Management                                | Core Spec              | \`UsersScreen\` (Admin)                                                          | \`UsersScreen\`, \`AdminBloc\`?, \`DatabaseService\`                                                                         | \`users\_screen\_widget\_test.dart\` (Admin)                                                   | High     | Included |  
| FR11.2   | Club Management                                | Core Spec              | \`ClubsScreen\` (Admin)                                                          | \`ClubsScreen\`, \`AdminBloc\`?, \`DatabaseService\`, \`admin\_verify\_club\` RPC                                                 | \`clubs\_screen\_widget\_test.dart\` (Admin)                                                   | High     | Included |  
| FR12.1   | Change Tracking (Audit Log)                | Security/Admin Req     | N/A (DB Level), Admin UI Log Viewer (UI Needed)                                | \`log\_audit\_trail\` trigger function, \`audit\_log\` table                                                                    | DB Trigger Tests, Manual Verification                                                      | Medium   | Included |  
| FR13.1   | Schedule Publication Notification              | Usability              | (Notification System)                                                          | Notification Service (TBD), Trigger mechanism (e.g., after scheduling task)                                            | Notification Test (Integration/Manual)                                                   | Medium   | Included |  
| FR13.2   | Referee Assignment Notification                | Usability              | (Notification System)                                                          | Notification Service (TBD), Trigger mechanism (e.g., after assignment task)                                            | Notification Test (Integration/Manual)                                                   | Medium   | Included |  
| FR13.3   | Score Updates Notification                     | Feature Enhancement    | (Notification System)                                                          | Notification Service (TBD), Trigger mechanism (e.g., after score update)                                               | Notification Test (Integration/Manual)                                                   | Low      | Included |  
| NFR1.1   | Frontend Responsiveness                        | UX Requirement         | Flutter UI                                                                     | Flutter Rendering Engine, Widget Optimization                                                                          | Performance Profiling, Manual Testing                                                    | High     | Included |  
| NFR1.2   | API Response Time                              | Performance Target     | N/A (Backend API)                                                              | FastAPI Endpoints, Backend Service Logic, DB Queries                                                                   | API Load Testing, Performance Monitoring                                                 | High     | Included |  
| NFR1.3   | Async Job Completion Time (\<5m/50 teams)       | Performance Target     | N/A (Backend Task)                                                             | Celery Task Logic, Scheduling/Assignment Algorithms, DB/Redis Performance                                            | Backend Performance Testing, Benchmarking                                                | High     | Included |  
| NFR1.4   | Database Performance (\<1s lists)               | Performance Target     | N/A (DB Level)                                                                 | Supabase DB, Indexing Strategy, Query Optimization, RLS Efficiency                                                     | DB Load Testing, Query Analysis (\`EXPLAIN ANALYZE\`)                                       | High     | Included |  
| NFR1.5   | Real-time Latency (\<5s)                        | UX Requirement         | \`RealtimeTournamentScreen\`                                                     | Supabase Realtime Service, WebSocket infrastructure                                                                      | Manual Testing, Network Latency Monitoring                                                 | Medium   | Included |  
| NFR2.1   | Authentication (PKCE, Hashing)                 | Security Req.        | \`AuthScreen\`, Callback Flow                                                    | Supabase Auth, PKCE Implementation                                                                                       | Security Review, Penetration Testing (Optional)                                          | High     | Included |  
| NFR2.2   | Authorization (RLS/API)                        | Security Req.        | N/A (Backend/DB)                                                               | Supabase RLS Policies, FastAPI JWT Validation/Decorators                                                                 | RLS Tests, API Auth Tests, Security Review                                               | High     | Included |  
| NFR2.3   | Data Encryption (At Rest/Transit)              | Security Req.        | (Implicit)                                                                     | Supabase/GCP Platform Features (HTTPS, At-Rest Encryption)                                                               | Configuration Verification                                                               | High     | Included |  
| NFR2.4   | Secret Management                              | Security Req.        | N/A (Infrastructure)                                                           | GCP Secret Manager, Supabase Secrets, Environment Variables                                                              | Code Review, Configuration Audit                                                         | High     | Included |  
| NFR2.5   | Input Validation                               | Security Req.        | Flutter Forms, API Endpoints                                                   | Flutter \`Validators\`, FastAPI Pydantic Models                                                                          | Unit Tests, Widget Tests, API Tests, Security Scanning                                   | High     | Included |  
| NFR2.6   | Webhook Security (Stripe Signature)            | Security Req.        | N/A (Backend Function)                                                         | Supabase Edge Function (Signature Verification)                                                                        | Webhook Tests (Simulation, Manual)                                                       | High     | Included |  
| NFR2.7   | Dependency Security                            | Security Req.        | N/A (Development Process)                                                      | Package Management (\`pubspec.yaml\`, \`requirements.txt\`), Security Scanning Tools (e.g., \`pip-audit\`, \`pub audit\`) | CI Pipeline Step                                                                         | Medium   | Included |  
| NFR3.1   | Availability (99.9%)                           | Operational Req.       | (Platform Level)                                                               | Supabase SLA, GCP Cloud Run SLA, Monitoring                                                                            | Uptime Monitoring                                                                        | High     | Included |  
| NFR3.2   | Error Handling (Graceful)                      | Quality Req.         | Error Screens, SnackBars                                                       | \`ErrorHandler\`, \`ErrorListener\`, \`try/catch\` blocks (Flutter/Python), API Error Responses                        | Unit Tests (Error Cases), Manual Testing                                                 | High     | Included |  
| NFR3.3   | Task Retries (Celery)                          | Operational Req.       | N/A (Backend Task)                                                             | Celery Task Configuration (\`retry=True\`, \`max\_retries\`)                                                                | Backend Integration Tests (Simulate Failures)                                            | High     | Included |  
| NFR3.4   | Database Backups                               | Operational Req.       | N/A (DB Level)                                                                 | Supabase Backup Configuration                                                                                            | Backup/Restore Test Procedures                                                           | High     | Included |  
| NFR3.5   | Monitoring & Alerting                          | Operational Req.       | (Monitoring Tools)                                                             | \`/health\` endpoint, GCP Cloud Monitoring, Supabase Logs, Alerting Rules                                                | Manual Checks, Alert Simulation                                                          | High     | Included |  
| NFR3.6   | Rollback Capability                            | Deployment Req.        | N/A (Deployment)                                                               | Deployment Scripts (\`rollback\_...sh\`), Version Control (Git)                                                           | Deployment Process Testing                                                               | Medium   | Included |  
| NFR3.7   | Offline Functionality                          | Usability/Reliability  | \`OfflineIndicator\`                                                             | \`OfflineIndicator\`, \`ConnectivityService\`, (\`SyncService\`, \`LocalStorageService\` \- if implemented)                       | Widget Test (\`OfflineIndicator\`), Offline Mode Testing (Manual/Integration \- if applicable) | Medium   | Included |  
| NFR4.1   | Learnability (Intuitive UI)                    | UX Requirement         | Overall UI/UX Design                                                           | UI Components, Workflow Design, Documentation                                                                          | User Acceptance Testing (UAT), Heuristic Evaluation                                      | High     | Included |  
| NFR4.2   | Efficiency (Min Steps, Feedback)               | UX Requirement         | Key Workflows (Wizard, Scheduling)                                             | UI Design, Loading States, Feedback Mechanisms                                                                         | Task Completion Time Tests (UAT)                                                         | High     | Included |  
| NFR4.3   | Accessibility (WCAG AA Target)                 | Compliance Req.        | Flutter UI Elements                                                            | Semantic Widgets, Color Contrast, Focus Order                                                                          | Accessibility Testing Tools, Manual Screen Reader Test                                     | Medium   | Included |  
| NFR4.4   | Consistency (UI, Terminology)                  | UX Requirement         | Overall UI                                                                     | Shared Widgets (\`CommonAppBar\`, \`CustomCard\`), Theme (\`AppTheme\`)                                                      | UI Review, Style Guide Checks                                                            | High     | Included |  
| NFR5.1   | Modularity                                     | Dev Process            | Project Structure                                                              | Code Organization (Features, Layers), Service/Bloc Separation                                                            | Code Review                                                                              | High     | Included |  
| NFR5.2   | Readability (Style Guides, Comments)           | Dev Process            | Codebase                                                                       | Style Guide Adherence, Naming Conventions, Comments                                                                      | Code Review, Linter Checks                                                               | High     | Included |  
| NFR5.3   | Testability (Coverage, Mocks, DI)              | Dev Process            | Codebase                                                                       | Unit/Widget/Integration Test Coverage, Mocking, Dependency Injection                                                   | Test Coverage Reports                                                                    | High     | Included |  
| NFR5.4   | Configurability (Env Vars)                     | Dev Process            | Configuration Files                                                            | \`.env\`, Environment Variables                                                                                            | Configuration Review                                                                     | High     | Included |  
| NFR5.5   | Code Complexity (Low Cyclomatic)               | Dev Process            | Codebase                                                                       | Function/Method Length, Cyclomatic Complexity Analysis (Tools optional)                                                | Code Review                                                                              | Medium   | Included |  
| NFR6.1   | Backend Service Scaling (Cloud Run Auto)       | Infra Req.             | N/A (Infrastructure)                                                           | GCP Cloud Run Configuration (Min/Max Instances)                                                                        | Load Testing (API Endpoints)                                                             | High     | Included |  
| NFR6.2   | Worker Scaling (Celery Replicas)               | Infra Req.             | N/A (Infrastructure)                                                           | Celery Worker Deployment Configuration (Number of replicas)                                                            | Load Testing (Task Queue Length/Latency)                                                 | High     | Included |  
| NFR6.3   | Database Scaling (Supabase Managed)            | Infra Req.             | N/A (Infrastructure)                                                           | Supabase Plan/Scaling Options, Query Optimization                                                                      | DB Load Testing, Monitoring                                                              | High     | Included |  
| NFR6.4   | Redis Scaling (Memorystore Managed)            | Infra Req.             | N/A (Infrastructure)                                                           | GCP Memorystore Configuration                                                                                            | Monitoring                                                                               | High     | Included |  
| NFR7.1   | Frontend Portability (Flutter Web/Mobile?)     | Architectural Goal     | Flutter App                                                                    | Flutter SDK Features                                                                                     | Build/Test on Target Platforms (Web required, Mobile optional)                           | Medium   | Included |  
| NFR7.2   | Backend Portability (Docker)                   | Architectural Goal     | Docker Container                                                               | \`Dockerfile\`, Python/FastAPI Standards                                                                                   | Build/Run Container Locally/Other Environment                                            | High     | Included |  
| NFR8.1   | Unit Tests                                     | Quality Req.           | N/A                                                                            | \`test/\` directories (Flutter/Python), Mocking libraries                                                                | Test Coverage Reports, CI Checks                                                         | High     | Included |  
| NFR8.2   | Widget Tests                                   | Quality Req.           | N/A                                                                            | \`test/widgets/\` (Flutter), \`flutter\_test\`                                                                              | Test Coverage Reports, CI Checks                                                         | High     | Included |  
| NFR8.3   | Integration Tests (Flutter/Python)             | Quality Req.           | N/A                                                                            | \`integration\_test/\` (Flutter), \`tests/integration/\` (Python)                                                           | CI Pipeline Integration Tests                                                            | High     | Included |  
| NFR8.4   | API Tests                                      | Quality Req.           | N/A                                                                            | \`tests/api/\` (Python), \`pytest\`, \`httpx\`                                                                               | CI Pipeline API Tests                                                                    | High     | Included |  
| NFR8.5   | Database Tests                                 | Quality Req.           | N/A                                                                            | \`.sql\` test files                                                                                                       | Manual Run / DB Test Framework (Optional)                                                | Medium   | Included |  
| NFR8.6   | Test Automation (CI)                           | Dev Process            | N/A                                                                            | CI/CD Pipeline (\`.github/workflows/\`)                                                                                  | CI Pipeline Runs                                                                         | High     | Included |  
| NFR9.1   | Containerization (Docker)                      | Deployment Req.        | N/A                                                                            | \`Dockerfile\`                                                                                                             | Docker Build Success                                                                     | High     | Included |  
| NFR9.2   | Orchestration (Scripts)                        | Deployment Req.        | N/A                                                                            | \`deployment/scripts/\`                                                                                                  | Successful Script Execution                                                              | High     | Included |  
| NFR9.3   | Infrastructure as Code (IaC)                   | Best Practice          | N/A                                                                            | (Optional: Terraform/Pulumi files)                                                                                       | (Future)                                                                                 | Low      | Included |  
| NFR9.4   | CI/CD                                          | Dev Process            | N/A                                                                            | \`.github/workflows/\`                                                                                                   | Successful Pipeline Runs                                                                 | High     | Included |  
| NFR9.5   | Database Migrations (Supabase CLI)             | Dev Process            | N/A                                                                            | \`supabase/migrations/\`                                                                                                   | Supabase CLI \`db diff\`/\`migration apply\` success                                         | High     | Included |  
| NFR10.1  | Privacy Policy                                 | Legal Req.             | \`PrivacyPolicyScreen\`                                                          | \`PrivacyPolicyScreen\` content                                                                                            | Legal Review                                                                             | High     | Included |  
| NFR10.2  | Data Handling (PII, Security)                  | Legal Req.             | (Implicit)                                                                     | RLS Policies, Secure Coding Practices                                                                                    | Security Review, Compliance Audit (Optional)                                             | High     | Included |  
| NFR10.3  | Stripe Compliance (PCI DSS)                    | Legal Req.             | (Payment UI/Webhook)                                                           | Stripe Integration Code, Webhook Handler                                                                                 | Stripe PCI Compliance (Handled by Stripe), Manual Checks                                 | High     | Included |

\#\#\# Appendix B: Referenced Documents  
\*   \`supabase\_full\_schema\_and\_logic\_production\_v2.sql\` (Database Schema & Core Logic)  
\*   \`create\_club\_admin\_account.sql\` (Club Admin RPC)  
\*   \`supabase\_playoff\_advancement.sql\` (Playoff Logic)  
\*   Flutter Codebase (\`lib/\` directory \- includes all \`.dart\` files like \`main.dart\`, \`router.dart\`, \`auth\_notifier.dart\`, all screens, widgets, blocs, services, models, utils)  
\*   Python Backend Codebase (\`python\_backend/\` directory \- includes all \`.py\` files for API, services, tasks, utils, tests)  
\*   README Files (Various component descriptions, e.g., \`README\_BRACKET\_SIZE\_VALIDATION.md\`)  
\*   UI Mockups/Design System Document (\[Link Placeholder \- Add link if available\])  
\*   API Documentation (FastAPI Auto-docs, Stripe Webhook) (\[Link Placeholder\])  
\*   Deployment Scripts (\`deployment/scripts/\`, \`Dockerfile\`)  
\*   CI/CD Configuration (\`.github/workflows/\`)  
\*   Test Files (\`test/\`, \`integration\_test/\`, \`python\_backend/tests/\`)
