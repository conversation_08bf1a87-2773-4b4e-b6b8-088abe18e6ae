\# Developer Onboarding Guide \- Soccer Tournament Management Platform

\*\*Version:\*\* 1.0  
\*\*Date:\*\* April 28, 2025

\#\# 1\. Welcome\!

Welcome to the Soccer Tournament Management Platform team\! This guide provides the essential information you need to set up your development environment, understand the project structure, and start contributing effectively.

\#\# 2\. Project Overview

This platform is designed to manage soccer tournaments comprehensively, including registration, scheduling, referee assignment, payments, and real-time score tracking.

\*\*Core Components:\*\*

1\.  \*\*Flutter Frontend:\*\* A cross-platform application (primarily web) providing the user interface for all roles.  
2\.  \*\*Supabase Backend:\*\* Handles core backend services including authentication, database persistence (PostgreSQL), real-time updates, and serverless functions (e.g., Stripe webhook).  
3\.  \*\*Python Backend Service:\*\* A separate microservice built with FastAPI and Celery for handling computationally intensive tasks like automated scheduling and referee assignment. Runs in Docker containers, typically deployed on GCP Cloud Run.  
4\.  \*\*Redis:\*\* Used as a message broker and result backend for the Celery task queue.  
5\.  \*\*Stripe:\*\* Integrated for payment processing.

\*\*Key Documentation:\*\*

\* \*\*System Requirements Specification (SRS):\*\* \[Link to SRS Document \- e.g., \`docs/SRS.md\`\]  
\* \*\*Architecture Design Document (ADD):\*\* \[Link to ADD \- e.g., \`docs/architecture.md\`\]  
\* \*\*API Documentation:\*\*  
    \* FastAPI: Accessible via \`/docs\` and \`/redoc\` on the running local or staging API service.  
    \* Supabase: \[Supabase Docs\](https://supabase.com/docs) and specific RPCs documented in SQL files (\`supabase/migrations/\`).  
\* \*\*Other Detailed Designs:\*\* Check the \`/docs\` directory for detailed designs on specific modules like Scheduling, Referee Assignment, Auth, Payments, etc.

\#\# 3\. Getting Access

Before you start, ensure you have access to the following:

1\.  \*\*Version Control (Git):\*\*  
    \* Access to the project's Git repository (e.g., GitHub, GitLab). Request access from \[Team Lead/Admin Name\].  
    \* Ensure you have Git installed locally: \[https://git-scm.com/\](https://git-scm.com/)  
2\.  \*\*Communication:\*\*  
    \* Join the team's primary communication channel (e.g., Slack workspace/channel, Microsoft Teams). Ask your manager or team lead for an invitation.  
3\.  \*\*Project Management:\*\*  
    \* Get access to the issue tracking/project management tool (e.g., Jira, Trello, GitHub Issues). Request access from \[Project Manager/Scrum Master Name\].  
4\.  \*\*Supabase:\*\*  
    \* Request access to the \*\*Development\*\* and \*\*Staging\*\* Supabase projects from \[Infrastructure Lead/Admin Name\]. You will primarily interact with the local/dev instance for most development, but access to cloud instances is needed for certain tests and configuration checks.  
5\.  \*\*GCP (Google Cloud Platform):\*\*  
    \* Request appropriate IAM roles for accessing logs (Cloud Logging) and potentially monitoring dashboards (Cloud Monitoring) for the Development/Staging environments from \[Infrastructure Lead/Admin Name\]. Direct interaction with Cloud Run/Memorystore is typically handled via deployment scripts.  
6\.  \*\*Stripe:\*\*  
    \* Request access to the \*\*Test Mode\*\* environment in the team's Stripe account from \[Finance Contact/Admin Name\] if you need to work directly on payment integration features.

\#\# 4\. Development Environment Setup

\#\#\# 4.1 Prerequisites  
\* \*\*Git:\*\* Installed and configured.  
\* \*\*Docker & Docker Compose:\*\* Install Docker Desktop for your OS (\[https://www.docker.com/products/docker-desktop/\](https://www.docker.com/products/docker-desktop/)). This is essential for running the Python backend and its dependencies locally.

\#\#\# 4.2 Flutter Frontend (\`flutter\_frontend/\`)

1\.  \*\*Install Flutter SDK:\*\* Follow the official Flutter installation guide for your operating system: \[https://docs.flutter.dev/get-started/install\](https://docs.flutter.dev/get-started/install)  
    \* Ensure you can run \`flutter doctor\` without critical errors.  
    \* Enable web support: \`flutter config \--enable-web\`  
2\.  \*\*IDE Setup:\*\*  
    \* \*\*Recommended:\*\* Visual Studio Code (VS Code) with the official Dart and Flutter extensions.  
    \* Alternatively: Android Studio with the Flutter plugin.  
3\.  \*\*Clone Repository:\*\*  
    \`\`\`bash  
    git clone \<repository\_url\>  
    cd soccer\_tournament\_platform/flutter\_frontend  
    \`\`\`  
4\.  \*\*Install Dependencies:\*\*  
    \`\`\`bash  
    flutter pub get  
    \`\`\`  
5\.  \*\*Environment Configuration:\*\*  
    \* Copy the example environment file: \`cp .env.example .env\`  
    \* Edit the \`.env\` file and add the \*\*Supabase URL\*\* and \*\*Anon Key\*\* for your \*local\* Supabase instance (see Supabase setup below). You can get these by running \`supabase status\` after starting the local instance.  
    \* \*Note:\* The \`.env\` file is typically ignored by Git (\`.gitignore\`).  
6\.  \*\*Running the App:\*\*  
    \* Ensure your local Supabase instance is running (see section 4.4).  
    \* Select a target device (e.g., 'Chrome (web)') in your IDE.  
    \* Run the app from your IDE (usually F5 in VS Code) or via the command line:  
        \`\`\`bash  
        flutter run \-d chrome \--web-port 8114 \# Ensure port matches AuthConstants.localPort  
        \`\`\`  
7\.  \*\*Running Flutter Tests:\*\*  
    \`\`\`bash  
    flutter test \# Runs unit and widget tests  
    flutter test integration\_test/app\_test.dart \# Runs E2E tests (requires a running app/backend)  
    \`\`\`

\#\#\# 4.3 Python Backend Service (\`python\_backend/\`)

1\.  \*\*Install Python:\*\* Ensure you have Python 3.9 or later installed (\[https://www.python.org/downloads/\](https://www.python.org/downloads/)). Verify with \`python \--version\` or \`python3 \--version\`.  
2\.  \*\*Create & Activate Virtual Environment:\*\*  
    \`\`\`bash  
    cd python\_backend  
    python \-m venv venv  
    source venv/bin/activate  \# Linux/macOS  
    \# venv\\Scripts\\activate  \# Windows  
    \`\`\`  
3\.  \*\*Install Dependencies:\*\*  
    \`\`\`bash  
    pip install \--upgrade pip  
    pip install \-r requirements.txt  
    \`\`\`  
4\.  \*\*IDE Setup:\*\* VS Code with the Python extension is recommended. Configure the interpreter to use the \`venv\`.  
5\.  \*\*Environment Configuration:\*\*  
    \* Copy the example environment file: \`cp .env.example .env\`  
    \* Edit the \`.env\` file and add:  
        \* \`SUPABASE\_URL\`: Your \*local\* Supabase URL.  
        \* \`SUPABASE\_SERVICE\_ROLE\_KEY\`: Your \*local\* Supabase service role key (from \`supabase status\`).  
        \* \`SUPABASE\_JWT\_SECRET\`: Your \*local\* Supabase JWT secret (from \`supabase status\`).  
        \* \`REDIS\_URL\`: Typically \`redis://localhost:6379/0\` when using Docker Compose.  
        \* \`STRIPE\_SECRET\_KEY\`: Your Stripe \*test\* secret key.  
        \* \`STRIPE\_WEBHOOK\_SECRET\`: Your Stripe \*test\* webhook signing secret (generate one using \`stripe listen\` or from the Stripe dashboard for local testing).  
6\.  \*\*Running the Services (via Docker Compose \- Recommended):\*\*  
    \* Ensure Docker Desktop is running.  
    \* From the \`python\_backend\` directory:  
        \`\`\`bash  
        docker-compose up \--build  
        \`\`\`  
    \* This will start the FastAPI API (usually on \`http://localhost:8000\`), a Celery worker, and a Redis container.  
7\.  \*\*Running the Services (Manually \- Alternative):\*\*  
    \* Requires a separate Redis instance running.  
    \* Start the API: \`uvicorn app.main:app \--host 0.0.0.0 \--port 8000 \--reload\`  
    \* Start a Celery worker in a separate terminal: \`celery \-A app.celery\_app worker \--loglevel=info\`  
8\.  \*\*Running Python Tests:\*\*  
    \* Ensure Supabase and Redis are running (Docker Compose is easiest).  
    \* Run tests using the provided script or pytest:  
        \`\`\`bash  
        python run\_tests.py  
        \# OR  
        pytest tests/  
        \`\`\`  
    \* To run tests within the Docker environment (recommended for consistency):  
        \`\`\`bash  
        ./run\_tests\_in\_docker.sh  
        \`\`\`

\#\#\# 4.4 Supabase Local Development (\`supabase/\`)

1\.  \*\*Install Supabase CLI:\*\* Follow instructions at \[https://supabase.com/docs/guides/cli\](https://supabase.com/docs/guides/cli).  
2\.  \*\*Login:\*\* \`supabase login\` (only needed once).  
3\.  \*\*Initialize (if first time):\*\* If the \`.supabase\` directory doesn't exist, run \`supabase init\`.  
4\.  \*\*Link Project (Optional but helpful):\*\* Link to a remote Supabase project if needed for pulling schema changes: \`supabase link \--project-ref \<your-project-ref\>\` (get ref from Supabase dashboard URL).  
5\.  \*\*Start Local Supabase:\*\* From the \*root\* of the repository (\`soccer\_tournament\_platform\`):  
    \`\`\`bash  
    supabase start  
    \`\`\`  
    \* This starts Docker containers for Postgres, GoTrue (Auth), Realtime, Storage, etc.  
    \* It will output essential details like DB URL, API URL, Anon Key, Service Role Key, and JWT Secret. \*\*Use these in your \`.env\` files.\*\*  
6\.  \*\*Apply Migrations:\*\* To ensure your local database schema is up-to-date:  
    \`\`\`bash  
    supabase db reset \# Resets DB and applies all migrations in supabase/migrations  
    \# OR apply specific migrations if needed: supabase migration up  
    \`\`\`  
7\.  \*\*Stop Local Supabase:\*\* \`supabase stop\`

\#\# 5\. Codebase Overview

\*(Refer to the recommended Git repository structure in the Architecture Design Document)\*

\* \*\*\`flutter\_frontend/\`\*\*: Contains all Flutter UI code.  
    \* \`lib/\`: Main source code.  
        \* \`main.dart\`: App entry point.  
        \* \`app.dart\`: Root widget, theme, providers.  
        \* \`core/\`: Base utilities, navigation, theme, core services.  
        \* \`data/\`: Data models (\`\*.g.dart\` are generated), abstract repositories.  
        \* \`features/\`: Primary organization unit, containing Blocs, screens, widgets, and repository implementations specific to a feature (Auth, Tournaments, Profile, etc.).  
    \* \`test/\`: Unit and widget tests.  
    \* \`integration\_test/\`: End-to-end tests.  
    \* \`pubspec.yaml\`: Flutter dependencies.  
\* \*\*\`python\_backend/\`\*\*: Contains the FastAPI API and Celery worker code.  
    \* \`app/\`: Main application package.  
        \* \`api/\`: FastAPI endpoints, request/response models (Pydantic).  
        \* \`core/\`: Configuration, security helpers (JWT validation).  
        \* \`services/\`: Core business logic (scheduling, referee assignment).  
        \* \`tasks/\`: Celery task definitions (\`schedule\_tournament\_task\`).  
        \* \`utils/\`: Helper functions shared across services/tasks.  
        \* \`main.py\`: FastAPI application factory.  
        \* \`celery\_app.py\`: Celery application factory.  
    \* \`tests/\`: Python unit and integration tests (using \`pytest\` or \`unittest\`).  
    \* \`Dockerfile\`, \`Dockerfile.test\`: Container definitions.  
    \* \`docker-compose.yml\`: Local development environment setup.  
    \* \`requirements.txt\`: Python dependencies.  
\* \*\*\`supabase/\`\*\*: Configuration and code for Supabase backend components.  
    \* \`migrations/\`: SQL files for database schema changes. \*\*Crucial for maintaining DB consistency.\*\*  
    \* \`functions/\`: Code for Supabase Edge Functions (e.g., \`stripe-webhook/index.ts\`).  
    \* \`seed.sql\` (Optional): Script for seeding initial/test data.  
    \* \`config.toml\`: Supabase CLI configuration.  
\* \*\*\`docs/\`\*\*: Project documentation (SRS, ADDs, this guide, etc.).  
\* \*\*\`deployment/\`\*\*: Scripts and configuration for deploying the application (e.g., to GCP).  
\* \*\*\`legacy\_frontend/\`\*\*: Contains older React components (for reference only).

\#\# 6\. Development Workflow

1\.  \*\*Issue Tracking:\*\* Check the project board (Jira/GitHub Projects) for available tasks/issues. Assign yourself or pick from the backlog based on team priorities.  
2\.  \*\*Branching:\*\*  
    \* Create a new feature branch from the main development branch (e.g., \`develop\` or \`main\`). Use a descriptive name, often including the issue tracker ID (e.g., \`feature/TICKET-123-add-team-roster\`).  
    \* Commit frequently with clear, concise messages. Reference the issue ID in commit messages (e.g., \`feat(tournaments): Add team registration button \[TICKET-123\]\`).  
3\.  \*\*Development:\*\*  
    \* Write code following established patterns (Bloc for Flutter, Service/Task structure for Python).  
    \* Adhere to coding standards (check \`analysis\_options.yaml\` for Flutter, use linters like Black/Flake8 for Python if configured).  
    \* Write unit/widget/integration tests for new code. Ensure existing tests pass.  
4\.  \*\*Code Review (Pull Request):\*\*  
    \* Push your feature branch to the remote repository.  
    \* Create a Pull Request (PR) targeting the main development branch.  
    \* Ensure CI checks (tests, linting) pass.  
    \* Request reviews from relevant team members (e.g., frontend lead for UI changes, backend lead for API/DB changes).  
    \* Address feedback and iterate until approved.  
5\.  \*\*Merging:\*\* Once approved and CI passes, merge the PR into the development branch. Delete the feature branch.

\#\# 7\. Key Resources & Documentation

\* \*\*Project Repository:\*\* \[Link to Git Repository\]  
\* \*\*Issue Tracker:\*\* \[Link to Jira/GitHub Issues\]  
\* \*\*Communication Channel:\*\* \[Link to Slack/Teams Channel\]  
\* \*\*Supabase Project (Dev):\*\* \[Link to Dev Supabase Dashboard\]  
\* \*\*Supabase Project (Staging):\*\* \[Link to Staging Supabase Dashboard\]  
\* \*\*GCP Console (Dev/Staging):\*\* \[Link to GCP Project\]  
\* \*\*Stripe Dashboard (Test Mode):\*\* \[Link to Stripe Test Dashboard\]  
\* \*\*Core Documentation:\*\* Located in the \`/docs\` directory of the repository.

\#\# 8\. Getting Help

\* \*\*Flutter Questions:\*\* \[Frontend Lead Name/Contact\]  
\* \*\*Python/API/Celery Questions:\*\* \[Backend Lead Name/Contact\]  
\* \*\*Supabase/Database Questions:\*\* \[Backend Lead Name/DBA Name/Contact\]  
\* \*\*DevOps/Deployment/Infrastructure:\*\* \[DevOps Lead Name/Contact\]  
\* \*\*Product/Requirements Questions:\*\* \[Product Manager Name/Contact\]  
\* \*\*General Questions:\*\* Ask in the main team communication channel\!

We're excited to have you on the team\! Don't hesitate to ask questions.

