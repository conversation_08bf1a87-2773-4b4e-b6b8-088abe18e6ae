Detailed Design Document: Referee Assignment Algorithm

\*\*Version:\*\* 1.0  
\*\*Date:\*\* April 28, 2025  
\*\*Related Requirements:\*\* FR8 (Referee Assignment), NFR1 (Performance), NFR2 (Security), NFR3 (Reliability), NFR5 (Maintainability), NFR8 (Testability)

\#\# 1\. Introduction

\#\#\# 1.1 Purpose  
This document details the technical design for the automated referee assignment component of the Soccer Tournament Management Platform. It describes the process, logic, data requirements, and constraints involved in assigning qualified and available referees (Center, Assistant Referees) to scheduled matches within a tournament.

\#\#\# 1.2 Scope  
This document covers the automated referee assignment process, typically triggered after match scheduling is complete. It focuses on the logic executed within the Python backend service (likely as part of or called by the main Celery task). The scope includes:  
\* Retrieving scheduled matches and referee data from Supabase.  
\* Identifying required referee roles per match.  
\* Filtering referees based on qualifications, availability, and conflicts.  
\* Applying an assignment strategy to allocate referees to match slots.  
\* Persisting assignments to the Supabase database.  
\* Analyzing the resulting workload distribution among referees.  
\* Reporting assignment success, failures, and potential issues.

This document assumes the match schedule has already been generated (as detailed in the Scheduling Algorithm DDD). It focuses on the assignment logic itself, primarily implemented within \`referee\_assignment\_service.py\` and potentially orchestrated by \`scheduler\_service\_production\_v2\_final.py\` within the \`schedule\_tournament\_task\`.

\*Note: The provided \`referee\_assignment\_service.py\` contains placeholder logic for \`generate\_referee\_assignments\`. This document outlines the intended design and algorithm based on available data structures, tests (\`test\_referee\_assignment\_service.py\`), and common requirements for such systems.\*

\#\#\# 1.3 Glossary  
\*(Refer to main SRS Glossary)\*  
\* \*\*Center Referee:\*\* The main referee officiating a match.  
\* \*\*Assistant Referee (AR):\*\* Referees assisting the Center Referee, typically positioned along the touchlines (AR1, AR2).  
\* \*\*Referee Grade:\*\* A qualification level indicating the types/ages of matches a referee is certified to officiate.  
\* \*\*Conflict:\*\* A reason why a referee should not be assigned to a specific match (e.g., club affiliation, prior difficult interaction, scheduling clash).

\#\# 2\. Design Overview

The referee assignment process is designed to run automatically after match scheduling. It aims to assign a full, qualified crew (Center, AR1, AR2, where applicable) to each scheduled match while respecting various constraints.

\* \*\*Trigger:\*\* Typically invoked automatically within the \`schedule\_tournament\_task\` Celery task after \`generate\_match\_schedule\` completes successfully. Could potentially be triggered independently via a separate API endpoint/task if needed for reassignment.  
\* \*\*Execution:\*\* Logic resides primarily within the \`referee\_assignment\_service.py\` module, called by the Celery worker.  
\* \*\*Data Source/Sink:\*\* Supabase PostgreSQL database (\`matches\`, \`referees\`, \`referee\_availability\`, \`referee\_conflicts\`, \`referee\_assignments\` tables).  
\* \*\*Key Outputs:\*\* Populated \`referee\_assignments\` table in Supabase; summary statistics (assigned/unassigned counts), warnings, errors, and workload analysis returned as part of the overall scheduling task result.

\#\# 3\. Inputs

The assignment algorithm requires the following data, typically fetched from Supabase at the start of the process:

\* \*\*Scheduled Matches (\`matches\` table):\*\*  
    \* \`id\` (match ID)  
    \* \`tournament\_id\`  
    \* \`tournament\_age\_group\_id\`  
    \* \`scheduled\_time\` (start time)  
    \* \`field\_id\`  
    \* \`team1\_id\`, \`team2\_id\`  
    \* \`round\_name\` (e.g., 'group', 'semifinal', 'final')  
    \* \`status\` (should be 'scheduled')  
\* \*\*Tournament Age Group Settings (\`tournament\_age\_groups.custom\_settings\`):\*\*  
    \* Referee requirements (e.g., number of ARs needed based on format/age).  
    \* Minimum referee grade required for Center/AR roles for this age group.  
\* \*\*Referee Details (\`referees\` table):\*\*  
    \* \`id\` (referee's profile ID)  
    \* \`name\` (or fetched from \`profiles\`)  
    \* \`grade\` (qualification level)  
    \* \`club\_affiliation\_id\` (for conflict checking)  
    \* \`max\_games\_per\_day\` (optional constraint)  
\* \*\*Referee Availability (\`referee\_availability\` table):\*\*  
    \* \`referee\_id\`  
    \* \`start\_time\`, \`end\_time\` (periods when the referee is available)  
    \* \`venue\_preferences\` (optional, for optimization)  
\* \*\*Referee Conflicts (\`referee\_conflicts\` table):\*\*  
    \* \`referee\_id\`  
    \* \`conflict\_type\` (e.g., 'club', 'team', 'coach')  
    \* \`conflict\_entity\_id\` (e.g., club\_id, team\_id)  
\* \*\*Team Club Affiliations (\`teams\` table):\*\*  
    \* \`id\` (team ID)  
    \* \`club\_id\`

\#\# 4\. Outputs

\* \*\*Database:\*\* Records inserted into the \`referee\_assignments\` table:  
    \* \`id\`: Unique assignment ID.  
    \* \`match\_id\`: Foreign key to \`matches\`.  
    \* \`referee\_id\`: Foreign key to \`referees\`/\`profiles\`.  
    \* \`assignment\_role\`: Type \`assignment\_role\` ENUM ('center', 'ar1', 'ar2').  
    \* \`status\`: e.g., 'assigned', 'confirmed', 'declined'. Initially 'assigned'.  
    \* \`created\_at\`, \`updated\_at\`.  
\* \*\*Task Result (Dictionary):\*\* Included within the overall scheduling task result:  
    \* \`assigned\_count\`: Total number of referee slots filled.  
    \* \`unassigned\_count\`: Total number of referee slots remaining empty.  
    \* \`errors\`: List of critical errors encountered during assignment.  
    \* \`warnings\`: List of non-critical issues (e.g., low referee pool, minor constraint relaxations).  
    \* \`workload\_analysis\`: Dictionary produced by \`analyze\_referee\_workload\`.

\#\# 5\. Core Logic and Processing Steps (\`generate\_referee\_assignments\`)

\*(Based on common practices and available data, as the provided code is a placeholder)\*

1\.  \*\*Fetch Data:\*\* Retrieve all scheduled matches for the \`tournament\_id\` and all potentially available/relevant referees (perhaps filtered by those marked as available for the tournament dates). Fetch referee availability, conflicts, and qualifications. Fetch team club affiliations.  
2\.  \*\*Initialize State:\*\*  
    \* Create data structures to track referee assignments per time slot/referee to prevent double-booking. E.g., \`referee\_schedule\[referee\_id\]\[datetime\] \= match\_id\`.  
    \* Initialize counters for assigned/unassigned slots.  
    \* Create lists for errors and warnings.  
3\.  \*\*Determine Match Requirements:\*\* For each match, determine the required roles (Center, AR1, AR2) and minimum qualification grade based on \`tournament\_age\_groups.custom\_settings\`.  
4\.  \*\*Iterate Through Matches (or Time Slots):\*\* A common approach is to process matches chronologically.  
    \* For each \`match\`:  
        \* Identify required roles (e.g., Center, AR1, AR2).  
        \* For each \`role\` needed:  
            \* \*\*Find Candidate Referees:\*\*  
                \* Filter the pool of all referees to find those meeting the minimum \`grade\` for the role and match age group.  
                \* Check \*\*Availability:\*\* Filter candidates based on \`referee\_availability\` records overlapping with the \`match.scheduled\_time\` (plus estimated duration).  
                \* Check \*\*Conflicts:\*\*  
                    \* Filter out referees already assigned to another match during the same time slot (using \`referee\_schedule\`).  
                    \* Filter out referees with club conflicts (\`referee\_conflicts\` table or comparing \`referees.club\_affiliation\_id\` with \`teams.club\_id\` for both teams in the match).  
                    \* Filter out referees with other defined conflicts (\`referee\_conflicts\`).  
                \* Check \*\*Workload:\*\* (Optional during initial assignment, often used for optimization) Filter out referees exceeding \`max\_games\_per\_day\` if configured.  
            \* \*\*Select & Assign Referee:\*\*  
                \* From the filtered list of candidates:  
                    \* \*\*Strategy 1 (Simple):\*\* Pick the first available, qualified, non-conflicted referee.  
                    \* \*\*Strategy 2 (Prioritized):\*\* Prioritize referees with higher grades, specific venue preferences, or those needing more games to balance workload (requires pre-calculating or iteratively updating workload).  
                    \* \*\*Strategy 3 (Optimization):\*\* Use a more complex algorithm (e.g., matching algorithms, constraint solvers) to find the best overall fit across multiple matches, minimizing travel or maximizing preference fulfillment (likely overkill for V1).  
                \* If a suitable referee is found:  
                    \* Create a record for the \`referee\_assignments\` table.  
                    \* Update the \`referee\_schedule\` tracking structure.  
                    \* Increment \`assigned\_count\`.  
                \* If no suitable referee is found:  
                    \* Increment \`unassigned\_count\`.  
                    \* Log a warning or add to an "unassigned" list for later review/manual assignment.  
5\.  \*\*Persist Assignments:\*\* After iterating through all matches, batch insert the created \`referee\_assignments\` records into the Supabase database. Handle potential insertion errors.  
6\.  \*\*Analyze Workload:\*\* Call \`analyze\_referee\_workload(tournament\_id)\` to generate statistics on the assignments made.  
7\.  \*\*Compile Results:\*\* Assemble the final result dictionary including counts, errors, warnings, and the workload analysis report.

\#\# 6\. Key Data Structures (Internal)

\* \`referee\_pool\` (list\[dict\]): List of all potential referees with their details (ID, grade, availability, conflicts).  
\* \`match\_requirements\` (dict\[match\_id, dict\]): Stores required roles and qualifications per match.  
\* \`referee\_schedule\` (defaultdict\[referee\_id, list\[tuple(start\_time, end\_time, match\_id)\]\]): Tracks the assigned schedule for each referee to prevent double-booking.  
\* \`assignments\_to\_insert\` (list\[dict\]): List of dictionaries representing rows to be inserted into \`referee\_assignments\`.

\#\# 7\. Constraint Handling

The algorithm must enforce several constraints during candidate filtering and selection:

\* \*\*Qualification:\*\* Referee's grade must meet or exceed the minimum required for the match age group and role (Center vs. AR).  
\* \*\*Availability:\*\* Referee must be marked as available (\`referee\_availability\`) during the match time window (start time \+ estimated duration).  
\* \*\*Double-Booking:\*\* Referee cannot be assigned to another match that overlaps in time.  
\* \*\*Conflicts of Interest:\*\* Referee should not be assigned if they have a defined conflict (e.g., same club as one of the playing teams).  
\* \*\*Workload Limits (Optional):\*\* Adherence to \`max\_games\_per\_day\` if configured and tracked.

\#\# 8\. Error Handling

\* \*\*Data Fetching Errors:\*\* Failure to retrieve necessary data from Supabase should result in task failure or a clear error state.  
\* \*\*No Suitable Referees:\*\* If no qualified, available, and non-conflicted referee can be found for a specific slot, it should be marked as unassigned and potentially logged as a warning.  
\* \*\*Database Errors:\*\* Errors during the batch insertion of assignments should be caught and logged. Partial success might require rollback logic or clear reporting of which assignments failed.  
\* \*\*Configuration Errors:\*\* Missing or invalid tournament settings (e.g., referee requirements per age group) should be handled gracefully, likely resulting in warnings or task failure.

\#\# 9\. Performance Considerations

\* \*\*Database Queries:\*\* Efficiently query Supabase for matches, referees, availability, and conflicts. Index relevant columns (\`match\_id\`, \`referee\_id\`, \`scheduled\_time\`, \`start\_time\`, \`end\_time\`).  
\* \*\*Filtering Logic:\*\* Optimize the filtering of candidate referees for each match slot. Pre-processing referee availability/qualifications into efficient lookup structures might be beneficial.  
\* \*\*Algorithm Complexity:\*\* The complexity depends heavily on the number of matches, referees, and the assignment strategy. A simple greedy approach iterating through matches is likely O(M \* R \* S) in the worst case (M=matches, R=referees, S=slots/checks per ref), but optimizations are possible.  
\* \*\*Batching:\*\* Batch database inserts for assignments at the end of the process.

\#\# 10\. Future Improvements / Considerations

\* \*\*Optimization Strategy:\*\* Implement a more sophisticated assignment strategy beyond first-fit (e.g., min-cost max-flow, simulated annealing) to optimize for factors like referee travel distance, preferences, or workload balancing during the assignment phase itself.  
\* \*\*Manual Assignment Interface:\*\* Provide a UI for TDs/Assignors to review unassigned slots and manually assign referees, potentially with suggestions based on relaxed constraints.  
\* \*\*Referee Confirmation Flow:\*\* Add states like 'pending\_confirmation', 'confirmed', 'declined' to \`referee\_assignments\` and build UI/notification workflows for referees to accept/reject assignments.  
\* \*\*Dynamic Re-assignment:\*\* Handle scenarios where referees become unavailable after initial assignment.  
