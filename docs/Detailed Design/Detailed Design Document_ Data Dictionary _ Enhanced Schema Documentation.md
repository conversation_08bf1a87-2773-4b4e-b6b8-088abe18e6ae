\# Detailed Design Document: Data Dictionary / Enhanced Schema Documentation

\*\*Version:\*\* 1.0  
\*\*Date:\*\* April 28, 2025  
\*\*Related Requirements:\*\* All Functional Requirements (FR1-FR13), NFR2 (Security), NFR5 (Maintainability)

\#\# 1\. Introduction

\#\#\# 1.1 Purpose  
This document provides a detailed description of the database schema used by the Soccer Tournament Management Platform within Supabase (PostgreSQL). It serves as a reference for developers, database administrators, and anyone needing to understand the structure, relationships, and constraints of the application's data.

\#\#\# 1.2 Scope  
This document covers the primary tables, views, custom types (ENUMs), and key functions/procedures defined in the \`public\` schema, as established by the SQL scripts (primarily \`supabase\_full\_schema\_and\_logic\_production\_v2.sql\`). It explains the purpose of tables and columns, data types, constraints, and relationships.

\#\#\# 1.3 Glossary  
\*(Refer to main SRS Glossary and previous DDDs)\*  
\* \*\*PK:\*\* Primary Key  
\* \*\*FK:\*\* Foreign Key  
\* \*\*NN:\*\* Not Null  
\* \*\*UQ:\*\* Unique Constraint  
\* \*\*Default:\*\* Default value if not specified on insert.  
\* \*\*RLS:\*\* Row Level Security

\#\#\# 1.4 Custom ENUM Types
This section lists the custom ENUM types defined in the `public` schema.

*   \`tournament\_status\`: ('planning', 'registration\_open', 'registration\_closed', 'scheduling', 'live', 'completed', 'cancelled')
*   \`match\_status\`: ('scheduled', 'in\_progress', 'completed', 'penalty\_shootout', 'postponed', 'cancelled\_weather', 'cancelled\_other', 'forfeited\_team1', 'forfeited\_team2')
*   \`user\_role\`: ('admin', 'tournament\_director', 'club\_admin', 'coach', 'team\_manager', 'referee', 'player\_parent')
*   \`payment\_status\`: ('pending', 'paid', 'failed', 'waived', 'refunded')
*   \`bracket\_round\`: ('group', 'round\_of\_16', 'quarterfinal', 'semifinal', 'final', 'consolation')
*   \`assignment\_role\`: ('Center', 'AR1', 'AR2', '4th Official')
*   \`team\_role\`: ('Coach', 'Assistant Coach', 'Manager')
*   \`request\_type\`: ('Cannot Play Before', 'Cannot Play After', 'Cannot Play Between', 'Prefer Morning', 'Prefer Afternoon')
*   \`director\_affiliation\_status\`: ('pending', 'approved', 'rejected', 'invited', 'inactive') - *Newly added for club director affiliations.*

\#\# 2\. Schema Diagram (Conceptual \- Mermaid)

\`\`\`mermaid  
erDiagram  
    USERS ||--o{ PROFILES : "has"  
    PROFILES ||--o{ TEAMS : "manages (Club Admin)"  
    PROFILES ||--o{ TEAM\_STAFF : "is staff for"  
    PROFILES ||--o{ CLUB\_DIRECTORS : "directs"  
    PROFILES ||--o{ REFEREES : "is a"  
    PROFILES ||--o{ PLAYERS : "is guardian for"  
    PROFILES ||--o{ TRANSACTIONS : "initiates"  
    PROFILES ||--o{ DEVICE\_TOKENS : "has"

    CLUBS ||--o{ PROFILES : "managed by"  
    CLUBS ||--o{ TEAMS : "belongs to"  
    CLUBS ||--o{ CLUB\_DIRECTORS : "has director"

    TOURNAMENTS ||--o{ TOURNAMENT\_AGE\_GROUPS : "has"  
    TOURNAMENTS ||--o{ MATCHES : "contains"  
    TOURNAMENTS ||--o{ STANDINGS : "has"  
    TOURNAMENTS ||--o{ TRANSACTIONS : "related to"  
    TOURNAMENTS ||--o{ TOURNAMENT\_STAFF : "has"

    TOURNAMENT\_AGE\_GROUPS ||--o{ TOURNAMENT\_GROUPS : "has"  
    TOURNAMENT\_AGE\_GROUPS ||--o{ REGISTRATIONS : "has"  
    TOURNAMENT\_AGE\_GROUPS ||--o{ MATCHES : "contains"

    TOURNAMENT\_GROUPS ||--o{ MATCHES : "contains"  
    TOURNAMENT\_GROUPS ||--o{ STANDINGS : "has"  
    TOURNAMENT\_GROUPS ||--o{ REGISTRATIONS : "assigns to"

    TEAMS ||--o{ REGISTRATIONS : "participates in"  
    TEAMS ||--o{ TEAM\_STAFF : "has"  
    TEAMS ||--o{ PLAYERS : "has"  
    TEAMS ||--o{ STANDINGS : "represents"  
    TEAMS ||--o{ MATCHES : "plays as team1"  
    TEAMS ||--o{ MATCHES : "plays as team2"  
    TEAMS ||--o{ MATCHES : "wins"  
    TEAMS ||--o{ MATCHES : "loses"

    REGISTRATIONS ||--o{ TRANSACTIONS : "may require"

    MATCHES ||--o{ REFEREE\_ASSIGNMENTS : "has"  
    MATCHES ||--o{ FIELDS : "played on"

    REFEREES ||--o{ REFEREE\_ASSIGNMENTS : "assigned to"  
    REFEREES ||--o{ REFEREE\_AVAILABILITY : "has"  
    REFEREES ||--o{ REFEREE\_CONFLICTS : "has"

    VENUES ||--o{ FIELDS : "contains"

    PROFILES }|..| auth.users : "extends"

    USERS {  
        UUID id PK  
        timestamptz created\_at  
        timestamptz updated\_at  
        string email  
        string encrypted\_password  
        jsonb raw\_user\_meta\_data  
        timestamptz email\_confirmed\_at  
        timestamptz phone\_confirmed\_at  
        timestamptz recovery\_sent\_at  
        timestamptz confirmation\_sent\_at  
        timestamptz invited\_at  
        string confirmation\_token  
        string recovery\_token  
        string email\_change\_token\_new  
        string email\_change\_token\_current  
        timestamptz email\_change\_sent\_at  
        string phone  
        timestamptz phone\_change\_sent\_at  
        string phone\_change\_token  
        timestamptz reauthentication\_token  
        timestamptz reauthentication\_sent\_at  
    }

    PROFILES {  
        UUID id PK "FK to auth.users.id"  
        timestamptz created\_at  
        timestamptz updated\_at  
        text email UQ  
        text first\_name  
        text last\_name  
        text phone  
        user\_role role "Default: 'player\_parent'"  
        text avatar\_url  
        UUID managing\_club\_id FK "References CLUBS(id)"  
    }

    CLUBS {  
        UUID id PK  
        timestamptz created\_at  
        timestamptz updated\_at  
        text name NN  
        text city  
        text state  
        text country  
        text website  
        boolean is\_verified "Default: false"  
        text verification\_notes  
    }

    CLUB\_DIRECTORS {  
        UUID club\_id PK, FK "References CLUBS(id)"  
        UUID user\_id PK, FK "References PROFILES(id)"  
        director\_affiliation\_status status "Default: 'pending'"
        timestamptz created\_at  
        timestamptz updated\_at
    }

    TEAMS {
        UUID id PK  
        timestamptz created\_at  
        timestamptz updated\_at  
        text name NN  
        UUID club\_id FK "References CLUBS(id)"  
        text gender  
        integer birth\_year\_min  
        integer birth\_year\_max  
        text skill\_level  
        text notes  
    }

    PLAYERS {  
        UUID id PK  
        timestamptz created\_at  
        timestamptz updated\_at  
        text first\_name NN  
        text last\_name NN  
        date date\_of\_birth  
        text gender  
        UUID guardian\_profile\_id FK "References PROFILES(id)"  
        text notes  
    }

    TEAM\_STAFF {  
        UUID team\_id PK, FK "References TEAMS(id)"  
        UUID profile\_id PK, FK "References PROFILES(id)"  
        team\_role role NN "Default: 'coach'"  
        timestamptz created\_at  
    }

    TEAM\_PLAYERS {  
        UUID team\_id PK, FK "References TEAMS(id)"  
        UUID player\_id PK, FK "References PLAYERS(id)"  
        integer jersey\_number  
        timestamptz created\_at  
    }

    TOURNAMENTS {  
        UUID id PK  
        timestamptz created\_at  
        timestamptz updated\_at  
        text name NN  
        text location  
        date start\_date NN  
        date end\_date NN  
        integer season\_year  
        tournament\_status status "Default: 'planning'"  
        text description  
        text rules\_url  
        text logo\_url  
        jsonb custom\_settings  
    }

    TOURNAMENT\_STAFF {  
        UUID tournament\_id PK, FK "References TOURNAMENTS(id)"  
        UUID profile\_id PK, FK "References PROFILES(id)"  
        text role NN "e.g., 'Director', 'Scheduler', 'Admin'"  
        timestamptz created\_at  
    }

    TOURNAMENT\_AGE\_GROUPS {  
        UUID id PK  
        UUID tournament\_id NN, FK "References TOURNAMENTS(id)"  
        text name NN  
        integer age\_u NN  
        text gender "e.g., 'Boys', 'Girls', 'Coed'"  
        text format "e.g., '11v11', '9v9', '7v7'"  
        integer min\_teams  
        integer max\_teams  
        decimal registration\_fee  
        text currency  
        jsonb custom\_settings "e.g., game duration, playoff format, validation rules"  
        timestamptz created\_at  
        timestamptz updated\_at  
    }

    TOURNAMENT\_GROUPS {  
        UUID id PK  
        UUID tournament\_age\_group\_id NN, FK "References TOURNAMENT\_AGE\_GROUPS(id)"  
        text name NN "e.g., 'Group A', 'Blue Division'"  
        timestamptz created\_at  
        timestamptz updated\_at  
    }

    REGISTRATIONS {  
        UUID id PK  
        UUID tournament\_age\_group\_id NN, FK "References TOURNAMENT\_AGE\_GROUPS(id)"  
        UUID team\_id NN, FK "References TEAMS(id)"  
        UUID assigned\_group\_id FK "References TOURNAMENT\_GROUPS(id)"  
        payment\_status payment\_status "Default: 'pending'"  
        text notes  
        timestamptz created\_at  
        timestamptz updated\_at  
        UQ (tournament\_age\_group\_id, team\_id)  
    }

    VENUES {  
        UUID id PK  
        text name NN  
        text address  
        text city  
        text state  
        text zip\_code  
        text contact\_name  
        text contact\_phone  
        text contact\_email  
        time operational\_start\_time  
        time operational\_end\_time  
        timestamptz created\_at  
        timestamptz updated\_at  
    }

    FIELDS {  
        UUID id PK  
        UUID venue\_id NN, FK "References VENUES(id)"  
        text name NN  
        text field\_type\_name "e.g., 'Grass', 'Turf'"  
        text size "e.g., 'Full', '7v7', '9v9'"  
        text status "Default: 'Open'"  
        time field\_start\_time "Overrides venue time"  
        time field\_end\_time "Overrides venue time"  
        text notes  
        timestamptz created\_at  
        timestamptz updated\_at  
    }

    FIELD\_UNAVAILABILITY {  
        UUID id PK  
        UUID field\_id NN, FK "References FIELDS(id)"  
        timestamptz unavailable\_start NN  
        timestamptz unavailable\_end NN  
        text reason  
        timestamptz created\_at  
        timestamptz updated\_at  
    }

    MATCHES {  
        UUID id PK  
        UUID tournament\_id NN, FK "References TOURNAMENTS(id)"  
        UUID tournament\_age\_group\_id NN, FK "References TOURNAMENT\_AGE\_GROUPS(id)"  
        UUID group\_id FK "References TOURNAMENT\_GROUPS(id), NULL for playoffs"  
        UUID team1\_id FK "References TEAMS(id)"  
        UUID team2\_id FK "References TEAMS(id)"  
        integer team1\_score  
        integer team2\_score  
        UUID winner\_id FK "References TEAMS(id)"  
        UUID loser\_id FK "References TEAMS(id)"  
        boolean is\_draw "Default: false"  
        timestamptz scheduled\_time  
        UUID field\_id FK "References FIELDS(id)"  
        match\_status status "Default: 'scheduled'"  
        integer round\_number  
        text round\_name "e.g., 'group', 'quarterfinal', 'semifinal', 'final', 'consolation'"  
        integer match\_number "Number within the round/group"  
        boolean is\_bye "Default: false"  
        UUID forfeit\_winner\_id FK "References TEAMS(id)"  
        text placeholder\_team1\_text "For playoff seeding, e.g., 'Winner Group A'"  
        text placeholder\_team2\_text "For playoff seeding, e.g., 'Seed 8'"  
        jsonb custom\_data  
        timestamptz created\_at  
        timestamptz updated\_at  
        CHECK (team1\_id \<\> team2\_id)  
    }

    STANDINGS {  
        UUID id PK  
        UUID tournament\_id NN, FK "References TOURNAMENTS(id)"  
        UUID group\_id NN, FK "References TOURNAMENT\_GROUPS(id)"  
        UUID team\_id NN, FK "References TEAMS(id)"  
        integer played "Default: 0"  
        integer wins "Default: 0"  
        integer draws "Default: 0"  
        integer losses "Default: 0"  
        integer goals\_for "Default: 0"  
        integer goals\_against "Default: 0"  
        integer goal\_difference "Default: 0"  
        integer points "Default: 0"  
        integer group\_position  
        timestamptz created\_at  
        timestamptz updated\_at  
        UQ (tournament\_id, group\_id, team\_id)  
    }

    REFEREES {  
        UUID id PK "FK to profiles.id"  
        timestamptz created\_at  
        timestamptz updated\_at  
        text grade "e.g., 'Grassroots', 'Regional', 'National'"  
        text certification\_number  
        date certification\_expiry  
        text notes  
    }

    REFEREE\_AVAILABILITY {  
        UUID id PK  
        UUID referee\_id NN, FK "References REFEREES(id)"  
        timestamptz start\_time NN  
        timestamptz end\_time NN  
        text notes  
        timestamptz created\_at  
        timestamptz updated\_at  
    }

    REFEREE\_CONFLICTS {  
        UUID id PK  
        UUID referee\_id NN, FK "References REFEREES(id)"  
        text conflict\_type NN "e.g., 'club', 'team', 'coach', 'player'"  
        UUID conflict\_entity\_id NN "ID of the club, team, profile etc."  
        text reason  
        timestamptz created\_at  
        timestamptz updated\_at  
        UQ (referee\_id, conflict\_type, conflict\_entity\_id)  
    }

    REFEREE\_ASSIGNMENTS {  
        UUID id PK  
        UUID match\_id NN, FK "References MATCHES(id)"  
        UUID referee\_id NN, FK "References REFEREES(id)"  
        assignment\_role role NN "e.g., 'center', 'ar1', 'ar2'"  
        text status "Default: 'assigned'"  
        timestamptz created\_at  
        timestamptz updated\_at  
        UQ (match\_id, role)  
        UQ (match\_id, referee\_id)  
    }

    TRANSACTIONS {  
        UUID id PK  
        UUID user\_id FK "References PROFILES(id)"  
        UUID tournament\_id FK "References TOURNAMENTS(id)"  
        UUID registration\_id FK "References REGISTRATIONS(id)"  
        text transaction\_type "e.g., 'registration\_fee', 'tournament\_fee'"  
        text payment\_provider "Default: 'stripe'"  
        text provider\_transaction\_id "e.g., Stripe Payment Intent ID"  
        integer amount NN "In smallest currency unit (e.g., cents)"  
        text currency NN "e.g., 'usd'"  
        payment\_status status "Default: 'pending'"  
        jsonb metadata  
        timestamptz created\_at  
        timestamptz updated\_at  
    }

    DEVICE\_TOKENS {  
        UUID id PK  
        UUID profile\_id NN, FK "References PROFILES(id)"  
        text device\_token NN  
        text device\_type NN "CHECK ('ios', 'android', 'web')"  
        timestamptz created\_at  
        timestamptz updated\_at  
        UQ (profile\_id, device\_token)  
    }
