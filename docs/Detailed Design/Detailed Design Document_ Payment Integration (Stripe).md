\# Detailed Design Document: Payment Integration (Stripe)

\*\*Version:\*\* 1.0  
\*\*Date:\*\* April 28, 2025  
\*\*Related Requirements:\*\* FR10 (Payment Processing), NFR2 (Security), NFR3 (Reliability), NFR5 (Maintainability), NFR8 (Testability), NFR10 (Legal and Compliance)

\#\# 1\. Introduction

\#\#\# 1.1 Purpose  
This document provides a detailed technical design for the integration of the Stripe payment gateway into the Soccer Tournament Management Platform. It outlines how payments (e.g., for team registrations, tournament entry fees) are initiated, processed, and how the application confirms successful payments.

\#\#\# 1.2 Scope  
This design covers:  
\* The process of initiating a payment from the Flutter frontend.  
\* The interaction with the Stripe API (likely using Stripe Elements or a similar client-side integration method).  
\* The handling of payment success/failure notifications from Stripe via webhooks.  
\* The role of the Supabase Edge Function in receiving and processing Stripe webhooks.  
\* Updating the application's database (\`transactions\`, potentially \`registrations\` or \`tournaments\`) based on payment status.  
\* Security considerations specific to payment processing and webhook handling.

It does \*not\* cover the specifics of setting up Stripe products/prices or the detailed UI design of the payment forms within the Flutter application.

\#\#\# 1.3 Glossary  
\*(Refer to main SRS Glossary)\*  
\* \*\*Stripe:\*\* Third-party payment processing platform.  
\* \*\*Stripe Elements:\*\* Stripe's prebuilt UI components for collecting payment details securely.  
\* \*\*Payment Intent:\*\* A Stripe object representing an attempt to collect payment from a customer.  
\* \*\*Webhook:\*\* An automated message sent from Stripe to our application when an event occurs (e.g., payment success, failure).  
\* \*\*Webhook Endpoint:\*\* A specific URL in our application (hosted as a Supabase Edge Function) that receives webhook events from Stripe.  
\* \*\*Webhook Signing Secret:\*\* A secret key provided by Stripe used to verify that incoming webhook requests genuinely originate from Stripe.

\#\# 2\. Design Overview

The payment integration utilizes a hybrid approach involving the client-side (Flutter), a serverless function (Supabase Edge Function), and the Stripe API.

1\.  \*\*Initiation (Client-Side):\*\* The Flutter application initiates the payment process, likely by creating a Payment Intent via a call to a secure backend endpoint (or potentially directly using publishable keys for simpler flows, though less common for server-required actions like creating orders). The frontend then uses Stripe's libraries (e.g., Stripe Elements or a mobile SDK) to securely collect payment details and confirm the Payment Intent.  
2\.  \*\*Confirmation (Webhook):\*\* Stripe processes the payment and sends asynchronous webhook events to a dedicated Supabase Edge Function.  
3\.  \*\*Processing (Serverless):\*\* The Supabase Edge Function verifies the webhook signature, processes relevant events (like \`payment\_intent.succeeded\`), logs the event, and updates the application's database (e.g., marking a transaction or registration as paid).

This approach keeps sensitive payment details primarily handled by Stripe's secure components, minimizing the PCI DSS compliance scope for the core application. The backend primarily reacts to confirmed payment events via webhooks.

\#\# 3\. Component Responsibilities

\* \*\*Flutter Frontend:\*\*  
    \* Presents payment options and amounts to the user.  
    \* Initiates the payment process (potentially by calling a backend endpoint to create a Payment Intent).  
    \* Integrates with Stripe's client-side libraries (e.g., Stripe Elements for web, Stripe Mobile SDKs) to securely collect and tokenize payment information.  
    \* Confirms the Payment Intent with Stripe using the client secret obtained during initiation.  
    \* Displays payment success or failure messages to the user based on the client-side confirmation result \*and/or\* updates received via real-time database changes triggered by the webhook handler.  
\* \*\*Supabase Edge Function (\`stripe-webhook/index.ts\`):\*\*  
    \* Provides a publicly accessible HTTPS endpoint for Stripe to send webhook events.  
    \* Verifies the signature of incoming webhook requests using the \`STRIPE\_WEBHOOK\_SECRET\` to ensure authenticity.  
    \* Parses the webhook event payload (JSON).  
    \* Logs the incoming event to the \`webhook\_events\` table for auditing and debugging.  
    \* Contains logic to handle specific event types (e.g., \`payment\_intent.succeeded\`, \`charge.succeeded\`, \`checkout.session.completed\`).  
    \* Uses the \`SUPABASE\_SERVICE\_ROLE\_KEY\` to interact with the Supabase database (bypassing RLS) to update relevant tables (e.g., \`transactions\`, \`registrations\`) based on the event data.  
    \* Returns an appropriate HTTP status code (e.g., 200 OK) to Stripe to acknowledge receipt of the event.  
\* \*\*Supabase Database (PostgreSQL):\*\*  
    \* Stores transaction records (\`transactions\` table) potentially linked to registrations, users, or tournaments. Includes fields for \`stripe\_payment\_intent\_id\`, \`amount\`, \`currency\`, \`status\` ('pending', 'succeeded', 'failed'), \`user\_id\`, \`related\_entity\_id\` (e.g., registration\_id).  
    \* Stores raw webhook event data (\`webhook\_events\` table) for auditing and potential reprocessing.  
    \* May have triggers or functions that react to changes in the \`transactions\` table (e.g., updating a \`registrations.payment\_status\` field).  
\* \*\*Stripe:\*\*  
    \* Provides the payment gateway infrastructure.  
    \* Hosts secure payment forms (via Elements/SDKs).  
    \* Processes payments with various methods.  
    \* Sends webhook notifications for payment events to the configured Supabase Edge Function endpoint.  
    \* Provides a dashboard for managing payments, customers, and API keys.

\#\# 4\. Detailed Flows

\#\#\# 4.1 Payment Initiation and Client-Side Confirmation (Example: Using Payment Intents)

1\.  \*\*User Action:\*\* User clicks "Pay Registration Fee" (or similar) in the Flutter app.  
2\.  \*\*Frontend Request (Optional but Recommended):\*\* Flutter app sends an authenticated request to a backend endpoint (could be another Supabase Function or the Python API) specifying the amount, currency, and related entity (e.g., registration ID).  
3\.  \*\*Backend Endpoint (Optional):\*\* Creates a Stripe Payment Intent using the Stripe Secret Key, potentially associating metadata (like \`user\_id\`, \`registration\_id\`). Returns the \`client\_secret\` of the Payment Intent to the Flutter app.  
4\.  \*\*Flutter Frontend (Stripe SDK/Elements):\*\*  
    \* Initializes Stripe Elements/SDK with the publishable key.  
    \* Uses the \`client\_secret\` received from the backend.  
    \* Presents the Stripe payment form (e.g., Card Element).  
    \* User enters payment details directly into the Stripe Element.  
    \* User clicks "Confirm Payment".  
    \* Flutter app calls the Stripe SDK's \`confirmPayment\` (or similar) method, passing the \`client\_secret\` and payment method details collected by Elements.  
5\.  \*\*Stripe:\*\* Attempts to process the payment.  
6\.  \*\*Frontend Response:\*\* Stripe SDK returns the result of the payment confirmation attempt (success, requires action, failure) to the Flutter app.  
7\.  \*\*Flutter UI Update:\*\* The app displays an initial success/processing/failure message based on the immediate Stripe SDK response. \*Crucially, the application state (e.g., marking the registration as fully paid) should ideally wait for the server-side webhook confirmation.\*

\#\#\# 4.2 Stripe Webhook Handling (Server-Side Confirmation)

1\.  \*\*Stripe Event:\*\* Stripe successfully processes a payment (e.g., \`payment\_intent.succeeded\`).  
2\.  \*\*Webhook Sent:\*\* Stripe sends an HTTP POST request containing the event details (JSON payload) to the configured Supabase Edge Function URL (\`supabase/functions/stripe-webhook/index.ts\`). The request includes a \`Stripe-Signature\` header.  
3\.  \*\*Supabase Edge Function (\`index.ts\`):\*\*  
    \* Receives the POST request.  
    \* Retrieves the raw request body and the \`Stripe-Signature\` header.  
    \* Uses the \`STRIPE\_WEBHOOK\_SECRET\` (from environment variables) and the Stripe SDK (or crypto library) to verify the signature. If verification fails, returns a \`400 Bad Request\` and logs the error.  
    \* If signature is valid, logs the raw event payload and \`event\_type\` to the \`webhook\_events\` table with status 'pending'. Records the \`webhook\_event\_id\`.  
    \* Parses the JSON payload to identify the \`event.type\`.  
    \* \*\*Handles Specific Events (e.g., \`payment\_intent.succeeded\`):\*\*  
        \* Extracts relevant data (e.g., \`payment\_intent.id\`, \`amount\`, \`currency\`, \`metadata\`).  
        \* Uses the \`supabaseAdmin\` client (with Service Role Key) to update the database:  
            \* Find the corresponding record in the \`transactions\` table (using \`stripe\_payment\_intent\_id\`).  
            \* Update the \`transactions.status\` to 'succeeded'.  
            \* Potentially update the related entity (e.g., \`registrations.payment\_status\` to 'paid') based on metadata or transaction links.  
        \* Sets \`processingStatus \= 'processed'\`, \`processingNotes \= 'Successfully updated transaction/registration.'\`.  
    \* \*\*Handles Other Events:\*\* Implement logic for other relevant events (e.g., \`payment\_intent.payment\_failed\`, \`charge.refunded\`).  
    \* \*\*Handles Unhandled Events:\*\* Logs a warning if an unexpected event type is received. Sets \`processingStatus \= 'ignored'\`.  
    \* \*\*Error Handling:\*\* If any step during processing fails (e.g., database update error), catch the exception, set \`processingStatus \= 'failed'\`, and populate \`processingNotes\` with the error details.  
    \* Updates the corresponding \`webhook\_events\` record with the final \`processingStatus\` and \`processingNotes\`.  
    \* Returns a \`200 OK\` response to Stripe to acknowledge successful receipt (even if processing failed internally, Stripe just needs to know the webhook was received).  
4\.  \*\*Database Trigger/Realtime (Optional):\*\* A database trigger on the \`transactions\` or \`registrations\` table could notify the Flutter app via Supabase Realtime that the payment status has been officially updated by the backend.  
5\.  \*\*Flutter UI Update (Final):\*\* The Flutter app (listening to Realtime updates or refreshing data) reflects the confirmed paid status.

\#\# 5\. Key Data Structures

\* \*\*\`transactions\` Table (Supabase):\*\*  
    \* \`id\` (UUID, PK)  
    \* \`user\_id\` (UUID, FK to \`profiles.id\`)  
    \* \`related\_entity\_type\` (TEXT, e.g., 'registration', 'tournament\_fee')  
    \* \`related\_entity\_id\` (UUID)  
    \* \`stripe\_payment\_intent\_id\` (TEXT, Indexed)  
    \* \`stripe\_charge\_id\` (TEXT, Optional)  
    \* \`amount\` (INTEGER, in smallest currency unit, e.g., cents)  
    \* \`currency\` (TEXT, e.g., 'usd')  
    \* \`status\` (TEXT ENUM: 'pending', 'succeeded', 'failed', 'refunded')  
    \* \`payment\_method\_details\` (JSONB, Optional)  
    \* \`created\_at\`, \`updated\_at\`  
\* \*\*\`webhook\_events\` Table (Supabase):\*\*  
    \* \`id\` (UUID, PK)  
    \* \`event\_type\` (TEXT, e.g., 'payment\_intent.succeeded')  
    \* \`payload\` (JSONB)  
    \* \`status\` (TEXT: 'pending', 'processed', 'failed', 'ignored')  
    \* \`attempts\` (INTEGER)  
    \* \`last\_attempt\_at\` (TIMESTAMPTZ)  
    \* \`error\_message\` (TEXT)  
    \* \`created\_at\`, \`updated\_at\`  
\* \*\*Stripe Event Object (JSON):\*\* Structure defined by Stripe API, containing \`id\`, \`type\`, \`data.object\`, etc.

\#\# 6\. Security Considerations

\* \*\*Webhook Secret Protection:\*\* The \`STRIPE\_WEBHOOK\_SECRET\` is critical. Store it securely in Supabase Function secrets or GCP Secret Manager (if deploying function differently). \*\*Never\*\* commit it to the repository.  
\* \*\*Webhook Signature Verification:\*\* \*\*Always\*\* verify the \`Stripe-Signature\` header in the webhook handler (\`index.ts\`) to prevent processing forged requests.  
\* \*\*Idempotency:\*\* Design the webhook handler to be idempotent. If Stripe resends the same event (due to network issues), processing it multiple times should not cause incorrect state changes (e.g., double-crediting a payment). Check the \`webhook\_events\` table or the target table's status before processing.  
\* \*\*Service Role Key Usage:\*\* The Supabase Edge Function uses the \`SUPABASE\_SERVICE\_ROLE\_KEY\`. Ensure this key is protected and the function's logic is secure, as it bypasses RLS.  
\* \*\*Frontend Keys:\*\* Use Stripe's \*publishable\* keys in the Flutter frontend. These are less sensitive than secret keys.  
\* \*\*PCI Compliance:\*\* Using Stripe Elements/SDKs shifts most PCI DSS compliance burden to Stripe, but ensure no sensitive card details are ever logged or stored on your servers (including Supabase DB or function logs).

\#\# 7\. Error Handling

\* \*\*Webhook Verification Failure:\*\* Log the error, return \`400 Bad Request\` to Stripe. Stripe will retry sending the webhook.  
\* \*\*Webhook Processing Failure:\*\*  
    \* Log the specific error within the Edge Function.  
    \* Update the \`webhook\_events\` table record with \`status \= 'failed'\` and the \`error\_message\`.  
    \* Return \`200 OK\` to Stripe to prevent unnecessary retries for processing errors (unless it's a temporary issue like DB unavailability, where a 5xx might trigger a retry).  
    \* Implement monitoring/alerting on failed webhook events. Consider a mechanism for manual review and reprocessing of failed events.  
\* \*\*Payment Intent Failures (Client-side):\*\* Handle errors returned by the Stripe SDK in the Flutter app and provide clear feedback to the user.

\#\# 8\. Performance Considerations

\* \*\*Webhook Volume:\*\* If expecting very high payment volume, ensure the Supabase Edge Function and database updates can handle the load. Supabase Functions generally scale well.  
\* \*\*Database Updates:\*\* Keep database updates within the webhook handler efficient. Avoid complex, long-running queries.

\#\# 9\. Future Improvements / Considerations

\* \*\*More Payment Methods:\*\* Integrate additional Stripe payment methods (Apple Pay, Google Pay, ACH, etc.).  
\* \*\*Refund Handling:\*\* Implement API endpoints and/or UI features for processing refunds via the Stripe API, including updating the \`transactions\` table status.  
\* \*\*Subscription/Recurring Payments:\*\* If needed in the future, integrate Stripe Billing.  
\* \*\*Manual Reprocessing:\*\* Build an admin tool to view failed webhook events and trigger reprocessing.  
\* \*\*Enhanced Logging/Monitoring:\*\* Add more detailed logging within the webhook handler and set up specific alerts for payment failures or high error rates.

