\# Detailed Design Document: Playoff Advancement and Seeding Logic

\*\*Version:\*\* 1.0  
\*\*Date:\*\* April 28, 2025  
\*\*Related Requirements:\*\* FR7 (Scheduling \- Playoffs), FR9 (Standings), FR6 (Bracket Management), NFR3 (Reliability), NFR8 (Testability)

\#\# 1\. Introduction

\#\#\# 1.1 Purpose  
This document details the technical design for determining which teams advance from the group stage to the playoff rounds and how those teams are seeded into the initial playoff bracket within the Soccer Tournament Management Platform.

\#\#\# 1.2 Scope  
This design covers the logic encapsulated primarily within Supabase PostgreSQL functions and procedures, specifically:  
\* Calculating final group standings, including tie-breaking rules (\`calculate\_team\_performance\`, \`refresh\_standings\`).  
\* Determining the set of advancing teams based on tournament format and standings (\`determine\_advancing\_teams\`).  
\* Creating the initial playoff match records and seeding the advancing teams into the bracket (\`seed\_playoff\_bracket\`).

This document assumes that group stage matches have been marked as 'completed' and scores have been entered, allowing for accurate standings calculation. It focuses on the database-level logic.

\#\#\# 1.3 Glossary  
\*(Refer to main SRS Glossary)\*  
\* \*\*Tiebreaker:\*\* A rule used to rank teams with the same number of points in the standings.  
\* \*\*Seeding:\*\* The process of ranking advancing teams and placing them into the playoff bracket according to a predefined structure.  
\* \*\*Placeholder:\*\* A temporary identifier used in playoff matches (e.g., "Winner Group A") before the actual advancing team is known or confirmed.

\#\# 2\. Design Overview

The playoff advancement and seeding process is primarily handled within the Supabase database using SQL functions and procedures. This approach leverages the database's ability to efficiently query and manipulate relational data (standings, matches, teams).

\* \*\*Trigger:\*\* This logic is intended to be executed after the completion of all relevant group stage matches for an age group within a tournament. While the current implementation seems to include calls to these functions within the initial \`schedule\_tournament\_task\` (in \`scheduler\_service\_production\_v2\_final.py\`), the ideal trigger point is after group stage results are finalized. This could be a manual action by the Tournament Director via the UI or an automated trigger based on match completion status.  
\* \*\*Execution:\*\* Primarily involves calling the Supabase RPC functions \`determine\_advancing\_teams\` and \`seed\_playoff\_bracket\` for a specific \`tournament\_id\` and \`tournament\_age\_group\_id\`.  
\* \*\*Data Source:\*\* Relies heavily on the \`matches\`, \`standings\`, \`tournament\_age\_groups\`, and \`teams\` tables.  
\* \*\*Key Outputs:\*\*  
    \* A ranked list of advancing teams with their calculated seed positions.  
    \* New rows created in the \`matches\` table representing the playoff rounds (e.g., Quarterfinals, Semifinals, Final, Consolation), initially populated with placeholder team identifiers or directly seeded team IDs where possible.

\#\# 3\. Inputs

\* \*\*\`p\_tournament\_id\` (UUID):\*\* The ID of the tournament.  
\* \*\*\`p\_tournament\_age\_group\_id\` (UUID):\*\* The ID of the specific age group within the tournament for which playoffs are being generated.  
\* \*\*\`tournament\_age\_groups.custom\_settings\` (JSONB):\*\* Contains critical configuration:  
    \* \`playoff\_format\`: String identifier for the playoff structure (e.g., "8\_team", "4\_team\_2\_groups", "champions\_only").  
    \* \`advancing\_teams\_count\`: The total number of teams expected to advance.  
    \* \`include\_consolation\`: Boolean indicating if a 3rd place/consolation match should be created.  
\* \*\*\`standings\` table:\*\* Contains the calculated final standings for each team within their respective groups, including points, goal difference (GD), goals for (GF), goals against (GA), etc. (Populated by \`refresh\_standings\` function).  
\* \*\*\`matches\` table:\*\* Contains results of group stage matches, particularly for head-to-head tiebreakers.

\#\# 4\. Outputs

\* \*\*\`determine\_advancing\_teams\` Function Output:\*\* Returns a table/set of records, each containing:  
    \* \`team\_id\`  
    \* \`team\_name\`  
    \* \`group\_id\`  
    \* \`group\_name\`  
    \* \`seed\_position\` (Overall rank among advancing teams, 1 to N)  
    \* \`seed\_description\` (e.g., "Winner Group A", "Runner-up Group B", "Best 3rd Place")  
    \* \`points\`  
    \* \`goal\_difference\`  
    \* \`group\_position\` (Rank within their group)  
\* \*\*\`seed\_playoff\_bracket\` Procedure Output:\*\*  
    \* Creates new rows in the \`matches\` table for playoff rounds (Quarterfinals, Semifinals, Final, Consolation based on format).  
    \* Populates \`team1\_id\`, \`team2\_id\`, \`placeholder\_team1\_text\`, and \`placeholder\_team2\_text\` based on the seeding logic and the output of \`determine\_advancing\_teams\`.

\#\# 5\. Core Logic and Processing Steps

\#\#\# 5.1 Standings Calculation (\`refresh\_standings\` \- Triggered on Score Update)

\* This function (likely triggered after each match score update) calculates and updates the \`standings\` table for the relevant group.  
\* It aggregates points (win=3, draw=1, loss=0), sums goals for (GF) and goals against (GA), and calculates goal difference (GD).  
\* It applies a standard ranking within the group based on: Points \-\> GD \-\> GF. (Head-to-head is handled later if needed).

\#\#\# 5.2 Determining Advancing Teams (\`determine\_advancing\_teams\` Function)

1\.  \*\*Fetch Configuration:\*\* Retrieves \`playoff\_format\` and \`advancing\_teams\_count\` from \`tournament\_age\_groups.custom\_settings\` for the given \`p\_tournament\_age\_group\_id\`.  
2\.  \*\*Fetch Ranked Standings:\*\* Queries the \`standings\` table for the specified tournament and age group, ordering teams within each group by Points, GD, GF.  
3\.  \*\*Identify Potential Qualifiers:\*\* Based on the \`playoff\_format\`:  
    \* \*\*Group Winners/Runners-up:\*\* Selects the top N teams from each group (e.g., top 2 for an 8-team bracket from 4 groups).  
    \* \*\*Wildcards (Best 3rd/4th Place, etc.):\*\* If the format requires wildcards (e.g., 10 teams advancing from 3 groups), it selects the necessary number of lower-ranked teams across all groups based on the tie-breaking criteria.  
4\.  \*\*Apply Tiebreakers (Across Groups if necessary):\*\* If multiple teams across different groups have identical primary ranking criteria (Points, GD, GF) and are competing for the final advancement spots (wildcards), apply further tiebreakers in order:  
    \* Goals Against (Lower is better)  
    \* Fair Play Points (Requires tracking \- \*may not be fully implemented based on schema\*)  
    \* Drawing of Lots (Requires manual intervention or a deterministic pseudo-random method based on IDs if full automation is desired). \*Note: Head-to-head is typically only applied within a group, not usually across groups for wildcard selection.\*  
5\.  \*\*Assign Overall Seed:\*\* Ranks the identified advancing teams from 1 to \`advancing\_teams\_count\` based on the full tie-breaking sequence (Points \-\> GD \-\> GF \-\> GA \-\> etc.).  
6\.  \*\*Generate Seed Description:\*\* Creates a descriptive label for each advancing team (e.g., "Winner Group A", "Runner-up Group B", "Wildcard \#1").  
7\.  \*\*Return Results:\*\* Returns the table of advancing teams with their calculated \`seed\_position\` and \`seed\_description\`.

\#\#\# 5.3 Seeding the Playoff Bracket (\`seed\_playoff\_bracket\` Procedure)

1\.  \*\*Fetch Advancing Teams:\*\* Calls \`determine\_advancing\_teams(p\_tournament\_id, p\_tournament\_age\_group\_id)\` to get the ranked list of qualifiers.  
2\.  \*\*Clear Existing Playoff Matches (Optional):\*\* May delete existing playoff matches for the age group if re-seeding is intended (requires careful handling if matches have started).  
3\.  \*\*Create Playoff Matches:\*\* Based on the \`playoff\_format\` (e.g., "8\_team", "4\_team\_2\_groups") and \`advancing\_teams\_count\`:  
    \* Inserts rows into the \`matches\` table for each playoff round (e.g., Quarterfinals, Semifinals, Final, Consolation).  
    \* Sets the \`round\_name\`, \`match\_number\` (within the round), \`tournament\_id\`, \`tournament\_age\_group\_id\`.  
    \* Sets initial \`status\` to 'scheduled' (or perhaps 'pending\_teams').  
    \* Sets \`scheduled\_time\` and \`field\_id\` to NULL initially (these are typically scheduled separately or manually).  
4\.  \*\*Populate Match Participants:\*\* Assigns teams/placeholders to the \`team1\_id\`/\`team2\_id\` or \`placeholder\_team1\_text\`/\`placeholder\_team2\_text\` columns based on standard seeding rules:  
    \* \*\*Example (8-Team Bracket):\*\*  
        \* QF1: Seed 1 vs Seed 8  
        \* QF2: Seed 4 vs Seed 5  
        \* QF3: Seed 2 vs Seed 7  
        \* QF4: Seed 3 vs Seed 6  
        \* SF1: Winner QF1 vs Winner QF2  
        \* SF2: Winner QF3 vs Winner QF4  
        \* Final: Winner SF1 vs Winner SF2  
        \* Consolation (if \`include\_consolation\` is true): Loser SF1 vs Loser SF2  
    \* Uses the \`seed\_description\` from \`determine\_advancing\_teams\` for placeholder text (e.g., \`placeholder\_team1\_text \= 'Winner Group A'\`).  
    \* If the advancing team is definitively known (e.g., only one team qualifies from a group), it might directly populate the \`team1\_id\`/\`team2\_id\`. \*Based on \`test\_playoff\_advancement.sql\`, it seems placeholders are used initially and then potentially updated.\*  
5\.  \*\*Update Placeholders (Post-Group Stage):\*\* A separate process or trigger (potentially \`update\_playoff\_match\_teams\` function, not explicitly seen but implied) would likely run after group stage matches affecting advancement are completed. This process would:  
    \* Query the results of \`determine\_advancing\_teams\`.  
    \* Update the \`team1\_id\` and \`team2\_id\` fields in the playoff \`matches\` table, replacing placeholders with actual team IDs once they are confirmed.

\#\# 6\. Key Data Structures

\* \*\*\`standings\` Table:\*\* Stores calculated group stage rankings. Crucial input.  
\* \*\*\`matches\` Table:\*\* Stores group stage results (input for tiebreakers) and is populated with playoff matches (output). Contains \`placeholder\_team1\_text\` and \`placeholder\_team2\_text\` for seeding.  
\* \*\*\`tournament\_age\_groups\` Table:\*\* Stores \`custom\_settings\` JSONB containing \`playoff\_format\`, \`advancing\_teams\_count\`, etc.  
\* \*\*Return Type of \`determine\_advancing\_teams\`:\*\* Table containing ranked advancing teams with seed information.

\#\# 7\. Tie-Breaking Logic (within \`determine\_advancing\_teams\`)

The standard tie-breaking order applied when ranking teams (both within groups and for wildcards) appears to be:

1\.  \*\*Points:\*\* Total points accumulated.  
2\.  \*\*Goal Difference (GD):\*\* Goals For (GF) minus Goals Against (GA).  
3\.  \*\*Goals For (GF):\*\* Total goals scored.  
4\.  \*\*Head-to-Head (H2H):\*\* Result(s) of matches played \*only\* between the tied teams. (Requires specific logic to query relevant matches).  
5\.  \*\*Goals Against (GA):\*\* Fewest goals conceded overall (sometimes used before Fair Play).  
6\.  \*\*Fair Play Points:\*\* Based on yellow/red cards (Requires tracking card data, which may not be present in the current schema).  
7\.  \*\*Drawing of Lots:\*\* Final resort, likely requires manual intervention or a deterministic method (e.g., based on team ID).

\*Note: The exact implementation details of H2H and Fair Play within the SQL functions need verification against the \`supabase\_full\_schema\_and\_logic\_production\_v2.sql\` file.\*

\#\# 8\. Error Handling

\* \*\*Incomplete Standings:\*\* If \`refresh\_standings\` hasn't run or group stage matches aren't marked 'completed', \`determine\_advancing\_teams\` may return incorrect or incomplete results. The calling process should verify prerequisites.  
\* \*\*Invalid Configuration:\*\* Missing or invalid \`playoff\_format\` or \`advancing\_teams\_count\` in \`custom\_settings\` should cause the functions to raise an error or return a clear failure indication.  
\* \*\*Ambiguous Tiebreakers:\*\* If teams remain tied after all implemented automatic tiebreakers, the system might require manual intervention (for drawing lots) or use a deterministic fallback (like team ID). The functions should ideally report such ambiguities.  
\* \*\*Concurrency:\*\* Ensure transactions are used appropriately if multiple processes could potentially modify standings or trigger playoff generation concurrently (less likely if triggered manually or by a single background task).

\#\# 9\. Performance Considerations

\* The \`determine\_advancing\_teams\` function involves potentially complex queries and ranking logic, especially with many groups and wildcard scenarios. Ensure underlying tables (\`standings\`, \`matches\`) are appropriately indexed (on \`tournament\_id\`, \`tournament\_age\_group\_id\`, \`group\_id\`, \`team\_id\`).  
\* The \`seed\_playoff\_bracket\` procedure involves multiple INSERT/UPDATE statements. Running it within a transaction is recommended.

\#\# 10\. Future Improvements / Considerations

\* \*\*Configurable Tiebreakers:\*\* Allow Tournament Directors to customize the tie-breaking order via settings.  
\* \*\*More Playoff Formats:\*\* Add support for more complex or less common playoff structures (e.g., double elimination, specific league formats).  
\* \*\*UI Integration:\*\* Ensure the Flutter UI clearly displays the calculated standings, tiebreaker applications, advancing teams, seeding, and the generated playoff bracket (\`BracketView\`).  
\* \*\*Triggering Mechanism:\*\* Clarify and potentially refine \*when\* the playoff generation logic is triggered (e.g., automatically upon completion of all group games vs. manual TD action).

