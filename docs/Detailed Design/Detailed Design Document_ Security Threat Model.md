\# Detailed Design Document: Security Threat Model

\*\*Version:\*\* 1.0  
\*\*Date:\*\* April 28, 2025  
\*\*Related Requirements:\*\* NFR2 (Security), FR1 (Authentication), FR10 (Payment Processing), FR11 (Admin Functions), FR12 (Audit Logging)

\#\# 1\. Introduction

\#\#\# 1.1 Purpose  
This document identifies, assesses, and proposes mitigations for potential security threats to the Soccer Tournament Management Platform. The goal is to proactively identify vulnerabilities and ensure appropriate security controls are implemented throughout the system's architecture and components to protect data confidentiality, integrity, and system availability.

\#\#\# 1.2 Scope  
This threat model covers the primary components of the platform:  
\* Flutter Frontend (Web/Mobile)  
\* Supabase Backend (Auth, Database, Edge Functions, Storage)  
\* Python Backend Service (FastAPI API, Celery Workers)  
\* Supporting infrastructure (Redis, GCP Cloud Run)  
\* Third-party integrations (Stripe)

It focuses on threats related to data confidentiality, integrity, availability, authentication, and authorization across the identified data flows and trust boundaries.

\#\#\# 1.3 Methodology  
This threat model primarily utilizes the \*\*STRIDE\*\* methodology, analyzing potential threats across the following categories:  
\* \*\*S\*\*poofing: Illegitimately accessing or claiming user identities or service identities.  
\* \*\*T\*\*ampering: Maliciously modifying data in transit or at rest, or manipulating code execution.  
\* \*\*R\*\*epudiation: Denying having performed an action when they did, or falsely claiming an action was performed.  
\* \*\*I\*\*nformation Disclosure: Exposing sensitive information to unauthorized parties.  
\* \*\*D\*\*enial of Service (DoS): Making a system or resource unavailable to legitimate users.  
\* \*\*E\*\*levation of Privilege: Gaining capabilities or permissions beyond those authorized.

The analysis considers the system's architecture, data flows, and key functionalities.

\#\#\# 1.4 Glossary  
\*(Refer to main SRS Glossary and previous DDDs)\*  
\* \*\*STRIDE:\*\* Threat modeling framework (Spoofing, Tampering, Repudiation, Information Disclosure, Denial of Service, Elevation of Privilege).  
\* \*\*XSS:\*\* Cross-Site Scripting.  
\* \*\*CSRF:\*\* Cross-Site Request Forgery.  
\* \*\*SQLi:\*\* SQL Injection.  
\* \*\*DoS:\*\* Denial of Service.  
\* \*\*DDoS:\*\* Distributed Denial of Service.  
\* \*\*PII:\*\* Personally Identifiable Information.  
\* \*\*RLS:\*\* Row Level Security.  
\* \*\*JWT:\*\* JSON Web Token.

\#\# 2\. System Overview for Threat Modeling

\*(Refer to Architecture Design Document Section 3 for diagram and component descriptions)\*

\*\*Key Assets to Protect:\*\*  
\* User credentials (passwords managed by Supabase Auth; JWTs).  
\* Personally Identifiable Information (PII): Names, emails, phone numbers, potentially player DOBs, addresses (stored in Supabase \`profiles\`, \`players\`, \`referees\` tables).  
\* Payment information identifiers (Stripe IDs, transaction details in \`transactions\` table). Full card details are NOT stored.  
\* Tournament data integrity: Schedules, scores, standings, team/player registrations.  
\* System configuration: Tournament settings, user roles, API keys, secrets.  
\* Service availability: Ensuring frontend, Supabase, and backend services remain operational.

\*\*Trust Boundaries & Data Flows:\*\*

1\.  \*\*User \<-\> Flutter Frontend:\*\* User interacts via browser/mobile app. Data flows over HTTPS.  
2\.  \*\*Flutter Frontend \<-\> Supabase:\*\* Direct communication using Supabase SDKs over HTTPS/WSS. Authenticated via JWTs (or Anon Key for public data).  
3\.  \*\*Flutter Frontend \<-\> Python FastAPI:\*\* Authenticated API calls over HTTPS using Supabase JWTs.  
4\.  \*\*Python Service (API/Worker) \<-\> Supabase DB:\*\* Uses Supabase Python SDK with Service Role Key over secure connection.  
5\.  \*\*Python Service (API/Worker) \<-\> Redis:\*\* Internal network communication (ideally within VPC/secured).  
6\.  \*\*Supabase Edge Function \<-\> Supabase DB:\*\* Internal Supabase communication, likely using Service Role Key.  
7\.  \*\*Supabase Edge Function \<-\> Stripe API:\*\* Outbound HTTPS calls using Stripe Secret Key.  
8\.  \*\*Stripe \<-\> Supabase Edge Function:\*\* Inbound HTTPS webhook calls.

\#\# 3\. Threat Analysis and Mitigations

This section analyzes potential threats using STRIDE across the different components and interactions.

\#\#\# 3.1 Spoofing

| Threat Scenario                                      | Component(s) Affected         | Mitigation(s)                                                                                                                                                                                                                                                                                                                                                                                                                                                  | Requirement(s) Addressed |  
| :--------------------------------------------------- | :---------------------------- | :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :----------------------- |  
| \*\*User Impersonation (Credential Theft)\*\* | Flutter Frontend, Supabase Auth | \- Strong password policies enforced by Supabase Auth (configurable). \<br\> \- Secure password reset mechanism with email verification (\`recommended\_reset\_password\_screen.dart\`). \<br\> \- Rate limiting on login/reset attempts (\`RateLimiter\` class, Supabase Auth limits). \<br\> \- \*\*Recommendation:\*\* Enable Multi-Factor Authentication (MFA) in Supabase Auth. \<br\> \- \*\*Recommendation:\*\* Educate users about phishing and password security. | FR1.1, FR1.2, FR1.3, NFR2 |  
| \*\*JWT Token Theft/Replay\*\* | Flutter Frontend, FastAPI API | \- Use HTTPS for all communication to prevent token interception. \<br\> \- Short access token expiry (managed by Supabase Auth), rely on refresh tokens. \<br\> \- Secure storage of tokens on the client (handled by \`supabase-flutter\`). \<br\> \- \*\*Recommendation:\*\* Implement refresh token rotation in Supabase Auth settings if not default. \<br\> \- FastAPI validates token signature and expiry (\`security.py\`).                                             | FR1.6, FR1.7, NFR2.1     |  
| \*\*Forged API Requests (Impersonating Frontend)\*\* | Python FastAPI API            | \- Strict JWT signature verification using the shared \`SUPABASE\_JWT\_SECRET\` (\`security.py\`). \<br\> \- Validate token claims (e.g., \`aud\`, \`iss\`).                                                                                                                                                                                                                                                                                                                      | FR1.9.2, NFR2.2          |  
| \*\*Forged Stripe Webhook Requests\*\* | Supabase Edge Function        | \- \*\*Crucial:\*\* Strict verification of the \`Stripe-Signature\` header using the \`STRIPE\_WEBHOOK\_SECRET\` (\`index.ts\`). Reject any request without a valid signature.                                                                                                                                                                                                                                                                                                       | FR10.2, NFR2.2           |  
| \*\*Spoofed Service Identity (Internal \- e.g., Redis)\*\* | Python Backend Service        | \- Run services within a secure network environment (e.g., GCP VPC). \<br\> \- Use network policies/firewalls to restrict access to Redis only from authorized Cloud Run services. \<br\> \- \*\*Recommendation:\*\* Configure Redis authentication if supported by Memorystore tier/configuration.                                                                                                                                                                | NFR2.6                   |

\#\#\# 3.2 Tampering

| Threat Scenario                                      | Component(s) Affected         | Mitigation(s) Implemented/Recommended                                                                                                                                                                                                                                                                                                                                                                                                                                                  | Requirement(s) Addressed |  
| :--------------------------------------------------- | :---------------------------- | :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :----------------------- |  
| \*\*Data Modification in Transit\*\* | All communication channels    | \- Enforce HTTPS for all communication between Frontend \<-\> Supabase, Frontend \<-\> FastAPI, Supabase \<-\> Stripe. \<br\> \- Ensure internal communication (FastAPI \<-\> Redis, FastAPI/Celery \<-\> Supabase) uses secure connections if traversing untrusted networks (often handled within cloud provider infrastructure).                                                                                                                                                                 | NFR2.1, NFR2.4           |  
| \*\*Client-Side Parameter Tampering\*\* | Flutter Frontend, Backend APIs | \- \*\*Primary Mitigation:\*\* All critical validation (permissions, data integrity, business rules) MUST occur server-side (Supabase RLS/Functions, FastAPI API logic). \<br\> \- Do not trust data coming directly from the client without server-side verification. \<br\> \- Use strong typing and validation on API inputs (FastAPI Pydantic models).                                                                                                                                       | FR1.9, NFR2.4            |  
| \*\*Database Record Tampering (Unauthorized)\*\* | Supabase DB                   | \- \*\*Primary Mitigation:\*\* Strict Row Level Security (RLS) policies on all sensitive tables (\`profiles\`, \`tournaments\`, \`matches\`, \`registrations\`, etc.) based on \`auth.uid()\` and roles. \<br\> \- Grant minimal necessary privileges to database roles/users. \<br\> \- Regularly review and audit RLS policies (\`supabase\_schema\_v2\_part2\_auth\_rls.sql\`, \`tournament\_rls\_policies.sql\`). \<br\> \- Use Service Role Key only where absolutely necessary (backend services, functions). | FR1.9.1, NFR2.2          |  
| \*\*Log Tampering\*\* | Logging Infrastructure        | \- Use centralized, immutable logging services (e.g., GCP Cloud Logging, Supabase Logs). \<br\> \- Restrict access to log modification/deletion.                                                                                                                                                                                                                                                                                                                                       | NFR2.6, FR12.1           |  
| \*\*Webhook Payload Tampering\*\* | Supabase Edge Function        | \- Stripe webhook signature verification prevents processing of tampered payloads.                                                                                                                                                                                                                                                                                                                                                                                                      | FR10.2, NFR2.2           |

\#\#\# 3.3 Repudiation

| Threat Scenario                                             | Component(s) Affected         | Mitigation(s) Implemented/Recommended                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              \*/\_le\_1\_T\_t1\_id\_code\_t team1\_                                                 C\_INT,  
  T\_IDENTIFICACION\_EQUIPO team2\_id,  
  INOUT p\_result                                      INT,  
  INOUT p\_msg\_error                                 VARCHAR(255)  
)  
AS $$  
DECLARE  
  v\_count INT;  
BEGIN  
  \-- Verificar si el equipo 1 existe  
  SELECT COUNT(\*) INTO v\_count FROM public.teams WHERE id \= team1\_id;  
  IF v\_count \= 0 THEN  
    p\_result := 1;  
    p\_msg\_error := 'El equipo 1 no existe';  
    RETURN;  
  END IF;

  \-- Verificar si el equipo 2 existe  
  SELECT COUNT(\*) INTO v\_count FROM public.teams WHERE id \= team2\_id;  
  IF v\_count \= 0 THEN  
    p\_result := 1;  
    p\_msg\_error := 'El equipo 2 no existe';  
    RETURN;  
  END IF;

  \-- Verificar si los equipos son diferentes  
  IF team1\_id \= team2\_id THEN  
    p\_result := 1;  
    p\_msg\_error := 'El equipo 1 y el equipo 2 no pueden ser iguales';  
    RETURN;  
  END IF;  
END;  
$$ LANGUAGE plpgsql;

\-- Crear la función para insertar un nuevo partido  
CREATE OR REPLACE FUNCTION public.insert\_match(  
  p\_tournament\_id UUID,  
  p\_group\_id UUID,  
  p\_team1\_id UUID,  
  p\_team2\_id UUID,  
  p\_scheduled\_time TIMESTAMPTZ,  
  p\_field\_id UUID  
) RETURNS TABLE (  
  id UUID,  
  tournament\_id UUID,  
  group\_id UUID,  
  team1\_id UUID,  
  team2\_id UUID,  
  scheduled\_time TIMESTAMPTZ,  
  field\_id UUID,  
  status TEXT,  
  created\_at TIMESTAMPTZ,  
  updated\_at TIMESTAMPTZ  
) AS $$  
DECLARE  
  v\_validation\_result INT := 0;  
  v\_validation\_msg VARCHAR(255) := '';  
  new\_match RECORD;  
BEGIN  
  \-- Validar equipos  
  CALL public.validate\_teams(p\_team1\_id, p\_team2\_id, v\_validation\_result, v\_validation\_msg);  
  IF v\_validation\_result \!= 0 THEN  
    RAISE EXCEPTION '%', v\_validation\_msg;  
  END IF;

  \-- Validar torneo  
  IF NOT EXISTS (SELECT 1 FROM public.tournaments WHERE id \= p\_tournament\_id) THEN  
    RAISE EXCEPTION 'Tournament with ID % not found', p\_tournament\_id;  
  END IF;

  \-- Validar grupo (opcional, si p\_group\_id no es NULL)  
  IF p\_group\_id IS NOT NULL AND NOT EXISTS (SELECT 1 FROM public.tournament\_groups WHERE id \= p\_group\_id AND tournament\_id \= p\_tournament\_id) THEN  
    RAISE EXCEPTION 'Group with ID % not found in tournament %', p\_group\_id, p\_tournament\_id;  
  END IF;

  \-- Validar campo (opcional, si p\_field\_id no es NULL)  
  IF p\_field\_id IS NOT NULL AND NOT EXISTS (SELECT 1 FROM public.fields WHERE id \= p\_field\_id) THEN  
    RAISE EXCEPTION 'Field with ID % not found', p\_field\_id;  
  END IF;

  \-- Validar horario (opcional, si p\_scheduled\_time no es NULL)  
  \-- Podrías añadir lógica para verificar si el horario está dentro del rango del torneo, etc.

  \-- Insertar el nuevo partido  
  INSERT INTO public.matches (  
    tournament\_id,  
    group\_id,  
    team1\_id,  
    team2\_id,  
    scheduled\_time,  
    field\_id,  
    status  
  ) VALUES (  
    p\_tournament\_id,  
    p\_group\_id,  
    p\_team1\_id,  
    p\_team2\_id,  
    p\_scheduled\_time,  
    p\_field\_id,  
    'scheduled' \-- Estado inicial  
  ) RETURNING \* INTO new\_match;

  RETURN QUERY SELECT \* FROM public.matches WHERE id \= new\_match.id;  
END;  
$$ LANGUAGE plpgsql;

\-- Function to update match score and determine winner  
CREATE OR REPLACE FUNCTION public.update\_match\_score(  
    p\_match\_id UUID,  
    p\_team1\_score INT,  
    p\_team2\_score INT,  
    p\_status TEXT DEFAULT 'completed' \-- Allow setting status, default to 'completed'  
)  
RETURNS VOID  
LANGUAGE plpgsql  
SECURITY DEFINER \-- Important if you need to bypass RLS for updates  
AS $$  
DECLARE  
    v\_winner\_id UUID;  
    v\_loser\_id UUID;  
    v\_is\_draw BOOLEAN := FALSE;  
    v\_tournament\_id UUID;  
    v\_group\_id UUID;  
    v\_team1\_id UUID;  
    v\_team2\_id UUID;  
BEGIN  
    \-- Validate inputs  
    IF p\_match\_id IS NULL THEN  
        RAISE EXCEPTION 'Match ID cannot be NULL';  
    END IF;  
    IF p\_team1\_score IS NULL OR p\_team1\_score \< 0 THEN  
        RAISE EXCEPTION 'Team 1 score must be a non-negative integer';  
    END IF;  
    IF p\_team2\_score IS NULL OR p\_team2\_score \< 0 THEN  
        RAISE EXCEPTION 'Team 2 score must be a non-negative integer';  
    END IF;

    \-- Get match details  
    SELECT  
        matches.tournament\_id, matches.group\_id, matches.team1\_id, matches.team2\_id  
    INTO  
        v\_tournament\_id, v\_group\_id, v\_team1\_id, v\_team2\_id  
    FROM public.matches  
    WHERE id \= p\_match\_id;

    IF NOT FOUND THEN  
        RAISE EXCEPTION 'Match with ID % not found', p\_match\_id;  
    END IF;

    \-- Determine winner and loser  
    IF p\_team1\_score \> p\_team2\_score THEN  
        v\_winner\_id := v\_team1\_id;  
        v\_loser\_id := v\_team2\_id;  
    ELSIF p\_team2\_score \> p\_team1\_score THEN  
        v\_winner\_id := v\_team2\_id;  
        v\_loser\_id := v\_team1\_id;  
    ELSE  
        v\_winner\_id := NULL;  
        v\_loser\_id := NULL;  
        v\_is\_draw := TRUE;  
    END IF;

    \-- Update the match record  
    UPDATE public.matches  
    SET  
        team1\_score \= p\_team1\_score,  
        team2\_score \= p\_team2\_score,  
        winner\_id \= v\_winner\_id,  
        loser\_id \= v\_loser\_id,  
        is\_draw \= v\_is\_draw,  
        status \= p\_status, \-- Update status  
        updated\_at \= timezone('utc', now())  
    WHERE id \= p\_match\_id;

    \-- Optionally, trigger standings update if it's a group stage match  
    IF v\_group\_id IS NOT NULL AND p\_status \= 'completed' THEN  
        PERFORM public.refresh\_standings(v\_tournament\_id, v\_group\_id);  
    END IF;

    \-- Optionally, trigger playoff match update if this was a prerequisite match  
    IF v\_group\_id IS NULL AND p\_status \= 'completed' THEN \-- Assuming NULL group\_id means playoff  
        PERFORM public.update\_playoff\_match\_teams(p\_match\_id);  
    END IF;

END;  
$$;

\-- Function to refresh standings for a specific group  
CREATE OR REPLACE FUNCTION public.refresh\_standings(  
    p\_tournament\_id UUID,  
    p\_group\_id UUID  
)  
RETURNS VOID  
LANGUAGE plpgsql  
SECURITY DEFINER \-- May need elevated privileges to write to standings  
AS $$  
DECLARE  
    team\_record RECORD;  
    perf RECORD;  
BEGIN  
    \-- Delete existing standings for this group to recalculate  
    DELETE FROM public.standings  
    WHERE tournament\_id \= p\_tournament\_id AND group\_id \= p\_group\_id;

    \-- Loop through teams registered in this group  
    FOR team\_record IN  
        SELECT t.id as team\_id  
        FROM public.teams t  
        JOIN public.registrations r ON t.id \= r.team\_id  
        WHERE r.tournament\_age\_group\_id \= (SELECT tournament\_age\_group\_id FROM public.tournament\_groups WHERE id \= p\_group\_id)  
          AND r.assigned\_group\_id \= p\_group\_id  
    LOOP  
        \-- Calculate performance for the current team  
        SELECT \* INTO perf FROM public.calculate\_team\_performance(p\_tournament\_id, p\_group\_id, team\_record.team\_id);

        \-- Insert or update the standings row  
        INSERT INTO public.standings (  
            tournament\_id,  
            group\_id,  
            team\_id,  
            played,  
            wins,  
            draws,  
            losses,  
            goals\_for,  
            goals\_against,  
            goal\_difference,  
            points  
        )  
        VALUES (  
            p\_tournament\_id,  
            p\_group\_id,  
            team\_record.team\_id,  
            perf.games\_played,  
            perf.wins,  
            perf.draws,  
            perf.losses,  
            perf.goals\_for,  
            perf.goals\_against,  
            perf.goal\_difference,  
            perf.points  
        );  
    END LOOP;  
END;  
$$;

\-- Function to calculate performance stats for a single team within a group  
CREATE OR REPLACE FUNCTION public.calculate\_team\_performance(  
    p\_tournament\_id UUID,  
    p\_group\_id UUID,  
    p\_team\_id UUID  
)  
RETURNS TABLE (  
    games\_played INT,  
    wins INT,  
    draws INT,  
    losses INT,  
    goals\_for INT,  
    goals\_against INT,  
    goal\_difference INT,  
    points INT  
)  
LANGUAGE plpgsql  
AS $$  
DECLARE  
    v\_max\_goal\_diff INT := 4; \-- Example: Cap goal difference per match if needed by rules  
BEGIN  
    RETURN QUERY  
    WITH team\_matches AS (  
        SELECT  
            m.id,  
            m.team1\_id,  
            m.team2\_id,  
            m.team1\_score,  
            m.team2\_score,  
            m.winner\_id,  
            m.loser\_id,  
            m.is\_draw,  
            m.forfeit\_winner\_id,  
            m.status  
        FROM  
            public.matches m  
        WHERE  
            m.tournament\_id \= p\_tournament\_id  
            AND m.group\_id \= p\_group\_id  
            AND m.status \= 'completed' \-- Only count completed matches  
            AND (m.team1\_id \= p\_team\_id OR m.team2\_id \= p\_team\_id)  
    )  
    SELECT  
        COUNT(id)::INT AS games\_played,  
        SUM(CASE  
                WHEN forfeit\_winner\_id \= p\_team\_id THEN 1  
                WHEN winner\_id \= p\_team\_id THEN 1  
                ELSE 0  
            END)::INT AS wins,  
        SUM(CASE WHEN is\_draw THEN 1 ELSE 0 END)::INT AS draws,  
        SUM(CASE  
                WHEN forfeit\_winner\_id IS NOT NULL AND forfeit\_winner\_id \<\> p\_team\_id THEN 1  
                WHEN loser\_id \= p\_team\_id THEN 1  
                ELSE 0  
            END)::INT AS losses,  
        SUM(CASE  
                WHEN team1\_id \= p\_team\_id THEN team1\_score  
                ELSE team2\_score  
            END)::INT AS goals\_for,  
        SUM(CASE  
                WHEN team1\_id \= p\_team\_id THEN team2\_score  
                ELSE team1\_score  
            END)::INT AS goals\_against,  
        SUM(  
            CASE  
                WHEN team1\_id \= p\_team\_id THEN LEAST(team1\_score \- team2\_score, v\_max\_goal\_diff)  
                ELSE LEAST(team2\_score \- team1\_score, v\_max\_goal\_diff)  
            END  
        )::INT AS goal\_difference, \-- Consider if capping GD per match is needed  
        SUM(  
            CASE  
                WHEN forfeit\_winner\_id \= p\_team\_id THEN 3  
                WHEN winner\_id \= p\_team\_id THEN 3  
                WHEN is\_draw THEN 1  
                ELSE 0  
            END  
        )::INT AS points  
    FROM team\_matches;  
END;  
$$;

\-- Function to get Head-to-Head results between two teams  
CREATE OR REPLACE FUNCTION public.get\_h2h\_result(  
    p\_tournament\_id UUID,  
    p\_group\_id UUID,  
    p\_team\_a\_id UUID,  
    p\_team\_b\_id UUID  
)  
RETURNS TABLE (  
    winner UUID,  
    loser UUID,  
    is\_draw BOOLEAN,  
    team\_a\_score INT,  
    team\_b\_score INT  
)  
LANGUAGE plpgsql  
AS $$  
BEGIN  
    RETURN QUERY  
    SELECT  
        CASE  
            WHEN m.winner\_id \= p\_team\_a\_id THEN p\_team\_a\_id  
            WHEN m.winner\_id \= p\_team\_b\_id THEN p\_team\_b\_id  
            ELSE NULL  
        END AS winner,  
        CASE  
            WHEN m.loser\_id \= p\_team\_a\_id THEN p\_team\_a\_id  
            WHEN m.loser\_id \= p\_team\_b\_id THEN p\_team\_b\_id  
            ELSE NULL  
        END AS loser,  
        m.is\_draw,  
        CASE WHEN m.team1\_id \= p\_team\_a\_id THEN m.team1\_score ELSE m.team2\_score END AS team\_a\_score,  
        CASE WHEN m.team1\_id \= p\_team\_b\_id THEN m.team1\_score ELSE m.team2\_score END AS team\_b\_score  
    FROM public.matches m  
    WHERE m.tournament\_id \= p\_tournament\_id  
      AND m.group\_id \= p\_group\_id  
      AND m.status \= 'completed'  
      AND (  
          (m.team1\_id \= p\_team\_a\_id AND m.team2\_id \= p\_team\_b\_id) OR  
          (m.team1\_id \= p\_team\_b\_id AND m.team2\_id \= p\_team\_a\_id)  
      )  
    LIMIT 1; \-- Assuming only one match between two teams in a group stage  
END;  
$$;

\-- Function: Determine Advancing Teams (incorporates tiebreakers)  
CREATE OR REPLACE FUNCTION public.determine\_advancing\_teams(  
    p\_tournament\_id UUID,  
    p\_tournament\_age\_group\_id UUID  
)  
RETURNS TABLE (  
    team\_id UUID,  
    team\_name TEXT,  
    group\_id UUID,  
    group\_name TEXT,  
    seed\_position INT,  
    seed\_description TEXT,  
    points INT,  
    goal\_difference INT,  
    goals\_for INT,  
    goals\_against INT,  
    group\_position INT  
)  
LANGUAGE plpgsql SECURITY DEFINER AS $$  
DECLARE  
    v\_playoff\_format TEXT;  
    v\_advancing\_count INT;  
    v\_group\_count INT;  
    v\_teams\_per\_group\_map JSONB; \-- Store avg teams per group for format logic  
    v\_record RECORD;  
    v\_rank BIGINT;  
BEGIN  
    \-- Get tournament age group format and settings  
    SELECT COALESCE(tag.custom\_settings \-\>\> 'playoff\_format', '8\_team'), \-- Default if not set  
           COALESCE((tag.custom\_settings \-\>\> 'advancing\_teams\_count')::INT, 8\) \-- Default if not set  
    INTO v\_playoff\_format, v\_advancing\_count  
    FROM public.tournament\_age\_groups tag  
    WHERE tag.id \= p\_tournament\_age\_group\_id;

    IF NOT FOUND THEN  
        RAISE EXCEPTION 'Tournament Age Group ID % not found.', p\_tournament\_age\_group\_id;  
    END IF;

    \-- Refresh standings for all relevant groups first (ensure data is current)  
    FOR v\_record IN  
        SELECT id FROM public.tournament\_groups WHERE tournament\_age\_group\_id \= p\_tournament\_age\_group\_id  
    LOOP  
        PERFORM public.refresh\_standings(p\_tournament\_id, v\_record.id);  
    END LOOP;

    \-- Create a temporary table or CTE to hold ranked teams with H2H tiebreaker logic applied if needed  
    \-- NOTE: Implementing full multi-team H2H tiebreakers within SQL is complex.  
    \-- This example uses a simplified ranking based on standard criteria.  
    \-- A more robust solution might involve procedural logic or calling out to an external service.  
    RETURN QUERY  
    WITH RankedTeams AS (  
        SELECT  
            s.team\_id,  
            t.name AS team\_name,  
            s.group\_id,  
            g.name AS group\_name,  
            s.points,  
            s.goal\_difference,  
            s.goals\_for,  
            s.goals\_against,  
            s.wins,  
            s.draws,  
            s.losses,  
            s.played,  
            \-- Rank within the group first  
            RANK() OVER (PARTITION BY s.group\_id ORDER BY s.points DESC, s.goal\_difference DESC, s.goals\_for DESC, s.goals\_against ASC, t.name ASC) as group\_rank  
        FROM public.standings s  
        JOIN public.teams t ON s.team\_id \= t.id  
        JOIN public.tournament\_groups g ON s.group\_id \= g.id  
        WHERE s.tournament\_id \= p\_tournament\_id  
          AND g.tournament\_age\_group\_id \= p\_tournament\_age\_group\_id  
    ),  
    \-- Add logic here to handle potential head-to-head tiebreakers if needed,  
    \-- which might involve creating mini-leagues for tied teams.  
    \-- This simplified version ranks directly based on overall stats.  
    OverallRankedTeams AS (  
      SELECT  
        rt.\*,  
        RANK() OVER (ORDER BY rt.group\_rank ASC, rt.points DESC, rt.goal\_difference DESC, rt.goals\_for DESC, rt.goals\_against ASC, rt.team\_name ASC) as overall\_rank  
      FROM RankedTeams rt  
    )  
    SELECT  
        ort.team\_id,  
        ort.team\_name,  
        ort.group\_id,  
        ort.group\_name,  
        ort.overall\_rank::INT AS seed\_position,  
        \-- Generate a basic seed description (can be enhanced based on playoff\_format)  
        CASE  
            WHEN ort.group\_rank \= 1 THEN 'Winner ' || ort.group\_name  
            WHEN ort.group\_rank \= 2 THEN 'Runner-up ' || ort.group\_name  
            WHEN ort.group\_rank \= 3 THEN '3rd Place ' || ort.group\_name  
            WHEN ort.group\_rank \= 4 THEN '4th Place ' || ort.group\_name  
            ELSE 'Rank ' || ort.group\_rank::TEXT || ' ' || ort.group\_name  
        END || ' (Seed ' || ort.overall\_rank::TEXT || ')' AS seed\_description,  
        ort.points,  
        ort.goal\_difference,  
        ort.goals\_for,  
        ort.goals\_against,  
        ort.group\_rank::INT AS group\_position  
    FROM OverallRankedTeams ort  
    ORDER BY ort.overall\_rank  
    LIMIT v\_advancing\_count;

END;  
$$;

\-- Procedure: Seed Playoff Bracket  
CREATE OR REPLACE PROCEDURE public.seed\_playoff\_bracket(  
    p\_tournament\_id UUID,  
    p\_tournament\_age\_group\_id UUID  
)  
LANGUAGE plpgsql SECURITY DEFINER AS $$  
DECLARE  
    v\_advancing\_teams RECORD;  
    v\_playoff\_format TEXT;  
    v\_advancing\_count INT;  
    v\_include\_consolation BOOLEAN;  
    v\_custom\_settings JSONB;  
    v\_seed\_map HSTORE; \-- Using hstore to map seed position to team info  
    v\_seed\_key TEXT;  
    v\_team\_data JSONB;  
    v\_match\_id UUID;  
BEGIN  
    \-- Get tournament age group settings  
    SELECT custom\_settings INTO v\_custom\_settings  
    FROM public.tournament\_age\_groups  
    WHERE id \= p\_tournament\_age\_group\_id;

    IF NOT FOUND THEN  
        RAISE EXCEPTION 'Tournament Age Group ID % not found.', p\_tournament\_age\_group\_id;  
    END IF;

    v\_playoff\_format := COALESCE(v\_custom\_settings \-\>\> 'playoff\_format', '8\_team');  
    v\_advancing\_count := COALESCE((v\_custom\_settings \-\>\> 'advancing\_teams\_count')::INT, 8);  
    v\_include\_consolation := COALESCE((v\_custom\_settings \-\>\> 'include\_consolation')::BOOLEAN, false);

    \-- Optional: Delete existing playoff matches for this age group if re-seeding  
    \-- Add checks here if needed (e.g., check if matches have scores)  
    DELETE FROM public.matches  
    WHERE tournament\_id \= p\_tournament\_id  
      AND tournament\_age\_group\_id \= p\_tournament\_age\_group\_id  
      AND group\_id IS NULL; \-- Assuming playoff matches have NULL group\_id

    \-- Fetch and store advancing teams in a temporary structure (hstore for easy lookup)  
    v\_seed\_map := ''::hstore;  
    FOR v\_advancing\_teams IN  
        SELECT \* FROM public.determine\_advancing\_teams(p\_tournament\_id, p\_tournament\_age\_group\_id)  
    LOOP  
        v\_seed\_key := 'seed\_' || v\_advancing\_teams.seed\_position::TEXT;  
        v\_team\_data := jsonb\_build\_object(  
            'team\_id', v\_advancing\_teams.team\_id,  
            'placeholder', v\_advancing\_teams.seed\_description  
        );  
        v\_seed\_map := v\_seed\_map || hstore(v\_seed\_key, v\_team\_data::TEXT);  
    END LOOP;

    \-- Create playoff matches based on the format  
    \-- This requires specific logic for each supported format  
    \-- Example for "8\_team" format (Quarterfinals, Semifinals, Final)

    IF v\_playoff\_format \= '8\_team' AND v\_advancing\_count \>= 8 THEN  
        \-- Quarterfinals (QF)  
        INSERT INTO public.matches (tournament\_id, tournament\_age\_group\_id, round\_name, match\_number, placeholder\_team1\_text, placeholder\_team2\_text, status) VALUES  
        (p\_tournament\_id, p\_tournament\_age\_group\_id, 'quarterfinal', 1, (v\_seed\_map \-\> 'seed\_1')::jsonb \-\>\> 'placeholder', (v\_seed\_map \-\> 'seed\_8')::jsonb \-\>\> 'placeholder', 'scheduled'),  
        (p\_tournament\_id, p\_tournament\_age\_group\_id, 'quarterfinal', 2, (v\_seed\_map \-\> 'seed\_4')::jsonb \-\>\> 'placeholder', (v\_seed\_map \-\> 'seed\_5')::jsonb \-\>\> 'placeholder', 'scheduled'),  
        (p\_tournament\_id, p\_tournament\_age\_group\_id, 'quarterfinal', 3, (v\_seed\_map \-\> 'seed\_2')::jsonb \-\>\> 'placeholder', (v\_seed\_map \-\> 'seed\_7')::jsonb \-\>\> 'placeholder', 'scheduled'),  
        (p\_tournament\_id, p\_tournament\_age\_group\_id, 'quarterfinal', 4, (v\_seed\_map \-\> 'seed\_3')::jsonb \-\>\> 'placeholder', (v\_seed\_map \-\> 'seed\_6')::jsonb \-\>\> 'placeholder', 'scheduled');

        \-- Semifinals (SF) \- Placeholders reference previous round winners  
        INSERT INTO public.matches (tournament\_id, tournament\_age\_group\_id, round\_name, match\_number, placeholder\_team1\_text, placeholder\_team2\_text, status) VALUES  
        (p\_tournament\_id, p\_tournament\_age\_group\_id, 'semifinal', 1, 'Winner QF1', 'Winner QF2', 'scheduled'),  
        (p\_tournament\_id, p\_tournament\_age\_group\_id, 'semifinal', 2, 'Winner QF3', 'Winner QF4', 'scheduled');

        \-- Final (F)  
        INSERT INTO public.matches (tournament\_id, tournament\_age\_group\_id, round\_name, match\_number, placeholder\_team1\_text, placeholder\_team2\_text, status) VALUES  
        (p\_tournament\_id, p\_tournament\_age\_group\_id, 'final', 1, 'Winner SF1', 'Winner SF2', 'scheduled');

        \-- Consolation (3rd Place Game) \- Optional  
        IF v\_include\_consolation THEN  
            INSERT INTO public.matches (tournament\_id, tournament\_age\_group\_id, round\_name, match\_number, placeholder\_team1\_text, placeholder\_team2\_text, status) VALUES  
            (p\_tournament\_id, p\_tournament\_age\_group\_id, 'consolation', 1, 'Loser SF1', 'Loser SF2', 'scheduled');  
        END IF;

    ELSIF v\_playoff\_format \= '4\_team\_2\_groups' AND v\_advancing\_count \>= 4 THEN  
        \-- Example for 4 teams from 2 groups (assuming G1W, G1R, G2W, G2R advance as seeds 1-4)  
        \-- Semifinals (SF)  
        INSERT INTO public.matches (tournament\_id, tournament\_age\_group\_id, round\_name, match\_number, placeholder\_team1\_text, placeholder\_team2\_text, status) VALUES  
        (p\_tournament\_id, p\_tournament\_age\_group\_id, 'semifinal', 1, (v\_seed\_map \-\> 'seed\_1')::jsonb \-\>\> 'placeholder', (v\_seed\_map \-\> 'seed\_4')::jsonb \-\>\> 'placeholder', 'scheduled'), \-- e.g., Winner G1 vs Runner-up G2  
        (p\_tournament\_id, p\_tournament\_age\_group\_id, 'semifinal', 2, (v\_seed\_map \-\> 'seed\_2')::jsonb \-\>\> 'placeholder', (v\_seed\_map \-\> 'seed\_3')::jsonb \-\>\> 'placeholder', 'scheduled'); \-- e.g., Winner G2 vs Runner-up G1

        \-- Final (F)  
        INSERT INTO public.matches (tournament\_id, tournament\_age\_group\_id, round\_name, match\_number, placeholder\_team1\_text, placeholder\_team2\_text, status) VALUES  
        (p\_tournament\_id, p\_tournament\_age\_group\_id, 'final', 1, 'Winner SF1', 'Winner SF2', 'scheduled');

        \-- Consolation (3rd Place Game) \- Optional  
        IF v\_include\_consolation THEN  
            INSERT INTO public.matches (tournament\_id, tournament\_age\_group\_id, round\_name, match\_number, placeholder\_team1\_text, placeholder\_team2\_text, status) VALUES  
            (p\_tournament\_id, p\_tournament\_age\_group\_id, 'consolation', 1, 'Loser SF1', 'Loser SF2', 'scheduled');  
        END IF;

    ELSIF v\_playoff\_format \= 'champions\_only' THEN  
         \-- Logic for champions only format (e.g., straight to final if 2 groups, semis if 4 groups, etc.)  
         \-- Requires more specific logic based on the number of groups.  
         RAISE NOTICE 'Seeding for format % not fully implemented in this example.', v\_playoff\_format; \-- Placeholder  
    ELSE  
        RAISE WARNING 'Unsupported or insufficient teams for playoff\_format: % with advancing\_count: %', v\_playoff\_format, v\_advancing\_count;  
    END IF;

    \-- Attempt to populate team\_ids for the first round of playoffs immediately  
    \-- This assumes determine\_advancing\_teams provides definitive team\_ids for the initial seeds  
    UPDATE public.matches m  
    SET team1\_id \= (v\_seed\_map \-\> ('seed\_' || regexp\_replace(m.placeholder\_team1\_text, '\[^0-9\]', '', 'g')))::jsonb \-\>\> 'team\_id'  
    WHERE m.tournament\_id \= p\_tournament\_id  
      AND m.tournament\_age\_group\_id \= p\_tournament\_age\_group\_id  
      AND m.group\_id IS NULL \-- Playoff matches  
      AND m.round\_name IN ('quarterfinal', 'semifinal') \-- Adjust based on first playoff round name  
      AND m.placeholder\_team1\_text LIKE 'Seed %'; \-- Or based on seed\_description format

    UPDATE public.matches m  
    SET team2\_id \= (v\_seed\_map \-\> ('seed\_' || regexp\_replace(m.placeholder\_team2\_text, '\[^0-9\]', '', 'g')))::jsonb \-\>\> 'team\_id'  
    WHERE m.tournament\_id \= p\_tournament\_id  
      AND m.tournament\_age\_group\_id \= p\_tournament\_age\_group\_id  
      AND m.group\_id IS NULL \-- Playoff matches  
      AND m.round\_name IN ('quarterfinal', 'semifinal') \-- Adjust based on first playoff round name  
      AND m.placeholder\_team2\_text LIKE 'Seed %'; \-- Or based on seed\_description format

END;  
$$;

\-- Function to update playoff match teams based on results of previous rounds  
\-- This would be called after a playoff match completes  
CREATE OR REPLACE FUNCTION public.update\_playoff\_match\_teams(  
    p\_completed\_match\_id UUID  
)  
RETURNS VOID  
LANGUAGE plpgsql SECURITY DEFINER AS $$  
DECLARE  
    v\_completed\_match RECORD;  
    v\_next\_match RECORD;  
    v\_winner\_id UUID;  
    v\_loser\_id UUID;  
    v\_placeholder TEXT;  
BEGIN  
    \-- Get details of the completed match  
    SELECT \* INTO v\_completed\_match  
    FROM public.matches  
    WHERE id \= p\_completed\_match\_id AND status \= 'completed' AND group\_id IS NULL; \-- Ensure it's a completed playoff match

    IF NOT FOUND THEN  
        RAISE NOTICE 'Match % not found or not a completed playoff match.', p\_completed\_match\_id;  
        RETURN;  
    END IF;

    v\_winner\_id := v\_completed\_match.winner\_id;  
    v\_loser\_id := v\_completed\_match.loser\_id;

    \-- Find the next match where the winner plays  
    v\_placeholder := 'Winner ' || upper(v\_completed\_match.round\_name) || v\_completed\_match.match\_number; \-- e.g., 'Winner QF1'  
    SELECT \* INTO v\_next\_match  
    FROM public.matches  
    WHERE tournament\_id \= v\_completed\_match.tournament\_id  
      AND tournament\_age\_group\_id \= v\_completed\_match.tournament\_age\_group\_id  
      AND group\_id IS NULL  
      AND (placeholder\_team1\_text \= v\_placeholder OR placeholder\_team2\_text \= v\_placeholder)  
    LIMIT 1;

    IF FOUND THEN  
        IF v\_next\_match.placeholder\_team1\_text \= v\_placeholder THEN  
            UPDATE public.matches SET team1\_id \= v\_winner\_id, updated\_at \= timezone('utc', now()) WHERE id \= v\_next\_match.id;  
            RAISE NOTICE 'Updated team1\_id for match % based on winner of match %', v\_next\_match.id, p\_completed\_match\_id;  
        ELSIF v\_next\_match.placeholder\_team2\_text \= v\_placeholder THEN  
            UPDATE public.matches SET team2\_id \= v\_winner\_id, updated\_at \= timezone('utc', now()) WHERE id \= v\_next\_match.id;  
            RAISE NOTICE 'Updated team2\_id for match % based on winner of match %', v\_next\_match.id, p\_completed\_match\_id;  
        END IF;  
    END IF;

    \-- Find the next match where the loser plays (e.g., consolation)  
    v\_placeholder := 'Loser ' || upper(v\_completed\_match.round\_name) || v\_completed\_match.match\_number; \-- e.g., 'Loser SF1'  
    SELECT \* INTO v\_next\_match  
    FROM public.matches  
    WHERE tournament\_id \= v\_completed\_match.tournament\_id  
      AND tournament\_age\_group\_id \= v\_completed\_match.tournament\_age\_group\_id  
      AND group\_id IS NULL  
      AND (placeholder\_team1\_text \= v\_placeholder OR placeholder\_team2\_text \= v\_placeholder)  
    LIMIT 1;

    IF FOUND THEN  
        IF v\_next\_match.placeholder\_team1\_text \= v\_placeholder THEN  
            UPDATE public.matches SET team1\_id \= v\_loser\_id, updated\_at \= timezone('utc', now()) WHERE id \= v\_next\_match.id;  
            RAISE NOTICE 'Updated team1\_id for match % based on loser of match %', v\_next\_match.id, p\_completed\_match\_id;  
        ELSIF v\_next\_match.placeholder\_team2\_text \= v\_placeholder THEN  
            UPDATE public.matches SET team2\_id \= v\_loser\_id, updated\_at \= timezone('utc', now()) WHERE id \= v\_next\_match.id;  
            RAISE NOTICE 'Updated team2\_id for match % based on loser of match %', v\_next\_match.id, p\_completed\_match\_id;  
        END IF;  
    END IF;

END;  
$$;

