\# Detailed Design Document: Scheduling Algorithm

\*\*Version:\*\* 1.0  
\*\*Date:\*\* April 28, 2025  
\*\*Related Requirements:\*\* FR6 (Bracket Validation), FR7 (Scheduling), NFR1 (Performance), NFR2 (Security), NFR3 (Reliability), NFR5 (Maintainability), NFR8 (Testability)

\#\# 1\. Introduction

\#\#\# 1.1 Purpose  
This document provides a detailed technical design for the automated tournament scheduling component of the Soccer Tournament Management Platform. It elaborates on the algorithms, data structures, constraints, and interactions involved in generating match schedules for both group stage and playoff rounds.

\#\#\# 1.2 Scope  
This document covers the process initiated by a Tournament Director (or Admin) via the API, executed asynchronously by the Python backend service (using Celery), covering:  
\* Data retrieval from Supabase.  
\* Bracket validation and fairness analysis integration (via Supabase RPCs).  
\* Group stage match pairing generation (Round Robin).  
\* Time slot generation.  
\* Constraint checking during match assignment.  
\* Assignment of matches to specific times and fields.  
\* Determination of advancing teams and playoff seeding (via Supabase RPCs).  
\* Persistence of the generated schedule back to Supabase.  
\* Reporting of results, errors, warnings, and performance metrics.

It does \*not\* cover the detailed implementation of the Referee Assignment algorithm, which is handled by a separate service/module (though invoked by the overall scheduling process).

\#\#\# 1.3 Glossary  
\*(Refer to main SRS Glossary)\*

\#\# 2\. Design Overview

The scheduling process is designed as an asynchronous background task triggered via a REST API call. It aims to generate a valid and reasonably fair schedule respecting various constraints.

\* \*\*Initiation:\*\* Triggered by \`POST /schedule\` API call (FastAPI).  
\* \*\*Execution:\*\* Handled by a Celery worker executing the \`schedule\_tournament\_task\` (\`tasks.py\`).  
\* \*\*Orchestration:\*\* The core logic resides in \`run\_full\_schedule\_process\` and \`generate\_match\_schedule\` within \`scheduler\_service\_production\_v2\_final.py\`, which calls various helper modules and Supabase RPCs.  
\* \*\*Data Source/Sink:\*\* Supabase PostgreSQL database.  
\* \*\*Key Outputs:\*\* Populated \`matches\` table in Supabase, job status/results available via \`GET /schedule/status/{job\_id}\`.

\#\# 3\. Inputs

The scheduling process relies on comprehensive data fetched from the Supabase database via the \`fetch\_scheduling\_data\` function (or its production equivalent):

\* \*\*Tournament Details (\`tournaments\` table):\*\* ID, start/end dates, global custom settings (default game times, etc.).  
\* \*\*Age Group Details (\`tournament\_age\_groups\` table):\*\* Format (e.g., '7v7'), specific custom settings (game duration, halftime, transition time, playoff format, bracket validation rules, fairness rules like min rest time, club matchup avoidance preference).  
\* \*\*Group Details (\`tournament\_groups\` table):\*\* Group IDs associated with age groups.  
\* \*\*Team Registrations (\`registrations\` table):\*\* List of teams registered for each age group, including their assigned group ID and club ID.  
\* \*\*Team Details (\`teams\` table):\*\* Team names, associated club IDs.  
\* \*\*Field/Venue Details (\`fields\`, \`venues\` tables):\*\* Field IDs, names, types (e.g., '7v7', '9v9'), operational start/end times, status (Open/Closed).  
\* \*\*Field Unavailability (\`field\_unavailability\` table):\*\* Specific time blocks when fields are not available.  
\* \*\*Team Staff (\`team\_staff\` table):\*\* Mapping of coaches/managers (profile IDs) to teams.  
\* \*\*Team Requests (\`team\_requests\` table):\*\* Specific scheduling constraints requested by teams (e.g., cannot play at certain times \- \*Note: constraint checking logic for this might need review/enhancement in \`check\_constraints.py\`\*).

\#\# 4\. Outputs

The primary output is the population of the \`matches\` table in Supabase with scheduled game details (team1\_id, team2\_id, scheduled\_time, field\_id, round\_name, match\_number, status='scheduled', etc.).

The Celery task result (stored in Redis and retrievable via the API) includes a dictionary containing:

\* \`tournament\_id\`: The ID of the scheduled tournament.  
\* \`match\_scheduling\`:  
    \* \`scheduled\_count\`: Number of matches successfully scheduled.  
    \* \`unscheduled\_count\`: Number of pairings that could not be scheduled.  
    \* \`unscheduled\_details\`: List of unscheduled pairings and reasons (if available).  
    \* \`errors\`: List of critical errors encountered during match scheduling.  
    \* \`warnings\`: List of non-critical issues (e.g., constraint violations met by overrides, fairness concerns).  
    \* \`validation\_report\`: Results from \`api\_validate\_brackets\` (structure defined in \`README\_DATABASE\_CHANGES.md\`).  
    \* \`fairness\_report\`: Results from \`api\_get\_fairness\_report\` (structure defined in \`README\_DATABASE\_CHANGES.md\`).  
    \* \`performance\_metrics\`: Execution time details (total time, data fetch time, scheduling time, etc.).  
\* \`referee\_assignment\`: (Result from the referee assignment service/module)  
    \* \`assigned\_count\`: Number of referee slots filled.  
    \* \`unassigned\_count\`: Number of referee slots remaining empty.  
    \* \`errors\`: List of critical errors during referee assignment.  
    \* \`warnings\`: List of non-critical issues.  
    \* \`workload\_analysis\`: Report on referee workload distribution.  
\* \`completed\_at\`: Timestamp of task completion.

\#\# 5\. Core Logic and Processing Steps

The \`run\_full\_schedule\_process\` function orchestrates the main steps:

1\.  \*\*Fetch Data:\*\* Call \`fetch\_scheduling\_data\` (or equivalent production function) to retrieve all necessary inputs from Supabase for the given \`tournament\_id\`. Handle potential DB connection errors.  
2\.  \*\*Generate Match Schedule:\*\* Call \`generate\_match\_schedule(tournament\_id)\`:  
    \* \*\*Bracket Validation (DB RPC):\*\* Call Supabase function \`api\_validate\_brackets(tournament\_id)\`. Store results. If critical errors are found (e.g., insufficient teams), potentially halt or proceed with warnings based on configuration.  
    \* \*\*Generate Time Slots:\*\* Call \`generate\_time\_slots\` (from \`scheduler\_time\_slots.py\`) based on tournament dates, field availability, and venue operational times. Creates a dictionary mapping \`field\_id\` to a list of available \`datetime\` start times.  
    \* \*\*Generate Pairings:\*\* Call \`create\_match\_pairings\` (from \`create\_match\_pairings.py\`). This function:  
        \* Groups registered teams by \`assigned\_group\_id\`.  
        \* For each group, uses \`simple\_round\_robin\` logic (from \`simple\_round\_robin.py\`, likely implementing the "Circle Method" or equivalent) to generate all unique pairs \`(team1, team2)\`.  
        \* Applies constraints like \`avoid\_club\_matchups\` if configured.  
        \* Returns a list of match pairing dictionaries.  
    \* \*\*Assign Pairings to Slots (Core Algorithm):\*\*  
        \* Initialize empty schedules for coaches and fields (\`coach\_schedules\`, \`field\_schedules\`).  
        \* Iterate through the generated \`pairings\`.  
        \* For each \`pairing\`, iterate through available \`fields\` and their \`time\_slots\`.  
        \* For each potential \`(pairing, field, start\_time)\` combination, call \`check\_constraints\` (from \`check\_constraints.py\`).  
        \* \*\*Constraint Checking (\`check\_constraints\`):\*\*  
            \* Calculates game end time based on \`game\_duration\`, \`halftime\`, and \`transition\` time from settings.  
            \* Checks for field availability overlap using \`field\_schedules\`.  
            \* Checks field type compatibility (\`match\_pairing.format\` vs \`field.field\_type\_name\`).  
            \* Checks field status (\`field.field\_status \== 'Open'\`).  
            \* Checks for coach conflicts using \`coach\_schedules\` and \`scheduling\_data\['team\_staff'\]\`.  
            \* Checks team rest time (requires comparing \`potential\_start\_dt\` with previous game end times for both teams in \`team\_schedules\` \- \*implementation detail to verify in code\*).  
            \* (Potential) Checks team time requests.  
            \* Returns \`(isValid: bool, duration: timedelta, reason: str)\`.  
        \* \*\*Assignment:\*\* If \`check\_constraints\` returns \`True\`:  
            \* Select the first valid slot found (Greedy/First-Fit approach).  
            \* Add the match details (teams, field, start/end times) to the \`results\['scheduled'\]\` list.  
            \* Update \`field\_schedules\` and \`coach\_schedules\` with the newly assigned block.  
            \* Break the inner loops (slots/fields) and move to the next pairing.  
        \* \*\*Unscheduled:\*\* If no valid slot is found for a pairing after checking all fields/slots, add it to \`results\['unscheduled'\]\`.  
    \* \*\*Fairness Analysis (DB RPC):\*\* Call Supabase function \`api\_get\_fairness\_report(group\_id)\` for relevant groups (likely after scheduling is complete). Store results.  
    \* \*\*Compile Performance & Results:\*\* Record execution times, calculate scheduled/unscheduled counts, gather errors/warnings. Return the \`match\_result\` dictionary.  
3\.  \*\*Assign Referees:\*\* If \`generate\_match\_schedule\` completed without critical errors and scheduled at least one match:  
    \* Call \`generate\_referee\_assignments(tournament\_id)\` (from \`referee\_assignment\_service.py\`).  
    \* This service fetches the newly scheduled matches and available referees.  
    \* It runs its own assignment logic (details TBD in a separate design doc).  
    \* Saves assignments to \`referee\_assignments\` table.  
    \* Returns a \`ref\_result\` dictionary (assigned count, unassigned count, errors, warnings, workload analysis).  
4\.  \*\*Determine Advancement & Seed Playoffs (DB RPCs):\*\* After group stage matches are (theoretically) completed and scores entered (though this seems integrated into the \*initial\* scheduling task):  
    \* Call Supabase RPC \`determine\_advancing\_teams(tournament\_id, age\_group\_id)\`.  
    \* Call Supabase RPC \`seed\_playoff\_bracket(tournament\_id, age\_group\_id)\` to create placeholder playoff matches in the \`matches\` table. \*(Note: The timing of this step within the initial scheduling task vs. after actual group play needs clarification. The current code seems to run it as part of the initial task).\*  
5\.  \*\*Final Result:\*\* Combine \`match\_result\` and \`ref\_result\` into the final dictionary returned by the Celery task.

\#\# 6\. Key Data Structures

\* \*\*\`scheduling\_data\` (dict):\*\* Central dictionary holding all input data fetched from Supabase, organized by entity type (tournaments, age\_groups, teams, fields, etc.).  
\* \*\*\`pairings\` (list\[dict\]):\*\* List of dictionaries representing potential matches, e.g., \`{'team1\_id': 'A', 'team2\_id': 'B', 'age\_group\_id': 'AG1', 'group\_id': 'G1', ...}\`.  
\* \*\*\`time\_slots\` (dict\[str, list\[datetime\]\]):\*\* Dictionary mapping \`field\_id\` to a sorted list of available \`datetime\` start times.  
\* \*\*\`field\_schedules\` (defaultdict\[str, list\[tuple(datetime, datetime)\]\]):\*\* Tracks occupied time intervals \`(start, end)\` for each \`field\_id\`.  
\* \*\*\`coach\_schedules\` (defaultdict\[str, list\[tuple(datetime, datetime, str)\]\]):\*\* Tracks occupied time intervals \`(start, end, venue\_id)\` for each \`coach\_id\`.  
\* \*\*\`team\_schedules\` (defaultdict\[str, list\[tuple(datetime, datetime)\]\]):\*\* (Implicitly needed for rest time constraint) Tracks occupied time intervals \`(start, end)\` for each \`team\_id\`.  
\* \*\*API Models (Pydantic):\*\* \`ScheduleRequest\`, \`JobStatus\`, \`ScheduleResult\`.

\#\# 7\. Algorithms Used

\* \*\*Round-Robin Generation:\*\* "Circle Method" or equivalent algorithm used by \`simple\_round\_robin.py\` and adapted in \`create\_match\_pairings.py\`.  
\* \*\*Constraint Satisfaction:\*\* A greedy, first-fit approach appears to be used for assigning pairings to slots. It iterates through pairings and assigns each to the first valid time slot found that satisfies all constraints checked by \`check\_constraints.py\`. No complex backtracking or optimization (like minimizing travel or maximizing rest beyond the minimum) seems explicitly implemented in the provided core assignment loop, although fairness checks happen post-assignment via RPC.  
\* \*\*Database Functions/Procedures:\*\* Significant logic for validation, fairness analysis, playoff advancement, and seeding is encapsulated within Supabase SQL functions/procedures (RPCs).

\#\# 8\. Constraint Handling

Constraints are primarily checked within the \`check\_constraints.py\` module during the assignment phase:

\* Field Time Conflicts (Overlap checking).  
\* Field Type Compatibility.  
\* Field Status (Must be "Open").  
\* Coach Conflicts (Checks if any coach involved is already scheduled elsewhere at the same time).  
\* Team Rest Time (Requires comparing potential start time against the end times of previous games for both teams involved).  
\* (Potentially) Team Time Requests.

Violations prevent a match from being scheduled in that specific slot.

\#\# 9\. Error Handling

\* \*\*DB Connection/Query Errors:\*\* Handled during data fetching; task should fail or return error state.  
\* \*\*Validation Errors:\*\* Bracket validation errors (from \`api\_validate\_brackets\`) are captured and included in the result report. Scheduling may proceed with warnings depending on severity/configuration.  
\* \*\*Scheduling Failures:\*\* If a pairing cannot be assigned to any slot, it's added to the \`unscheduled\_details\` list in the result.  
\* \*\*Unexpected Exceptions:\*\* Caught within the Celery task (\`tasks.py\`) and service functions (\`scheduler\_service...py\`). Logged extensively. Celery task configured to retry on failure (up to 3 times with 60s delay). API returns a generic error for job status checks if the task failed irrecoverably.

\#\# 10\. Performance Considerations

\* \*\*Asynchronicity:\*\* Offloading the entire process to Celery prevents blocking the API.  
\* \*\*Database Interaction:\*\* Batching reads/writes where possible. Using efficient SQL queries and RPCs. RLS policy performance should be monitored.  
\* \*\*Algorithm Complexity:\*\* The core assignment loop iterates through pairings \* fields \* slots. The complexity depends on the number of teams, fields, and the density of the schedule. Constraint checking adds overhead per potential assignment. For very large tournaments, this could become slow.  
\* \*\*Metrics:\*\* The service logs performance metrics (total time, sub-component times) to help identify bottlenecks.

\#\# 11\. Future Improvements / Considerations

\* \*\*Optimization Algorithm:\*\* Replace the greedy/first-fit assignment with a more sophisticated optimization algorithm (e.g., simulated annealing, constraint programming) if fairness metrics (like balanced rest time, minimizing coach travel) need further improvement beyond simple constraint satisfaction.  
\* \*\*Parallelism:\*\* Explore parallelizing the scheduling of independent groups or age divisions within the Celery task.  
\* \*\*Caching:\*\* Cache frequently accessed, rarely changing data (like field details) if DB reads become a bottleneck.  
\* \*\*Team Request Handling:\*\* Ensure team time requests are fully implemented and tested within \`check\_constraints.py\`.  
