\# Detailed Design Document: Authentication and Authorization

\*\*Version:\*\* 1.0  
\*\*Date:\*\* April 28, 2025  
\*\*Related Requirements:\*\* FR1 (Authentication), FR2 (Profile Management), FR3 (Club Management), FR8 (Admin Functions), NFR2 (Security), NFR3 (Reliability), NFR5 (Maintainability), NFR8 (Testability)

\#\# 1\. Introduction

\#\#\# 1.1 Purpose  
This document provides a detailed technical design for the authentication and authorization mechanisms used within the Soccer Tournament Management Platform. It covers user registration, login, session management, password recovery, role-based access control (RBAC), and the interaction between the Flutter frontend, Supabase Auth, Supabase Database (RLS), and the Python backend API.

\#\#\# 1.2 Scope  
This design encompasses:  
\* User identity management (signup, login, logout, password reset).  
\* Authentication methods (email/password).  
\* Session management using JSON Web Tokens (JWTs).  
\* Authorization mechanisms:  
    \* Supabase Row Level Security (RLS) for direct database access control.  
    \* JWT verification and role checking within the Python FastAPI service.  
\* Handling of authentication callbacks (e.g., email confirmation, password reset).  
\* Integration between the Flutter frontend (\`AuthBloc\`, \`AuthNotifier\`), Supabase Auth, and the Python backend (\`python\_backend/app/main.py\`, FastAPI security dependencies), particularly for operations like Club Admin registration which now use a dedicated backend API endpoint.

\#\#\# 1.3 Glossary  
\*(Refer to main SRS Glossary)\*  
\* \*\*JWT:\*\* JSON Web Token \- A standard for securely transmitting information between parties as a JSON object. Used here for session management and API authorization.  
\* \*\*RLS:\*\* Row Level Security \- PostgreSQL/Supabase feature to control which rows users can access or modify in a table.  
\* \*\*PKCE:\*\* Proof Key for Code Exchange \- An OAuth 2.0 security extension used by Supabase Auth for secure authorization code flow, especially relevant for mobile/SPA applications.  
\* \*\*Service Role Key:\*\* A Supabase API key with permissions to bypass RLS, used by backend services.  
\* \*\*Anon Key:\*\* A Supabase API key for public, unauthenticated access (limited by RLS).

\#\# 2\. Design Overview

The authentication system relies heavily on Supabase Auth for core identity management, complemented by custom logic in both the Flutter frontend and the Python backend for authorization and user experience.

\* \*\*Identity Provider:\*\* Supabase Auth (GoTrue).  
\* \*\*Authentication Method:\*\* Email and Password.  
\* \*\*Session Management:\*\* JWTs issued by Supabase Auth, managed by \`supabase-flutter\` on the client and verified by the Python API.  
\* \*\*Authorization:\*\*  
    \* \*\*Database:\*\* Supabase Row Level Security (RLS) policies based on \`auth.uid()\` and custom claims/profile roles.  
    \* \*\*API (Python):\*\* FastAPI dependency (\`security.py\`) verifies incoming JWTs using the \`SUPABASE\_JWT\_SECRET\` and checks for required roles.  
\* \*\*Frontend Integration:\*\* Flutter uses \`supabase-flutter\` SDK for Auth operations, \`AuthBloc\` for managing auth logic/state, and \`AuthNotifier\` to integrate with \`GoRouter\` for redirects based on auth state.

\#\# 3\. Component Responsibilities

\* \*\*Flutter Frontend (\`flutter\_frontend/lib/features/auth/\`):\*\*  
    \* Presents UI for login, registration (including club admin registration), password reset.
    \* Captures user credentials.  
    \* Uses \`supabase-flutter\` SDK for standard Supabase Auth operations (e.g., regular sign-up, login).
    \* For Club Admin Registration, it calls a dedicated endpoint (\`/api/v1/register-club-admin\`) on the Python FastAPI backend.
    \* Manages local auth state (\`AuthBloc\`, \`AuthNotifier\`).  
    \* Stores/retrieves JWTs securely (using \`supabase-flutter\`'s persistence).  
    \* Handles navigation based on auth state (\`GoRouter\` redirects).  
    \* Includes JWT in \`Authorization\` header for Python API calls (if applicable to the endpoint being called).
    \* Handles auth callbacks (\`/auth/callback\`).  
\* \*\*Supabase Auth (GoTrue):\*\*  
    \* Manages user accounts (email/password).  
    \* Handles password hashing and security.  
    \* Issues and verifies JWTs (access and refresh tokens).  
    \* Manages email confirmation and password reset flows (sending emails, verifying tokens).
    \* Provides Admin API methods (e.g., `create_user`, `invite_user_by_email`) used by the Python backend for privileged user creation and management.
\* \*\*Supabase Database (PostgreSQL):\*\*  
    \* Stores user profile data (\`profiles\` table), linked to \`auth.users\` via UUID.  
    \* Stores club data (`clubs` table).
    \* Stores role information (\`profiles.user\_role\`).  
    \* Stores affiliation data, including the new \`club\_directors\` table which links users (admins/directors) to clubs, and \`team\_staff\`.
    \* Enforces data access rules via RLS policies based on \`auth.uid()\` and role checks.
    \* Contains DB functions/triggers related to auth (e.g., \`handle\_new\_user` which creates a profile upon new `auth.users` entry).
\* \*\*Supabase Edge Functions:\*\*  
    \* Handles specific backend tasks like Stripe webhook verification (\`supabase/functions/stripe-webhook/index.ts\`). Uses the Service Role Key for privileged database access.  
\* \*\*Python Backend API (FastAPI - located in `python_backend/app/main.py`):\*\*  
    \* Provides an endpoint (\`/api/v1/register-club-admin\`) for club admin registration. This endpoint uses the Supabase Python client (configured with the `service_role` key) to perform the multi-step process: create club, create auth user, update profile, create club director entry, and trigger confirmation email.
    \* Verifies JWTs presented by the Flutter frontend for other protected endpoints (if any, using logic in `python\_backend/app/core/security.py` or similar).
    \* Extracts user ID and role from verified JWTs for authorized actions.
    \* Enforces endpoint-level authorization based on required roles for other endpoints.
\* \*\*Python Backend Workers (Celery):\*\*
    \* Receive tasks triggered by authenticated API calls.  
    \* May perform actions based on the user ID passed in the task payload, but generally rely on Supabase RLS for data access control when interacting with the database using the Service Role Key (or impersonation if implemented).

\#\# 4\. Detailed Flows

\#\#\# 4.1 User Registration

1\.  \*\*UI:\*\* User enters email, password, name, etc., in Flutter \`AuthScreen\` (or dedicated \`SignupScreen\`).  
2\.  \*\*Frontend Logic:\*\* (\`AuthBloc\`/\`AuthService\`) calls \`Supabase.instance.client.auth.signUp\` with email, password, and user metadata (first name, last name, user\_role).  
3\.  \*\*Supabase Auth:\*\*  
    \* Validates input.  
    \* Creates a new entry in \`auth.users\`.  
    \* Sends a confirmation email to the user's address containing a verification link.  
    \* Returns user and session information (or error) to Flutter.  
4\.  \*\*Database Trigger:\*\* \`on\_auth\_user\_created\` trigger executes \`handle\_new\_user()\` function.  
5\.  \*\*\`handle\_new\_user()\` Function:\*\* Inserts a new row into the \`public.profiles\` table, copying the user ID, email, and extracting metadata (like \`user\_role\`, defaulting to \`player\_parent\` if not provided).  
6\.  \*\*Frontend State:\*\* \`AuthBloc\` updates state (e.g., \`AuthNeedsConfirmation\`), potentially navigating the user to a "Check Your Email" screen (\`CheckEmailScreen\`).

\#\#\# 4.2 Email Confirmation

1\.  \*\*User Action:\*\* User clicks the confirmation link in their email.  
2\.  \*\*Supabase:\*\* Verifies the confirmation token and updates the \`email\_confirmed\_at\` timestamp in \`auth.users\`. Redirects the user to the configured callback URL (\`AuthConstants.emailConfirmationCallbackUrl\`, e.g., \`http://localhost:8114/auth/callback\`).  
3\.  \*\*Frontend Callback:\*\* Flutter app receives the callback at \`/auth/callback\` (\`RecommendedCallbackProcessingScreen\`).  
4\.  \*\*Supabase SDK:\*\* The \`supabase-flutter\` listener for \`onAuthStateChange\` detects the \`AuthChangeEvent.userUpdated\` or \`AuthChangeEvent.signedIn\` event.  
5\.  \*\*Frontend State:\*\* \`AuthNotifier\` updates its state (e.g., \`isAuthenticated \= true\`).  
6\.  \*\*Routing:\*\* \`GoRouter\` detects the state change via \`refreshListenable\` and executes its \`redirect\` logic, navigating the user to the appropriate authenticated screen (e.g., \`/home\` or role-specific dashboard).

\#\#\# 4.3 User Login

1\.  \*\*UI:\*\* User enters email and password in Flutter \`AuthScreen\`.  
2\.  \*\*Frontend Logic:\*\* (\`AuthBloc\`/\`AuthService\`) calls \`Supabase.instance.client.auth.signInWithPassword\`.  
3\.  \*\*Supabase Auth:\*\* Verifies credentials against \`auth.users\`. If successful, generates new JWT access and refresh tokens. Returns user and session data to Flutter.  
4\.  \*\*Frontend State:\*\* \`AuthNotifier\` updates state (\`isAuthenticated \= true\`, stores user/session).  
5\.  \*\*Routing:\*\* \`GoRouter\` redirects to the appropriate authenticated screen.

\#\#\# 4.4 Password Reset

1\.  \*\*UI:\*\* User enters email in a "Forgot Password" section (likely within \`AuthScreen\` or a dedicated screen).  
2\.  \*\*Frontend Logic:\*\* (\`AuthBloc\`/\`AuthService\`) calls \`Supabase.instance.client.auth.resetPasswordForEmail\`, passing the email and the \`redirectTo\` URL (\`AuthConstants.passwordResetCallbackUrl\`).  
3\.  \*\*Supabase Auth:\*\* Sends a password reset email with a unique link containing a recovery token.  
4\.  \*\*Frontend State:\*\* Navigates user to "Check Your Email" screen (\`CheckEmailScreen\`).  
5\.  \*\*User Action:\*\* User clicks the link in the email.  
6\.  \*\*Supabase:\*\* Verifies the token and redirects the user to the \`redirectTo\` URL (\`/auth/callback\`) with \`\#access\_token=...\&refresh\_token=...\&type=recovery\`.  
7\.  \*\*Frontend Callback:\*\* Flutter app receives the callback at \`/auth/callback\` (\`RecommendedCallbackProcessingScreen\`).  
8\.  \*\*Supabase SDK:\*\* \`onAuthStateChange\` listener detects \`AuthChangeEvent.passwordRecovery\`.  
9\.  \*\*Frontend State:\*\* \`AuthNotifier\` updates state (e.g., \`isRecoveringPassword \= true\`).  
10\. \*\*Routing:\*\* \`GoRouter\` redirect logic detects \`isRecoveringPassword\` and navigates to the password reset screen (\`RecommendedResetPasswordScreen\`).  
11\. \*\*UI (Reset Screen):\*\* User enters and confirms a new password.  
12\. \*\*Frontend Logic:\*\* Calls \`Supabase.instance.client.auth.updateUser\` with the new password.  
13\. \*\*Supabase Auth:\*\* Updates the user's password. Returns updated user info.  
14\. \*\*Frontend State:\*\* \`AuthNotifier\` resets \`isRecoveringPassword\`, potentially sets \`isAuthenticated \= true\`. Navigates to success screen (\`ResetSuccessScreen\`) or directly to the home screen.

\#\#\# 4.5 API Request Authorization (Flutter \-\> FastAPI)

1\.  \*\*Flutter Frontend:\*\* Prepares an API request (e.g., to \`POST /schedule\`). Retrieves the current valid Supabase access token from the \`supabase-flutter\` client (\`Supabase.instance.client.auth.currentSession?.accessToken\`).  
2\.  \*\*Flutter Frontend:\*\* Adds an \`Authorization\` header to the HTTP request: \`Authorization: Bearer \<supabase\_jwt\_token\>\`.  
3\.  \*\*FastAPI Backend:\*\* Receives the request. The relevant endpoint uses a dependency (\`Depends(get\_current\_active\_user\_with\_role)\` from \`security.py\`).  
4\.  \*\*FastAPI Security Dependency (\`security.py\`):\*\*  
    \* Extracts the token from the \`Authorization\` header.  
    \* Uses \`jwt.decode\` with the \`SUPABASE\_JWT\_SECRET\` and appropriate algorithms (e.g., \`HS256\`) to verify the token's signature and expiration.  
    \* Extracts claims, including \`sub\` (user ID) and potentially \`role\` or \`app\_metadata.roles\`.  
    \* (Optional but recommended) May perform an additional check against Supabase using \`supabase.auth.get\_user(token)\` to ensure the user hasn't been disabled since the token was issued.  
    \* Checks if the extracted role matches the required role(s) for the endpoint.  
    \* If valid and authorized, returns the user information (e.g., user ID, role). If invalid or unauthorized, raises an \`HTTPException\` (e.g., 401 Unauthorized or 403 Forbidden).  
5\.  \*\*FastAPI Endpoint:\*\* If the dependency succeeds, the endpoint logic proceeds, having access to the authenticated user's ID and role.

\#\#\# 4.6 Database Access Control (RLS)

1\.  \*\*Flutter Frontend / Python Backend:\*\* Initiates a database query/operation using the Supabase client SDK.  
2\.  \*\*Supabase SDK:\*\* Sends the request to the Supabase PostgREST API, including the user's JWT (for Flutter client requests) or the Service Role Key (for Python backend service requests).  
3\.  \*\*Supabase PostgREST:\*\*  
    \* If a user JWT is present, it sets the \`auth.uid()\` and potentially \`request.jwt.claims\` variables within the database session.  
    \* If the Service Role Key is used, RLS is typically bypassed unless specific policies are written to handle it or user impersonation is used.  
4\.  \*\*PostgreSQL:\*\* Before executing the query (SELECT, INSERT, UPDATE, DELETE), it evaluates the relevant RLS policies defined on the target table(s).  
5\.  \*\*RLS Policy Evaluation:\*\* Policies use conditions like \`auth.uid() \= user\_id\`, \`public.is\_claims\_admin()\`, \`public.is\_tournament\_director(tournament\_id)\`, etc., to determine if the current session user is allowed to perform the requested operation on the specific rows.  
6\.  \*\*Execution:\*\* If the policy allows, the query proceeds; otherwise, it returns an empty result set (for SELECT) or throws a permission error (for INSERT/UPDATE/DELETE).

\#\# 5\. Key Security Considerations

\* \*\*JWT Secret:\*\* The \`SUPABASE\_JWT\_SECRET\` must be kept confidential and securely configured in the Python backend environment (e.g., via GCP Secret Manager).  
\* \*\*Service Role Key:\*\* The \`SUPABASE\_SERVICE\_ROLE\_KEY\` grants full database access, bypassing RLS. It must be strictly protected and used only by trusted backend services (Python API/Workers, Edge Functions).  
\* \*\*RLS Policies:\*\* Must be comprehensive and rigorously tested (\`test\_resource\_authorization.py\`) to prevent unauthorized data access or modification. Default DENY policies are recommended.  
\* \*\*Callback URLs:\*\* Ensure \`AuthConstants.primaryCallbackUrl\` and related URLs are correctly configured in the Supabase Auth settings (URL Configuration \-\> Site URL, Redirect URLs). Use HTTPS in production.  
\* \*\*Input Validation:\*\* Sanitize and validate all user input on both frontend and backend to prevent XSS, SQL injection (less likely with Supabase SDKs but still important for function parameters), and other attacks.  
\* \*\*CSRF Protection:\*\* While JWTs help, ensure standard CSRF protection mechanisms are considered for the Flutter web app if using cookie-based sessions alongside JWTs (though Supabase typically relies on header-based JWTs).  
\* \*\*Rate Limiting:\*\* Implement rate limiting on sensitive endpoints (login, registration, password reset) to mitigate brute-force attacks (partially handled by Supabase Auth and Flutter \`RateLimiter\`).

\#\# 6\. Error Handling

\* \*\*Flutter:\*\* Use \`AuthBloc\` states (\`AuthFailure\`, \`AuthNeedsConfirmation\`, etc.) and \`ErrorHandler\` to display appropriate messages to the user for login failures, network issues, etc.  
\* \*\*FastAPI:\*\* Use FastAPI's \`HTTPException\` to return standard HTTP error codes (401, 403, 400, 500\) with informative (but not overly revealing) detail messages.  
\* \*\*Supabase:\*\* SDKs typically throw specific exceptions (e.g., \`AuthException\`, \`PostgrestException\`) which should be caught and handled appropriately in both Flutter and Python code.  
\* \*\*Callback Errors:\*\* Handle invalid/expired tokens during callback processing gracefully (\`InvalidLinkScreen\`).

\#\# 7\. Future Improvements

\* \*\*Social Logins:\*\* Integrate Supabase Auth providers (Google, GitHub, etc.). Requires frontend UI changes and configuring providers in Supabase.  
\* \*\*Multi-Factor Authentication (MFA):\*\* Implement MFA using Supabase Auth's built-in capabilities or third-party providers.  
\* \*\*User Impersonation:\*\* Allow System Admins to impersonate other users for troubleshooting (requires careful implementation with Supabase custom claims or RLS bypass mechanisms and robust audit logging).  
\* \*\*Session Revocation:\*\* Implement mechanisms for immediate session revocation (e.g., on password change or security event), potentially using Supabase Realtime or database triggers.
