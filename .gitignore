# General
.DS_Store
*.log
*.swp
.history

# IDE - IntelliJ
*.iml
*.ipr
*.iws
.idea/

# IDE - VSCode (User can uncomment if they prefer to ignore .vscode)
#.vscode/

# Flutter / Dart (specific to flutter_frontend/)
flutter_frontend/.dart_tool/
flutter_frontend/.flutter-plugins
flutter_frontend/.flutter-plugins-dependencies
flutter_frontend/.pub-cache/
flutter_frontend/.pub/
flutter_frontend/build/
flutter_frontend/doc/api/ # If API docs are generated here

# Flutter iOS (specific to flutter_frontend/)
flutter_frontend/ios/Flutter/.last_build_id
flutter_frontend/ios/Pods/
flutter_frontend/ios/DerivedData/
flutter_frontend/ios/.symlinks/
flutter_frontend/ios/Flutter/ephemeral/
flutter_frontend/ios/Flutter/app.flx
# flutter_frontend/ios/Flutter/flutter_assets/ # Usually committed
flutter_frontend/ios/Runner/GeneratedPluginRegistrant.h
flutter_frontend/ios/Runner/GeneratedPluginRegistrant.m
flutter_frontend/ios/Flutter/Generated.xcconfig
flutter_frontend/ios/Runner.xcworkspace/xcshareddata/
flutter_frontend/ios/Runner.xcodeproj/project.xcworkspace/xcshareddata/
flutter_frontend/ios/Runner.xcodeproj/xcuserdata/

# Flutter Android (specific to flutter_frontend/)
flutter_frontend/android/app/debug/
flutter_frontend/android/app/profile/
flutter_frontend/android/app/release/
flutter_frontend/android/.gradle/
flutter_frontend/android/app/build/
flutter_frontend/android/build/ # Top-level Android build outputs
# flutter_frontend/android/gradle-wrapper.jar # Often committed, but can be ignored

# Flutter Web (specific to flutter_frontend/, if used)
# flutter_frontend/web/build/
# flutter_frontend/web/.dart_tool/

# Flutter Desktop (specific to flutter_frontend/, if used)
# flutter_frontend/linux/flutter/ephemeral/
# flutter_frontend/macos/Flutter/ephemeral/
# flutter_frontend/windows/flutter/ephemeral/

# Flutter Symbolication & Obfuscation (specific to flutter_frontend/)
flutter_frontend/app.*.symbols
flutter_frontend/app.*.map.json

# Python (specific to python_backend/)
python_backend/__pycache__/
python_backend/*.py[cod]
python_backend/.env # Ignore local .env files, commit .env.example
python_backend/env/
python_backend/venv/
python_backend/.venv/
python_backend/ENV/
python_backend/build/
python_backend/dist/
python_backend/*.egg-info/
# Add other Python-specific ignores if needed (e.g., specific IDE files, test reports)

# Supabase (specific to supabase/functions/)
supabase/functions/**/.env # Ignore local .env files for edge functions
supabase/functions/**/node_modules/
supabase/functions/**/dist/ # If TypeScript functions are compiled to dist
