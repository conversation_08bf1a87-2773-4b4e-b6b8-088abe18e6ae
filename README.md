# Soccer Tournament Management Platform

![Platform Logo](https://via.placeholder.com/100x100.png?text=AppLogo)  
**Version:** 1.0 (Post-Rebaselining)

---

## Overview

This platform provides a comprehensive solution for managing soccer tournaments, primarily targeting youth leagues but adaptable for various levels. It includes features for:

- Tournament setup
- Club and team management
- Player rostering
- Online registration with payment processing
- Automated game scheduling (group stage & playoffs) with constraints
- Automated referee assignment
- Real-time score tracking and standings generation
- Bracket visualization
- Role-based access control (Admins, TDs, Club Admins, Coaches, Referees, Parents/Players)

The system uses a hybrid architecture combining a Backend-as-a-Service (BaaS) and dedicated microservices.

---

## Table of Contents

- [Key Features](#key-features)  
- [Architecture Overview](#architecture-overview)  
- [Technology Stack](#technology-stack)  
- [Directory Structure](#directory-structure)  
- [Getting Started (Setup)](#getting-started-setup)  
  - [Prerequisites](#prerequisites)  
  - [Cloning](#cloning)  
  - [Supabase Setup (Local & Cloud)](#supabase-setup-local--cloud)  
  - [Configuration (.env)](#configuration-env)  
  - [Flutter Frontend Setup](#flutter-frontend-setup)  
  - [Python Backend Setup](#python-backend-setup)  
- [Running Locally](#running-locally)  
- [Running Tests](#running-tests)  
- [Deployment](#deployment)  
- [Documentation](#documentation)  
- [Contributing](#contributing)  
- [License](#license)

---

## Key Features

- **Role-Based Access Control** for Admins, TDs, Club Admins, Coaches, Referees, Parents/Players  
- **Tournament Management** (formats, rules, settings)  
- **Club & Team Management** (profiles, rosters)  
- **Player Rostering** with age validation  
- **Online Registration** with Stripe integration  
- **Automated Scheduling**:
  - Round-robin (Circle Method)
  - Playoff bracket generation
  - Constraint checks (fields, rest, conflicts)
  - Bye handling and fairness analysis
- **Referee Assignment** using availability and workload logic  
- **Real-Time Updates** via Supabase Realtime  
- **Bracket Visualization**  
- **Venue & Field Management**  
- **User Profile Management**  
- **Audit Logging**  
- **Admin Panels**

---

## Architecture Overview

1. **Flutter Frontend (`flutter_frontend/`)**  
   - Uses Bloc for state
   - Talks to Supabase for data/auth
   - Calls Python FastAPI for heavy tasks

2. **Supabase (`supabase/`)**  
   - PostgreSQL with RLS
   - GoTrue for Auth
   - Realtime with WebSockets
   - Edge Functions (e.g., Stripe webhook)

3. **Python Backend (`python_backend/`)**  
   - FastAPI + Celery Workers
   - Hosted on GCP Cloud Run
   - Uses Redis for job queueing

4. **Stripe**  
   - Secure payment processing

*(For diagram and details, see `docs/Architecture_Design_Document.md`)*

---

## Technology Stack

- **Frontend:** Flutter, Dart, GoRouter, Bloc, supabase_flutter  
- **Backend (Core):** Supabase (PostgreSQL, GoTrue, Realtime, Edge Functions)  
- **Backend (Service):** Python 3.9+, FastAPI, Celery, Pydantic, Redis, PyJWT  
- **Payments:** Stripe API + Webhooks  
- **Hosting:** Docker, GCP Cloud Run, Netlify/Firebase  
- **CI/CD:** GitHub Actions  

---

## Directory Structure

```
/soccer_tournament_platform/
│
├── .github/              # CI/CD workflows
├── flutter_frontend/     # Flutter Web/Mobile App
├── python_backend/       # Python Backend Service
├── supabase/             # Supabase config & migrations
├── deployment/           # GCP scripts, deploy checklists
├── docs/                 # SRS, ADD, API Docs, etc.
├── legacy_frontend/      # (Optional) Legacy React
├── .env.example
├── .gitignore
└── README.md
```

---
