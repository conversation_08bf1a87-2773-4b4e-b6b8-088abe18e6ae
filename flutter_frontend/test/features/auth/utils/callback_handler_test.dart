import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:soccer_frontend/features/auth/utils/callback_handler.dart';

// Mock Supabase classes
class MockSupabaseClient extends Mock implements SupabaseClient {}
class MockGoTrueClient extends Mock implements GoTrueClient {}
class MockSession extends Mock implements Session {}
class MockUser extends Mock implements User {}

void main() {
  late CallbackHandler callbackHandler;
  late MockSupabaseClient mockSupabaseClient;
  late MockGoTrueClient mockGoTrueClient;
  late MockSession mockSession;
  late MockUser mockUser;

  setUp(() {
    // Initialize mocks
    mockSupabaseClient = MockSupabaseClient();
    mockGoTrueClient = MockGoTrueClient();
    mockSession = MockSession();
    mockUser = MockUser();

    // Setup mock behavior for the injected SupabaseClient
    when(() => mockSupabaseClient.auth).thenReturn(mockGoTrueClient);
    // Note: currentSession, user, and emailConfirmedAt will be set per test case for processMobileCallback

    // Instantiate CallbackHandler with the mock client for all tests in this file
    callbackHandler = CallbackHandler(supabaseClient: mockSupabaseClient);
  });

  group('processCallbackUri', () {
    test('should return error result when error_description is present', () {
      // Arrange
      final uri = Uri.parse('http://localhost:8114/auth/callback?error_description=Test+error');
      
      // Act
      final result = callbackHandler.processCallbackUri(uri);
      
      // Assert
      expect(result.action, equals(CallbackAction.showError));
      expect(result.errorMessage, equals('Test error'));
    });

    test('should return resetPassword result when type is recovery', () {
      // Arrange
      final uri = Uri.parse('http://localhost:8114/auth/callback?type=recovery');
      
      // Act
      final result = callbackHandler.processCallbackUri(uri);
      
      // Assert
      expect(result.action, equals(CallbackAction.navigateToResetPassword));
      expect(result.callbackType, equals('recovery'));
    });

    test('should return confirmationSuccess result when type is signup', () {
      // Arrange
      final uri = Uri.parse('http://localhost:8114/auth/callback?type=signup');
      
      // Act
      final result = callbackHandler.processCallbackUri(uri);
      
      // Assert
      expect(result.action, equals(CallbackAction.navigateToConfirmationSuccess));
      expect(result.callbackType, equals('signup'));
    });

    test('should return home result when type is not recognized', () {
      // Arrange
      final uri = Uri.parse('http://localhost:8114/auth/callback?type=unknown');
      
      // Act
      final result = callbackHandler.processCallbackUri(uri);
      
      // Assert
      expect(result.action, equals(CallbackAction.navigateToHome));
    });

    test('should return home result when no type is provided', () {
      // Arrange
      final uri = Uri.parse('http://localhost:8114/auth/callback');
      
      // Act
      final result = callbackHandler.processCallbackUri(uri);
      
      // Assert
      expect(result.action, equals(CallbackAction.navigateToHome));
    });
  });

  // Note: processMobileCallback is harder to test because it relies on Supabase.instance
  // In a real test environment, you would need to set up a way to inject the Supabase client
  // or refactor the code to accept a client parameter

  group('processMobileCallback', () {
    test('should return navigateToLogin when no session exists', () async {
      // Arrange
      when(() => mockGoTrueClient.currentSession).thenReturn(null);

      // Act
      final result = await callbackHandler.processMobileCallback();

      // Assert
      expect(result.action, equals(CallbackAction.navigateToLogin));
      // Verify that the mock was called
      // The refactored processMobileCallback now only calls currentSession once after the initial delay.
      verify(() => mockGoTrueClient.currentSession).called(1);
    });

    test('should return navigateToConfirmationSuccess when session exists and email is confirmed', () async {
      // Arrange
      when(() => mockGoTrueClient.currentSession).thenReturn(mockSession);
      when(() => mockSession.user).thenReturn(mockUser);
      when(() => mockUser.emailConfirmedAt).thenReturn('2023-01-01T12:00:00Z'); // Non-null string
      when(() => mockUser.email).thenReturn('<EMAIL>');


      // Act
      final result = await callbackHandler.processMobileCallback();

      // Assert
      expect(result.action, equals(CallbackAction.navigateToConfirmationSuccess));
      verify(() => mockGoTrueClient.currentSession).called(1); // Called once after delay
      verify(() => mockUser.emailConfirmedAt).called(1);
    });

    test('should return navigateToHome when session exists but email is not confirmed', () async {
      // Arrange
      when(() => mockGoTrueClient.currentSession).thenReturn(mockSession);
      when(() => mockSession.user).thenReturn(mockUser);
      when(() => mockUser.emailConfirmedAt).thenReturn(null); // Email not confirmed
      when(() => mockUser.email).thenReturn('<EMAIL>');

      // Act
      final result = await callbackHandler.processMobileCallback();

      // Assert
      expect(result.action, equals(CallbackAction.navigateToHome));
      verify(() => mockGoTrueClient.currentSession).called(1);
      verify(() => mockUser.emailConfirmedAt).called(1);
    });
  });
}
