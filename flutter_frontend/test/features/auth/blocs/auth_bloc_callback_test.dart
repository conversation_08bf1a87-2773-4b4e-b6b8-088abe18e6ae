// test/features/auth/blocs/auth_bloc_callback_test.dart
import 'package:bloc_test/bloc_test.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:supabase_flutter/supabase_flutter.dart' as supabase;
import 'package:soccer_frontend/features/auth/blocs/auth_bloc.dart';
import 'package:soccer_frontend/core/interfaces/auth_service_interface.dart';
import 'package:soccer_frontend/data/models/user_profile.dart';
import 'package:soccer_frontend/core/utils/app_exception.dart';

// --- Mock Classes ---
class MockAuthService extends Mock implements AuthServiceInterface {}

class MockUser extends Mock implements supabase.User {
  // Allow overriding specific fields for tests
  final String _id;
  final String? _email;
  final String? _emailConfirmedAt;
  MockUser({String id = 'mock-id', String? email, String? emailConfirmedAt})
      : _id = id, _email = email, _emailConfirmedAt = emailConfirmedAt;

  @override String get id => _id;
  @override String? get email => _email;
  @override String? get emailConfirmedAt => _emailConfirmedAt;
  // Mock other fields if needed by your code (e.g., appMetadata)
  @override Map<String, dynamic> get appMetadata => {};
  @override Map<String, dynamic> get userMetadata => {};
}

class MockSession extends Mock implements supabase.Session {
  final supabase.User _user;

  MockSession({supabase.User? user}) : _user = user ?? MockUser();

  @override
  supabase.User get user => _user;
}
class MockSupabaseAuthState extends Mock implements supabase.AuthState {}

// Helper to register fallback values for mocktail
void registerFallbackValues() {
  registerFallbackValue(Uri());
  registerFallbackValue(supabase.AuthChangeEvent.signedIn);
  registerFallbackValue(<String, dynamic>{});
  registerFallbackValue(MockSupabaseAuthState()); // Register the mock state itself
  registerFallbackValue(MockUser());            // Register mock user
}

// We can't directly use the private _AuthChangeEventOccurred event in tests
// Instead, we'll test the public methods that use it
// For this test, we'll use the AuthSupaUISuccess event which has similar behavior

void main() {
  late AuthBloc authBloc;
  late MockAuthService mockAuthService;
  // Define common mocks used across tests
  late MockUser mockUserConfirmed;
  late MockUser mockUserRecovery;

  setUpAll(() {
    registerFallbackValues();
  });

  setUp(() {
    mockAuthService = MockAuthService();

    // --- Setup Mock Users ---
    mockUserConfirmed = MockUser(id: 'confirmed-user-id', email: '<EMAIL>', emailConfirmedAt: DateTime.now().toIso8601String());
    mockUserRecovery = MockUser(id: 'recovery-user-id', email: '<EMAIL>');

    // --- Default Service Stubs ---
    // Default profile fetch (can be overridden in specific tests)
    when(() => mockAuthService.getUserProfile(any())).thenAnswer((invocation) async {
       final userId = invocation.positionalArguments.first as String;
       // Return a profile matching the requested user ID
       return UserProfile(
         id: userId,
         email: '<EMAIL>',
         firstName: 'Test',
         lastName: 'User',
         role: 'player_parent',
       );
    });

    // Default password update success
    when(() => mockAuthService.updatePassword(any())).thenAnswer((_) async {});

    // Ignore stream by default
    when(() => mockAuthService.onAuthStateChange).thenAnswer((_) => const Stream.empty());

    // Mock JWT validation methods
    when(() => mockAuthService.isCurrentTokenValid()).thenReturn(true);
    when(() => mockAuthService.logInitialSessionValidity()).thenAnswer((_) async {});

    // --- Create BLoC ---
    authBloc = AuthBloc(authService: mockAuthService);
  });

  tearDown(() {
    authBloc.close();
  });

  group('AuthBloc Callback Tests (Triggered by Listener)', () {

    // --- Test Email Confirmation Success ---
    blocTest<AuthBloc, AuthState>(
      'emits [AuthLoading, AuthAuthenticated] on AuthSupaUISuccess with confirmed email',
      setUp: () {
        // Ensure profile fetch for this *specific* confirmed user returns a profile
        when(() => mockAuthService.getUserProfile(mockUserConfirmed.id))
            .thenAnswer((_) async => UserProfile(
              id: mockUserConfirmed.id,
              email: mockUserConfirmed.email,
              firstName: 'Confirmed',
              lastName: 'User',
              role: 'player_parent',
            ));
      },
      build: () => authBloc,
      // Seed in the state the user would be in *before* confirmation happens
      seed: () => AuthVerificationEmailSent(email: '<EMAIL>'),
      // Act by simulating the event coming from the stream listener
      act: (bloc) {
         // Create a mock session with the confirmed user
         final confirmedSession = MockSession(user: mockUserConfirmed);
         // Use the public AuthSupaUISuccess event instead of the private _AuthChangeEventOccurred
         bloc.add(AuthSupaUISuccess(confirmedSession));
      },
      expect: () => [
        isA<AuthLoading>().having((s) => s.user?.id, 'user.id', mockUserConfirmed.id),
        isA<AuthAuthenticated>()
            .having((s) => s.user?.id, 'user.id', mockUserConfirmed.id)
            .having((s) => s.userProfile?.id, 'userProfile.id', mockUserConfirmed.id),
      ],
      verify: (_) {
        verify(() => mockAuthService.getUserProfile(mockUserConfirmed.id)).called(1);
      },
    );

    blocTest<AuthBloc, AuthState>(
      'emits [AuthLoading, AuthAuthenticatedNoProfile] on AuthSupaUISuccess if profile fetch returns null',
      setUp: () {
         // Simulate profile fetch failure/null for this user
         when(() => mockAuthService.getUserProfile(mockUserConfirmed.id)).thenAnswer((_) async => null);
      },
      build: () => authBloc,
      seed: () => AuthVerificationEmailSent(email: '<EMAIL>'),
      act: (bloc) {
          // Create a mock session with the confirmed user
          final confirmedSession = MockSession(user: mockUserConfirmed);
          // Use the public AuthSupaUISuccess event instead of the private _AuthChangeEventOccurred
          bloc.add(AuthSupaUISuccess(confirmedSession));
      },
      expect: () => [
         isA<AuthLoading>().having((s) => s.user?.id, 'user.id', mockUserConfirmed.id),
         isA<AuthAuthenticatedNoProfile>().having((s) => s.user?.id, 'user.id', mockUserConfirmed.id),
      ],
      verify: (_) {
        verify(() => mockAuthService.getUserProfile(mockUserConfirmed.id)).called(1);
      },
    );

     blocTest<AuthBloc, AuthState>(
      'emits [AuthLoading, AuthFailure] on AuthSupaUISuccess if profile fetch throws',
      setUp: () {
         // Simulate profile fetch throwing an exception
         when(() => mockAuthService.getUserProfile(mockUserConfirmed.id)).thenThrow(AppException('DB error'));
      },
      build: () => authBloc,
      seed: () => AuthVerificationEmailSent(email: '<EMAIL>'),
      act: (bloc) {
          // Create a mock session with the confirmed user
          final confirmedSession = MockSession(user: mockUserConfirmed);
          // Use the public AuthSupaUISuccess event instead of the private _AuthChangeEventOccurred
          bloc.add(AuthSupaUISuccess(confirmedSession));
      },
      expect: () => [
         isA<AuthLoading>().having((s) => s.user?.id, 'user.id', mockUserConfirmed.id),
         // Expect failure because profile fetch failed
         isA<AuthFailure>().having((s) => s.message, 'message', contains('DB error')),
      ],
      verify: (_) {
        verify(() => mockAuthService.getUserProfile(mockUserConfirmed.id)).called(1);
      },
    );

    // Note: We can't directly test the password recovery flow since it uses the private _AuthChangeEventOccurred event
    // Instead, we'll test the password update functionality which uses a public event

    // --- Test Successful Password Update *after* Recovery ---
    blocTest<AuthBloc, AuthState>(
      'emits [AuthLoading, AuthPasswordResetSuccess] after password update in recovery',
      setUp: () {
        // Mock the password update call to succeed
        when(() => mockAuthService.updatePassword('newPassword123')).thenAnswer((_) async {});
      },
      build: () => authBloc,
      // Seed in the state where user is ready to reset
      seed: () => AuthPasswordRecovery(
            user: mockUserRecovery,
            userProfile: UserProfile(
              id: mockUserRecovery.id,
              email: mockUserRecovery.email,
              firstName: 'Recovery',
              lastName: 'User',
              role: 'player_parent',
            ),
          ),
      // Act by requesting the password update
      act: (bloc) => bloc.add(const AuthUpdatePasswordRequested(password: 'newPassword123')),
      // Expect sequence: Loading -> Success
      expect: () => [
        isA<AuthLoading>(),
        isA<AuthPasswordResetSuccess>(),
      ],
      verify: (_) {
        verify(() => mockAuthService.updatePassword('newPassword123')).called(1);
      },
    );
  });
}
