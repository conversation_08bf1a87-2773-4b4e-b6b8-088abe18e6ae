// test/features/auth/blocs/auth_bloc_supa_ui_test.dart
import 'package:bloc_test/bloc_test.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:supabase_flutter/supabase_flutter.dart' as supabase;
import 'package:soccer_frontend/features/auth/blocs/auth_bloc.dart';
import 'package:soccer_frontend/core/interfaces/auth_service_interface.dart';
import 'package:soccer_frontend/core/utils/app_exception.dart';
import 'package:soccer_frontend/data/models/user_profile.dart';
import '../../../mocks/mocks.dart';

void main() {
  late AuthServiceInterface mockAuthService;
  late AuthBloc authBloc;
  late MockUser mockUser;
  late MockSession mockSession;
  late UserProfile mockUserProfile;

  setUpAll(() {
    registerFallbackValues();
  });

  setUp(() {
    mockAuthService = MockAuthService();
    mockUser = MockUser();
    mockSession = MockSession();
    mockUserProfile = UserProfile(
      id: mockUser.id,
      email: mockUser.email,
      firstName: 'Test',
      lastName: 'User',
      role: 'player_parent',
    );

    // Set up the mock session
    mockSession = MockSession(user: mockUser);

    // Mock the stream getter on the service
    when(() => mockAuthService.onAuthStateChange)
        .thenAnswer((_) => const Stream.empty());

    // Mock JWT validation methods
    when(() => mockAuthService.isCurrentTokenValid())
        .thenReturn(true);
    when(() => mockAuthService.logInitialSessionValidity())
        .thenAnswer((_) async {});

    authBloc = AuthBloc(authService: mockAuthService);
  });

  tearDown(() {
    authBloc.close();
  });

  group('SupaEmailAuth Event Tests', () {
    blocTest<AuthBloc, AuthState>(
      'emits [AuthLoading, AuthAuthenticated] when AuthSupaUISuccess is added and profile fetch succeeds',
      setUp: () {
        when(() => mockAuthService.getUserProfile(mockUser.id))
            .thenAnswer((_) async => mockUserProfile);
      },
      build: () => authBloc,
      act: (bloc) => bloc.add(AuthSupaUISuccess(mockSession)),
      expect: () => [
        isA<AuthLoading>(),
        isA<AuthAuthenticated>().having(
          (state) => state.userProfile,
          'userProfile',
          equals(mockUserProfile),
        ),
      ],
      verify: (_) {
        verify(() => mockAuthService.getUserProfile(mockUser.id)).called(1);
      },
    );

    blocTest<AuthBloc, AuthState>(
      'emits [AuthLoading, AuthAuthenticatedNoProfile] when AuthSupaUISuccess is added but profile fetch returns null',
      setUp: () {
        when(() => mockAuthService.getUserProfile(mockUser.id))
            .thenAnswer((_) async => null);
      },
      build: () => authBloc,
      act: (bloc) => bloc.add(AuthSupaUISuccess(mockSession)),
      expect: () => [
        isA<AuthLoading>(),
        isA<AuthAuthenticatedNoProfile>(),
      ],
      verify: (_) {
        verify(() => mockAuthService.getUserProfile(mockUser.id)).called(1);
      },
    );

    blocTest<AuthBloc, AuthState>(
      'emits [AuthLoading, AuthFailure] when AuthSupaUISuccess is added but profile fetch throws',
      setUp: () {
        when(() => mockAuthService.getUserProfile(mockUser.id))
            .thenThrow(AppException('Profile fetch failed'));
      },
      build: () => authBloc,
      act: (bloc) => bloc.add(AuthSupaUISuccess(mockSession)),
      expect: () => [
        isA<AuthLoading>(),
        isA<AuthFailure>(),
      ],
      verify: (_) {
        verify(() => mockAuthService.getUserProfile(mockUser.id)).called(1);
      },
    );

    blocTest<AuthBloc, AuthState>(
      'emits [AuthFailure] when AuthSupaUIError is added with AuthException',
      build: () => authBloc,
      act: (bloc) => bloc.add(AuthSupaUIError(supabase.AuthException('Invalid credentials'))),
      expect: () => [
        isA<AuthFailure>().having(
          (state) => state.message,
          'message',
          contains('Invalid credentials'),
        ),
      ],
    );

    blocTest<AuthBloc, AuthState>(
      'emits [AuthFailure] when AuthSupaUIError is added with generic error',
      build: () => authBloc,
      act: (bloc) => bloc.add(AuthSupaUIError('Some generic error')),
      expect: () => [
        isA<AuthFailure>().having(
          (state) => state.message,
          'message',
          equals('An error occurred during authentication.'),
        ),
      ],
    );
  });
}
