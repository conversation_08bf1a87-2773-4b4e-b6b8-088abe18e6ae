// test/features/auth/blocs/auth_bloc_test.dart
import 'package:bloc_test/bloc_test.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:soccer_frontend/features/auth/blocs/auth_bloc.dart';
import 'package:soccer_frontend/core/interfaces/auth_service_interface.dart';
import 'package:soccer_frontend/core/utils/app_exception.dart';
import 'package:soccer_frontend/data/models/user_profile.dart';
import 'package:supabase_flutter/supabase_flutter.dart' show AuthException;
import '../../../mocks/mocks.dart';

void main() {
  late AuthServiceInterface mockAuthService;
  late AuthBloc authBloc;
  late MockUser mockUser;
  late UserProfile mockUserProfile;

  setUpAll(() {
    registerFallbackValues();
  });

  setUp(() {
    mockAuthService = MockAuthService();
    mockUser = MockUser();
    mockUserProfile = UserProfile(
      id: mockUser.id,
      email: mockUser.email,
      firstName: 'Test',
      lastName: 'User',
      role: 'player_parent',
    );

    // Mock the stream getter on the service
    when(() => mockAuthService.onAuthStateChange)
        .thenAnswer((_) => const Stream.empty());

    // Mock JWT validation methods
    when(() => mockAuthService.isCurrentTokenValid())
        .thenReturn(true);
    when(() => mockAuthService.logInitialSessionValidity())
        .thenAnswer((_) async {});

    authBloc = AuthBloc(authService: mockAuthService);
  });

  tearDown(() {
    authBloc.close();
  });

  test('initial state is AuthInitial', () {
    expect(authBloc.state, isA<AuthInitial>());
  });

  blocTest<AuthBloc, AuthState>(
    'emits [AuthUnauthenticated] when no user exists on AppStarted',
    setUp: () {
      when(() => mockAuthService.currentUser).thenReturn(null);
    },
    build: () => authBloc,
    act: (bloc) => bloc.add(AuthAppStarted()),
    expect: () => [isA<AuthUnauthenticated>()],
  );

  blocTest<AuthBloc, AuthState>(
    'emits [AuthLoading, AuthAuthenticated] when user exists and profile fetch succeeds',
    setUp: () {
      when(() => mockAuthService.currentUser).thenReturn(mockUser);
      when(() => mockAuthService.getUserProfile(mockUser.id))
          .thenAnswer((_) async => mockUserProfile);
    },
    build: () => authBloc,
    act: (bloc) => bloc.add(AuthAppStarted()),
    expect: () => [
      isA<AuthLoading>(),
      isA<AuthAuthenticated>().having(
        (state) => state.userProfile,
        'userProfile',
        equals(mockUserProfile),
      ),
    ],
  );

  blocTest<AuthBloc, AuthState>(
    'emits [AuthLoading, AuthAuthenticatedNoProfile] when user exists but profile fetch returns null',
    setUp: () {
      when(() => mockAuthService.currentUser).thenReturn(mockUser);
      when(() => mockAuthService.getUserProfile(mockUser.id))
          .thenAnswer((_) async => null);
    },
    build: () => authBloc,
    act: (bloc) => bloc.add(AuthAppStarted()),
    expect: () => [
      isA<AuthLoading>(),
      isA<AuthAuthenticatedNoProfile>(),
    ],
  );

  blocTest<AuthBloc, AuthState>(
    'emits [AuthLoading, AuthFailure] when profile fetch throws exception',
    setUp: () {
      when(() => mockAuthService.currentUser).thenReturn(mockUser);
      when(() => mockAuthService.getUserProfile(mockUser.id))
          .thenThrow(AppException('Profile fetch failed'));
    },
    build: () => authBloc,
    act: (bloc) => bloc.add(AuthAppStarted()),
    expect: () => [
      isA<AuthLoading>(),
      isA<AuthFailure>(),
    ],
  );

  // Tests for AuthSignInRequested and AuthSignUpRequested have been removed
  // as they are no longer used with the SupaEmailAuth component.
  // See auth_bloc_supa_ui_test.dart for tests of the new events.

  // --- Test Password Reset Requested ---
  group('AuthPasswordResetRequested Event', () {
    const testEmail = '<EMAIL>';

    blocTest<AuthBloc, AuthState>(
      'emits [AuthLoading, AuthPasswordResetEmailSent] on successful request',
      setUp: () {
        // Service call succeeds
        when(() => mockAuthService.requestPasswordReset(any()))
            .thenAnswer((_) async {});
      },
      build: () => authBloc,
      seed: () => const AuthUnauthenticated(), // Start from relevant state
      act: (bloc) => bloc.add(const AuthPasswordResetRequested(email: testEmail)),
      expect: () => [
        isA<AuthLoading>(),
        isA<AuthPasswordResetEmailSent>().having((s) => s.email, 'email', testEmail),
        // *** NO REVERT TO AuthUnauthenticated expected here anymore ***
      ],
      verify: (_) {
        verify(() => mockAuthService.requestPasswordReset(testEmail)).called(1);
      },
    );

    blocTest<AuthBloc, AuthState>(
      'emits [AuthLoading, AuthFailure] on rate limit error and STAYS in failure',
      setUp: () {
        // Simulate rate limit error from service
        final rateLimitError = AppException.fromSupabaseError(
            AuthException('Too many password reset attempts. Please try again in 60 minutes.', statusCode: '429'));
        when(() => mockAuthService.requestPasswordReset(testEmail))
            .thenThrow(rateLimitError);
      },
      build: () => authBloc,
      seed: () => const AuthUnauthenticated(),
      act: (bloc) => bloc.add(const AuthPasswordResetRequested(email: testEmail)),
      expect: () => [
        isA<AuthLoading>(),
        isA<AuthFailure>().having((s) => s.message, 'message', 'Too many password reset attempts. Please try again in 60 minutes.'),
        // *** It should STAY in AuthFailure for rate limit ***
      ],
      verify: (_) {
        verify(() => mockAuthService.requestPasswordReset(testEmail)).called(1);
      },
    );

    blocTest<AuthBloc, AuthState>(
      'emits [AuthLoading, AuthFailure, AuthUnauthenticated] on other errors (with delay)',
      setUp: () {
        // Simulate other error from service
        final otherError = AppException('Network error');
        when(() => mockAuthService.requestPasswordReset(testEmail))
            .thenThrow(otherError);
      },
      build: () => authBloc,
      seed: () => const AuthUnauthenticated(),
      act: (bloc) => bloc.add(const AuthPasswordResetRequested(email: testEmail)),
      // Expect sequence with revert (if delay is implemented)
      expect: () => [
        isA<AuthLoading>(),
        isA<AuthFailure>().having((s) => s.message, 'message', 'Network error'),
        isA<AuthUnauthenticated>(), // If the 3-second delay revert is active
      ],
      // Use wait if testing the delayed revert:
      wait: const Duration(seconds: 4),
      verify: (_) {
        verify(() => mockAuthService.requestPasswordReset(testEmail)).called(1);
      },
    );
  });
}
