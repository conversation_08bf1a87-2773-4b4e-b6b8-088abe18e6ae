import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart'; // Added for CupertinoActivityIndicator
import 'package:mocktail/mocktail.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:bloc_test/bloc_test.dart';
import 'package:go_router/go_router.dart';
import 'package:soccer_frontend/features/auth/blocs/auth_bloc.dart';
import 'package:soccer_frontend/features/auth/screens/forgot_password_screen.dart';
import 'package:soccer_frontend/features/auth/cubits/forgot_password_cubit.dart'; // Added
import 'package:soccer_frontend/core/interfaces/auth_service_interface.dart'; // Added
import 'package:soccer_frontend/features/auth/screens/check_email_screen.dart'; // Import for mock route

class MockAuthBloc extends Mock implements AuthBloc {} // Renamed
class MockForgotPasswordCubit extends MockCubit<ForgotPasswordState> implements ForgotPasswordCubit {} // Added
class MockAuthServiceInterface extends Mock implements AuthServiceInterface {} // Added


void main() {
  late MockAuthBloc mockAuthBloc; // Renamed
  late MockForgotPasswordCubit mockForgotPasswordCubit; // Added
  late MockAuthServiceInterface mockAuthServiceInterface; // Added
  late GoRouter mockGoRouter;

  setUp(() {
    mockAuthBloc = MockAuthBloc(); // Renamed
    mockForgotPasswordCubit = MockForgotPasswordCubit(); // Added
    mockAuthServiceInterface = MockAuthServiceInterface(); // Added

    mockGoRouter = GoRouter(
      initialLocation: '/forgot-password',
      routes: [
        GoRoute(
          path: '/forgot-password',
          name: 'forgotPassword',
          builder: (context, state) => RepositoryProvider<AuthServiceInterface>.value(
            value: mockAuthServiceInterface,
            child: BlocProvider<ForgotPasswordCubit>.value(
              value: mockForgotPasswordCubit,
              child: const ForgotPasswordScreen(),
            ),
          ),
        ),
        GoRoute(
          path: '/check-email',
          name: 'checkEmail',
          builder: (context, state) => CheckEmailScreen(
            email: state.uri.queryParameters['email'] ?? '<EMAIL>',
            isPasswordReset: (state.uri.queryParameters['reset'] ?? 'false').toLowerCase() == 'true',
          ),
        ),
      ],
    );

    // Default stubbing for AuthBloc (if needed by MaterialApp or other parts)
    when(() => mockAuthBloc.state).thenReturn(const AuthUnauthenticated());
    when(() => mockAuthBloc.stream).thenAnswer((_) => Stream.fromIterable([const AuthUnauthenticated()]));

    // Default stubbing for ForgotPasswordCubit
    when(() => mockForgotPasswordCubit.state).thenReturn(const ForgotPasswordInitial());
    when(() => mockForgotPasswordCubit.stream).thenAnswer((_) => Stream.fromIterable([const ForgotPasswordInitial()]));
    when(() => mockForgotPasswordCubit.requestPasswordReset(any())).thenAnswer((_) async {});
  });

  Widget makeTestable({required AuthBloc authBloc}) => BlocProvider<AuthBloc>.value( // Pass AuthBloc
        value: authBloc, // Use passed AuthBloc
        child: MaterialApp.router(
          routerConfig: mockGoRouter, // mockGoRouter from setUp now includes ForgotPasswordCubit provider
        ),
      );

  testWidgets('navigates to CheckEmailScreen on ForgotPasswordSuccess', // Renamed for clarity
      (WidgetTester tester) async {
    const testEmail = '<EMAIL>';
    // whenListen provides the stream of states for ForgotPasswordCubit
    whenListen(
      mockForgotPasswordCubit,
      Stream<ForgotPasswordState>.fromIterable([
        const ForgotPasswordInitial(),      // Initial state
        const ForgotPasswordSubmitting(),   // Simulating user interaction leading to submission
        ForgotPasswordSuccess(email: testEmail), // Success state
      ]),
      initialState: const ForgotPasswordInitial(),
    );

    await tester.pumpWidget(makeTestable(authBloc: mockAuthBloc)); // Pass mockAuthBloc

    // Pump for ForgotPasswordInitial (initial form)
    await tester.pump();
    expect(find.widgetWithText(ElevatedButton, 'Send Reset Link'), findsOneWidget);

    // Pump for ForgotPasswordSubmitting
    when(() => mockForgotPasswordCubit.state).thenReturn(const ForgotPasswordSubmitting());
    await tester.pump(); // Process ForgotPasswordSubmitting
    // We will not assert the intermediate loading UI here for this navigation test,
    // as it's covered by other tests and has proven brittle in this sequence.

    // Pump for ForgotPasswordSuccess and navigation
    when(() => mockForgotPasswordCubit.state).thenReturn(const ForgotPasswordSuccess(email: testEmail));
    await tester.pump(); // Process ForgotPasswordSuccess
    
    // After the state that triggers navigation, pump(Duration.zero) to allow microtasks (like navigation) to run
    await tester.pump(Duration.zero);
    // Then pumpAndSettle to wait for animations and other frames to complete
    await tester.pumpAndSettle();

    // Assert: Check that the CheckEmailScreen is now visible
    expect(find.text('Password Reset Email Sent'), findsOneWidget);
    expect(find.textContaining(testEmail), findsOneWidget);
    // Verify navigation occurred by checking if ForgotPasswordScreen elements are gone
    expect(find.widgetWithText(ElevatedButton, 'Send Reset Link'), findsNothing);
  });
}
