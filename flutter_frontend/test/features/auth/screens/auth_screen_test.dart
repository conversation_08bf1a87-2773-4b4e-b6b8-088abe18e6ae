import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:soccer_frontend/features/auth/blocs/auth_bloc.dart';
import 'package:soccer_frontend/features/auth/screens/auth_screen.dart';
import 'package:soccer_frontend/core/interfaces/auth_service_interface.dart';

class MockAuthService extends Mock implements AuthServiceInterface {}
class MockAuthBloc extends Mock implements AuthBloc {}

void main() {
  late MockAuthBloc mockAuthBloc;

  setUp(() {
    mockAuthBloc = MockAuthBloc();
  });

  testWidgets('AuthScreen renders SupaEmailAuth widget', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(
      MaterialApp(
        home: BlocProvider<AuthBloc>.value(
          value: mockAuthBloc,
          child: const AuthScreen(),
        ),
      ),
    );

    // Verify that the SupaEmailAuth widget is rendered
    expect(find.text('Tournament Scheduler'), findsOneWidget);
    expect(find.text('Account Login'), findsOneWidget);
    expect(find.text('Register a Club'), findsOneWidget);
  });
}
