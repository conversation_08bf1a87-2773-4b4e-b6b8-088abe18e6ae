// test/features/auth/screens/forgot_password_widget_test.dart

import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart'; // Added for CupertinoActivityIndicator
import 'package:mocktail/mocktail.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:bloc_test/bloc_test.dart';
import 'package:go_router/go_router.dart';

// Adjust import paths based on your final structure
import 'package:soccer_frontend/features/auth/blocs/auth_bloc.dart';
import 'package:soccer_frontend/features/auth/screens/forgot_password_screen.dart';
import 'package:soccer_frontend/features/auth/cubits/forgot_password_cubit.dart'; // Corrected: Imports states too
// import 'package:soccer_frontend/features/auth/cubits/forgot_password_state.dart'; // Removed: Part of cubit.dart
import 'package:soccer_frontend/core/interfaces/auth_service_interface.dart'; // Added
import 'package:soccer_frontend/features/auth/screens/check_email_screen.dart'; // Import for mock route

class MockAuthBloc extends Mock implements AuthBloc {}
class MockForgotPasswordCubit extends MockCubit<ForgotPasswordState> implements ForgotPasswordCubit {} // Added
class MockAuthServiceInterface extends Mock implements AuthServiceInterface {} // Added

// Mock GoRouter for testing navigation
class MockGoRouter extends Mock implements GoRouter {}

void main() {
  late MockAuthBloc mockAuthBloc;
  late MockForgotPasswordCubit mockForgotPasswordCubit; // Added
  late MockAuthServiceInterface mockAuthServiceInterface; // Added
  late GoRouter mockGoRouter;

  setUp(() {
    mockAuthBloc = MockAuthBloc();
    mockForgotPasswordCubit = MockForgotPasswordCubit(); // Added
    mockAuthServiceInterface = MockAuthServiceInterface(); // Added

    // Create a real GoRouter instance with minimal routes for testing
    mockGoRouter = GoRouter(
      initialLocation: '/forgot-password', // Start at the screen being tested
      routes: [
        GoRoute(
          path: '/forgot-password',
          name: 'forgotPassword', // Ensure this name matches if used by goNamed
          builder: (context, state) => RepositoryProvider<AuthServiceInterface>.value(
            value: mockAuthServiceInterface,
            child: BlocProvider<ForgotPasswordCubit>.value(
              value: mockForgotPasswordCubit,
              child: const ForgotPasswordScreen(),
            ),
          ),
        ),
        GoRoute(
          path: '/check-email', // Add the target route for navigation
          name: 'checkEmail',
          builder: (context, state) => CheckEmailScreen(
            email: state.uri.queryParameters['email'] ?? '<EMAIL>', 
            isPasswordReset: (state.uri.queryParameters['reset'] ?? 'false').toLowerCase() == 'true',
          ),
        ),
        // Add other routes if ForgotPasswordScreen navigates to them
      ],
    );

    // --- Default Stubbing ---
    when(() => mockAuthBloc.state).thenReturn(const AuthUnauthenticated()); // For AuthBloc if needed by other parts
    when(() => mockAuthBloc.stream).thenAnswer((_) => Stream.fromIterable([const AuthUnauthenticated()]));

    // Default stubbing for ForgotPasswordCubit
    when(() => mockForgotPasswordCubit.state).thenReturn(const ForgotPasswordInitial());
    when(() => mockForgotPasswordCubit.stream).thenAnswer((_) => Stream.fromIterable([const ForgotPasswordInitial()]));
    // Stub the requestPasswordReset method if it's called without specific arrangements in tests
    when(() => mockForgotPasswordCubit.requestPasswordReset(any())).thenAnswer((_) async {});

  });

  // Helper function to pump the widget with the BLoC provider and GoRouter
  Widget createTestableWidget({required AuthBloc authBloc}) { // Renamed param for clarity
    return BlocProvider<AuthBloc>.value(
      value: authBloc, // Use the passed AuthBloc
      child: MaterialApp.router(
        routerConfig: mockGoRouter, // Use the mockGoRouter instance from setUp
      ),
    );
  }

  testWidgets('Displays initial form correctly', (WidgetTester tester) async {
    // Arrange
    // Ensure ForgotPasswordCubit is in its initial state for this test
    when(() => mockForgotPasswordCubit.state).thenReturn(const ForgotPasswordInitial());
    when(() => mockForgotPasswordCubit.stream).thenAnswer((_) => Stream.fromIterable([const ForgotPasswordInitial()]));

    await tester.pumpWidget(createTestableWidget(authBloc: mockAuthBloc));
    await tester.pumpAndSettle(); // Ensure all initial frames are processed

    // Assert: Check for elements of the initial form
    expect(find.text('Forgot Password'), findsOneWidget); // Screen title/header
    expect(find.widgetWithText(TextFormField, 'Your email'), findsOneWidget); // Email field
    expect(find.widgetWithText(ElevatedButton, 'Send Reset Link'), findsOneWidget); // Button
    // Success pane (CheckEmailScreen) should not be visible initially
    expect(find.text('Password Reset Email Sent'), findsNothing);
    expect(find.byType(CircularProgressIndicator), findsNothing); // No loading spinner
  });

   testWidgets('Shows loading indicator when state is ForgotPasswordSubmitting', (tester) async {
      // Arrange
      whenListen(
        mockForgotPasswordCubit,
        Stream<ForgotPasswordState>.fromIterable([ const ForgotPasswordSubmitting() ]),
        initialState: const ForgotPasswordInitial(),
      );
      await tester.pumpWidget(createTestableWidget(authBloc: mockAuthBloc));
      await tester.pump(); // Process the ForgotPasswordSubmitting state

      // Assert
      // Find the ElevatedButton, then check its child is a CircularProgressIndicator
      final elevatedButtonFinder = find.byType(ElevatedButton);
      expect(elevatedButtonFinder, findsOneWidget); // Ensure the button itself is found

      // Check that the ElevatedButton is disabled (onPressed is null)
      final buttonWidget = tester.widget<ElevatedButton>(elevatedButtonFinder);
      expect(buttonWidget.onPressed, isNull);

      // Check that a progress indicator is visible somewhere on the screen
      expect(find.byWidgetPredicate((widget) => widget is CircularProgressIndicator || widget is CupertinoActivityIndicator), findsOneWidget);
      expect(find.widgetWithText(TextFormField, 'Your email'), findsOneWidget); // Form field still visible
      expect(find.widgetWithText(ElevatedButton, 'Send Reset Link'), findsNothing); // Text is gone
  });

    testWidgets('Shows error message when state is ForgotPasswordFailure', (tester) async {
      // Arrange
      const errorMessage = 'Invalid email address';
      whenListen(
        mockForgotPasswordCubit,
        Stream<ForgotPasswordState>.fromIterable([ const ForgotPasswordFailure(message: errorMessage) ]), // Corrected
        initialState: const ForgotPasswordInitial(),
      );
      await tester.pumpWidget(createTestableWidget(authBloc: mockAuthBloc));
      await tester.pump(); // Process the failure state

      // Assert
      expect(find.text(errorMessage), findsOneWidget); // Error text found
      expect(find.widgetWithText(ElevatedButton, 'Send Reset Link'), findsOneWidget); // Button visible
      expect(find.byType(CircularProgressIndicator), findsNothing); // No spinner
      expect(find.text('Password Reset Email Sent'), findsNothing); // Success pane (CheckEmailScreen) hidden
    });

  testWidgets('Navigates to CheckEmailScreen when state is ForgotPasswordSuccess',
      (WidgetTester tester) async {

    // Arrange: Simulate ForgotPasswordCubit emitting states sequentially
    const testEmail = '<EMAIL>';
    whenListen(
      mockForgotPasswordCubit,
      Stream<ForgotPasswordState>.fromIterable([
        const ForgotPasswordSubmitting(),       // 1. Start loading
        const ForgotPasswordSuccess(email: testEmail), // 2. Emit success - Corrected
      ]),
      initialState: const ForgotPasswordInitial(), // State before action starts
    );

    // Act: Pump the initial widget
    await tester.pumpWidget(createTestableWidget(authBloc: mockAuthBloc));
    // Initial state should show the form
    expect(find.widgetWithText(ElevatedButton, 'Send Reset Link'), findsOneWidget);

    // Act: Process the ForgotPasswordSubmitting state
    when(() => mockForgotPasswordCubit.state).thenReturn(const ForgotPasswordSubmitting());
    await tester.pump(); // Process ForgotPasswordSubmitting
    // We will not assert the intermediate loading UI here for this navigation test,
    // as it's covered by 'Shows loading indicator when state is ForgotPasswordSubmitting'
    // and has proven brittle in this sequence.

    // Act: Process the ForgotPasswordSuccess state
    when(() => mockForgotPasswordCubit.state).thenReturn(const ForgotPasswordSuccess(email: testEmail));
    // This state should trigger navigation in the BlocListener within ForgotPasswordScreen
    await tester.pump(); // Process ForgotPasswordSuccess
    
    // After the state that triggers navigation, pump(Duration.zero) to allow microtasks (like navigation) to run
    await tester.pump(Duration.zero); 
    // Then pumpAndSettle to wait for animations and other frames to complete
    await tester.pumpAndSettle(); 

    // Assert: Check that the CheckEmailScreen is now visible
    // (CheckEmailScreen should display "Password Reset Email Sent" or similar)
    expect(find.text('Password Reset Email Sent'), findsOneWidget); 
    expect(find.textContaining(testEmail), findsOneWidget); // Check email display on CheckEmailScreen

    // Assert: Check that the original ForgotPasswordScreen form elements are gone
    expect(find.widgetWithText(TextFormField, 'Your email'), findsNothing);
    expect(find.widgetWithText(ElevatedButton, 'Send Reset Link'), findsNothing);
  });
}
