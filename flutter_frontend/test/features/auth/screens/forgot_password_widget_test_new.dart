// test/features/auth/screens/forgot_password_widget_test.dart

import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:mocktail/mocktail.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:bloc_test/bloc_test.dart';

// Adjust import paths based on your final structure
import 'package:soccer_frontend/features/auth/blocs/auth_bloc.dart';
import 'package:soccer_frontend/features/auth/screens/forgot_password_screen.dart';

class MockAuthBloc extends Mock implements AuthBloc {}
class FakeAuthState extends Fake implements AuthState {}
class FakeAuthEvent extends Fake implements AuthEvent {}

// Mock for GoRouter
class MockGoRouter extends Mock implements GoRouter {}
class MockNavigatorObserver extends Mock implements NavigatorObserver {}


void main() {
  setUpAll(() {
    registerFallbackValue(FakeAuthState());
    registerFallbackValue(FakeAuthEvent());
    // Fallback for RoutePredicate
    registerFallbackValue((Route<dynamic> route) => false);
  });

  late MockAuthBloc mockAuthBloc;
  late MockGoRouter mockGoRouter;
  late MockNavigatorObserver mockNavigatorObserver;

  setUp(() {
    mockAuthBloc = MockAuthBloc();
    mockGoRouter = MockGoRouter(); // Initialize mockGoRouter
    mockNavigatorObserver = MockNavigatorObserver();

    // --- Default Stubbing for AuthBloc ---
    when(() => mockAuthBloc.state).thenReturn(const AuthUnauthenticated());
    when(() => mockAuthBloc.stream).thenAnswer((_) => Stream<AuthState>.fromIterable([
          const AuthUnauthenticated()
        ]));
    
    // --- Default Stubbing for GoRouter ---
    // Ensure that a basic `goNamed` call doesn't throw null errors by default
    // You might need to refine this if specific return values are expected by the widget
    when(() => mockGoRouter.goNamed(any(), queryParameters: any(named: 'queryParameters'), pathParameters: any(named: 'pathParameters'), extra: any(named: 'extra')))
        .thenAnswer((_) async {}); // Default to do nothing
    when(() => mockGoRouter.go(any(), extra: any(named: 'extra'))).thenAnswer((_) async {});


  });

  // Helper function to pump the widget with necessary providers
  Widget makeTestable({Widget? child}) {
    return MaterialApp(
      home: MultiBlocProvider(
        providers: [
          BlocProvider<AuthBloc>.value(value: mockAuthBloc),
        ],
        // Wrap with a Router if you need to test GoRouter actions like push/pop
        // For goNamed, it's often easier to verify the mockGoRouter directly if it's accessible
        // or use a NavigatorObserver.
        // For this test, we'll try with NavigatorObserver.
        child: InheritedGoRouter( // Provide mockGoRouter
          goRouter: mockGoRouter,
          child: child ?? const ForgotPasswordScreen(),
        )
      ),
      // navigatorObservers: [mockNavigatorObserver], // Add observer
    );
  }
  
  // Testable widget specifically for ForgotPasswordScreen with NavigatorObserver
  Widget makeForgotPasswordTestableWithObserver() {
    return MaterialApp(
      navigatorObservers: [mockNavigatorObserver], // Key for verifying navigation
      home: InheritedGoRouter(
        goRouter: mockGoRouter, // Provide the mockGoRouter
        child: BlocProvider<AuthBloc>.value(
          value: mockAuthBloc,
          child: const ForgotPasswordScreen(),
        ),
      ),
    );
  }


  testWidgets('Displays initial form correctly', (WidgetTester tester) async {
    // Arrange
    await tester.pumpWidget(makeTestable());

    // Assert: Check for elements of the initial form
    expect(find.text('Forgot Password'), findsOneWidget); // Title text
    expect(find.widgetWithText(TextFormField, 'Your email'), findsOneWidget); // Email field
    expect(find.widgetWithText(ElevatedButton, 'Send Reset Link'), findsOneWidget); // Button
    expect(find.text('Enter your email address and we\'ll send you a link to reset your password.'), findsOneWidget);
    expect(find.byType(CircularProgressIndicator), findsNothing); // No loading spinner
  });

  testWidgets('Shows loading indicator when state is AuthLoading', (tester) async {
    // Arrange
    // Use whenListen to control the stream for BlocBuilder
    whenListen(
      mockAuthBloc,
      Stream<AuthState>.fromIterable([ const AuthLoading() ]), // Stream emits loading
      initialState: const AuthUnauthenticated(), // Initial state before stream emits
    );
    await tester.pumpWidget(makeTestable());

    // Act: Pump to process the initial state + the emitted loading state
    await tester.pump(); // Process the loading state

    // Assert
    expect(find.byType(CircularProgressIndicator), findsOneWidget); // Spinner visible
    // Ensure the button shows loading indicator, not text
    final buttonFinder = find.byType(ElevatedButton);
    final button = tester.widget<ElevatedButton>(buttonFinder);
    expect(button.child is CircularProgressIndicator, isTrue);
    
    expect(find.widgetWithText(TextFormField, 'Your email'), findsOneWidget); // Form still visible
  });

  testWidgets('Shows error message when state is AuthFailure', (tester) async {
    // Arrange
    const errorMessage = 'Invalid email address';
    whenListen(
      mockAuthBloc,
      Stream<AuthState>.fromIterable([ const AuthFailure(message: errorMessage) ]),
      initialState: const AuthUnauthenticated(),
    );
    await tester.pumpWidget(makeTestable());

    // Act: Pump to process the failure state
    await tester.pump();

    // Assert
    expect(find.text(errorMessage), findsOneWidget); // Error text found
    expect(find.widgetWithText(ElevatedButton, 'Send Reset Link'), findsOneWidget); // Button visible
    expect(find.byType(CircularProgressIndicator), findsNothing); // No spinner
  });

  // This test needs to be adapted because the screen navigates, not shows a success pane.
  // We will verify navigation in a separate test using MockNavigatorObserver.
  testWidgets('BlocBuilder does not rebuild for AuthPasswordResetEmailSent due to buildWhen',
      (WidgetTester tester) async {
    // Arrange
    // Start in a state that would show the form
    whenListen(
      mockAuthBloc,
      Stream<AuthState>.fromIterable([
        const AuthUnauthenticated(), // Initial state for builder
        const AuthPasswordResetEmailSent(email: '<EMAIL>') // State that should be handled by listener
      ]),
      initialState: const AuthUnauthenticated(),
    );

    await tester.pumpWidget(makeTestable());
    await tester.pump(); // Process AuthUnauthenticated

    // Ensure form is visible
    expect(find.widgetWithText(TextFormField, 'Your email'), findsOneWidget);

    // Act: Transition to AuthPasswordResetEmailSent
    await tester.pump(); // Process AuthPasswordResetEmailSent

    // Assert: Due to buildWhen, the BlocBuilder should NOT have rebuilt.
    // So, the form elements should still be "found" by finders, even though
    // the listener should have triggered navigation.
    // This confirms the buildWhen logic.
    expect(find.widgetWithText(TextFormField, 'Your email'), findsOneWidget);
    expect(find.widgetWithText(ElevatedButton, 'Send Reset Link'), findsOneWidget);
  });
  
  testWidgets('Navigates to CheckEmailScreen when AuthPasswordResetEmailSent is emitted',
      (WidgetTester tester) async {
    // Arrange
    const testEmail = '<EMAIL>';

    // Configure mockGoRouter to capture calls
    // We expect goNamed to be called.
    // when(() => mockGoRouter.goNamed('checkEmail', queryParameters: {'email': testEmail, 'reset': 'true'}))
    //     .thenAnswer((_) async {}); // Allow the call

    // Use whenListen to simulate the BLoC emitting the state
    whenListen(
      mockAuthBloc,
      Stream<AuthState>.fromIterable([
        const AuthUnauthenticated(), // Initial state
        const AuthPasswordResetEmailSent(email: testEmail) // Target state
      ]),
      initialState: const AuthUnauthenticated(),
    );

    await tester.pumpWidget(makeForgotPasswordTestableWithObserver()); // Use the one with observer
    
    // Initial pump to build with AuthUnauthenticated
    await tester.pump(); 

    // Trigger the state change that should cause navigation
    // This pump processes the AuthPasswordResetEmailSent from the stream
    await tester.pump(); 

    // Assert: Verify that goNamed was called on the mockGoRouter
    // This is the most direct way to test navigation with go_router in widget tests
    // if you inject/provide a mock router.
    verify(() => mockGoRouter.goNamed(
      'checkEmail',
      queryParameters: {'email': testEmail, 'reset': 'true'},
    )).called(1);

    // Alternative using NavigatorObserver (if goNamed doesn't push a new route in a way observer can see easily)
    // This might be tricky if goNamed replaces rather than pushes, or if routes are complex.
    // For simple pushes, this works:
    // verify(() => mockNavigatorObserver.didPush(any, any)).called(1);
    // You might need to capture the route and assert its settings name or path.
  });


  testWidgets('Submits form and dispatches AuthPasswordResetRequested event',
      (WidgetTester tester) async {
    // Arrange
    await tester.pumpWidget(makeTestable());

    // Act: Enter email and submit form
    await tester.enterText(find.byType(TextFormField), '<EMAIL>');
    await tester.tap(find.widgetWithText(ElevatedButton, 'Send Reset Link'));
    await tester.pump();

    // Assert: Verify the event was dispatched to the bloc
    verify(() => mockAuthBloc.add(const AuthPasswordResetRequested(email: '<EMAIL>'))).called(1);
  });

  testWidgets('Does not submit form with invalid email',
      (WidgetTester tester) async {
    // Arrange
    await tester.pumpWidget(makeTestable());

    // Act: Enter invalid email and submit form
    await tester.enterText(find.byType(TextFormField), 'invalid-email');
    await tester.tap(find.widgetWithText(ElevatedButton, 'Send Reset Link'));
    await tester.pump();

    // Assert: Verify the event was NOT dispatched to the bloc
    verifyNever(() => mockAuthBloc.add(any()));
  });
}
