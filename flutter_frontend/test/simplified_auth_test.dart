// test/simplified_auth_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:supabase_flutter/supabase_flutter.dart' hide AuthState; // Hide AuthState to avoid conflict
import 'package:bloc_test/bloc_test.dart'; // Import bloc_test for better BLoC testing
import 'package:soccer_frontend/core/utils/secure_storage_adapter.dart';
import 'package:soccer_frontend/core/services/auth_service.dart';
import 'package:soccer_frontend/features/auth/blocs/auth_bloc.dart';
import 'package:soccer_frontend/core/services/supabase_service.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:soccer_frontend/core/interfaces/auth_service_interface.dart';
import 'package:soccer_frontend/core/interfaces/supabase_service_interface.dart';
import 'package:soccer_frontend/data/models/user_profile.dart'; // Import UserProfile model

// Simple mocks
class MockFlutterSecureStorage extends Mock implements FlutterSecureStorage {}
class MockSupabaseClient extends Mock implements SupabaseClient {}
class MockGoTrueClient extends Mock implements GoTrueClient {}
class MockSupabaseService extends Mock implements SupabaseService, SupabaseServiceInterface {}
class MockAuthService extends Mock implements AuthService, AuthServiceInterface {}
class MockUser extends Mock implements User {
  final String _id;
  final String _email;

  MockUser({
    String id = 'mock-user-id',
    String email = '<EMAIL>',
  }) : _id = id, _email = email;

  @override
  String get id => _id;

  @override
  String get email => _email;
}
class MockSession extends Mock implements Session {
  final User _user;
  final String _accessToken;

  MockSession({
    User? user,
    String accessToken = 'mock-access-token',
  }) : _user = user ?? MockUser(),
       _accessToken = accessToken;

  @override
  User get user => _user;

  @override
  String get accessToken => _accessToken;
}

void main() {
  group('SecureStorageAdapter', () {
    late SecureStorageAdapter secureStorageAdapter;
    late MockFlutterSecureStorage mockFlutterSecureStorage;
    const testSessionKey = supabasePersistSessionKey;
    const testSessionValue = '{"access_token":"abc","refresh_token":"xyz"}';

    setUp(() {
      mockFlutterSecureStorage = MockFlutterSecureStorage();

      // Now we can inject the mock storage
      secureStorageAdapter = SecureStorageAdapter(storage: mockFlutterSecureStorage);

      // Default stubs for read/write/delete
      when(() => mockFlutterSecureStorage.write(key: any(named: 'key'), value: any(named: 'value')))
          .thenAnswer((_) async => Future.value());
      when(() => mockFlutterSecureStorage.read(key: any(named: 'key')))
          .thenAnswer((_) async => null);
      when(() => mockFlutterSecureStorage.delete(key: any(named: 'key')))
          .thenAnswer((_) async => Future.value());
    });

    test('initialize completes successfully', () async {
      await expectLater(secureStorageAdapter.initialize(), completes);
    });

    test('persistSession writes session string to secure storage', () async {
      // Arrange
      when(() => mockFlutterSecureStorage.write(key: testSessionKey, value: testSessionValue))
          .thenAnswer((_) async => Future.value());

      // Act
      await secureStorageAdapter.persistSession(testSessionValue);

      // Assert
      verify(() => mockFlutterSecureStorage.write(key: testSessionKey, value: testSessionValue)).called(1);
    });

    test('hasAccessToken returns true when session exists in storage', () async {
      // Arrange
      when(() => mockFlutterSecureStorage.read(key: testSessionKey))
          .thenAnswer((_) async => testSessionValue);

      // Act
      final result = await secureStorageAdapter.hasAccessToken();

      // Assert
      expect(result, isTrue);
      verify(() => mockFlutterSecureStorage.read(key: testSessionKey)).called(1);
    });
  });

  group('AuthService', () {
    late AuthService authService;
    late MockSupabaseClient mockClient;
    late MockGoTrueClient mockGoTrueClient;
    late MockSupabaseService mockSupabaseService;

    setUp(() {
      mockClient = MockSupabaseClient();
      mockGoTrueClient = MockGoTrueClient();
      mockSupabaseService = MockSupabaseService();

      when(() => mockClient.auth).thenReturn(mockGoTrueClient);

      authService = AuthService(mockClient, mockSupabaseService);
    });

    test('isLoggedIn returns true when session exists', () async {
      // Arrange
      when(() => mockGoTrueClient.currentSession).thenReturn(MockSession());

      // Act
      final result = await authService.isLoggedIn();

      // Assert
      expect(result, isTrue);
    });

    test('isLoggedIn returns false when no session exists', () async {
      // Arrange
      when(() => mockGoTrueClient.currentSession).thenReturn(null);

      // Act
      final result = await authService.isLoggedIn();

      // Assert
      expect(result, isFalse);
    });

    test('currentUser returns user from GoTrueClient', () {
      // Arrange
      final mockUser = MockUser();
      when(() => mockGoTrueClient.currentUser).thenReturn(mockUser);

      // Act
      final result = authService.currentUser;

      // Assert
      expect(result, equals(mockUser));
    });
  });

  group('AuthBloc', () {
    late AuthBloc authBloc;
    late MockAuthService mockAuthService;
    late MockUser mockUser;
    late UserProfile mockUserProfile;

    setUp(() {
      mockAuthService = MockAuthService();
      mockUser = MockUser();
      mockUserProfile = UserProfile(
        id: mockUser.id,
        email: mockUser.email,
        firstName: 'Test',
        lastName: 'User',
        role: 'player',
      );

      // Set up default stubs for the AuthService methods used by AuthBloc
      when(() => mockAuthService.onAuthStateChange).thenAnswer((_) => const Stream.empty());
      when(() => mockAuthService.currentUser).thenReturn(null); // Default to no user
      when(() => mockAuthService.currentSession).thenReturn(null); // Default to no session

      // Add stubs for methods that return Future<void> to avoid null errors
      when(() => mockAuthService.logInitialSessionValidity()).thenAnswer((_) async {}); // Return completed Future<void>

      // Add stub for isCurrentTokenValid
      when(() => mockAuthService.isCurrentTokenValid()).thenReturn(true); // Default to valid token

      // Add stub for getUserProfile
      when(() => mockAuthService.getUserProfile(any())).thenAnswer((_) async => null); // Default to no profile

      authBloc = AuthBloc(authService: mockAuthService);
    });

    tearDown(() {
      authBloc.close();
    });

    test('initial state is AuthInitial', () {
      expect(authBloc.state, isA<AuthInitial>());
    });

    blocTest<AuthBloc, AuthState>(
      'emits [AuthUnauthenticated] when no user exists on AppStarted',
      build: () {
        // Override default stubs if needed
        when(() => mockAuthService.currentUser).thenReturn(null);
        return authBloc;
      },
      act: (bloc) => bloc.add(AuthAppStarted()),
      expect: () => [isA<AuthUnauthenticated>()],
      verify: (_) {
        verify(() => mockAuthService.currentUser).called(1);
        verifyNever(() => mockAuthService.getUserProfile(any())); // Should not try to fetch profile
      },
    );

    blocTest<AuthBloc, AuthState>(
      'emits [AuthLoading, AuthAuthenticatedNoProfile] when user exists but profile fetch returns null',
      build: () {
        // Override default stubs
        when(() => mockAuthService.currentUser).thenReturn(mockUser);
        when(() => mockAuthService.getUserProfile(mockUser.id)).thenAnswer((_) async => null);
        return authBloc;
      },
      act: (bloc) => bloc.add(AuthAppStarted()),
      expect: () => [
        isA<AuthLoading>(),
        isA<AuthAuthenticatedNoProfile>(),
      ],
      verify: (_) {
        verify(() => mockAuthService.currentUser).called(1);
        verify(() => mockAuthService.isCurrentTokenValid()).called(1); // Verify token validity check
        verify(() => mockAuthService.getUserProfile(mockUser.id)).called(1); // Verify profile fetch
      },
    );

    blocTest<AuthBloc, AuthState>(
      'emits [AuthLoading, AuthAuthenticated] when user exists and profile fetch succeeds',
      build: () {
        // Override default stubs
        when(() => mockAuthService.currentUser).thenReturn(mockUser);
        when(() => mockAuthService.getUserProfile(mockUser.id)).thenAnswer((_) async => mockUserProfile);
        return authBloc;
      },
      act: (bloc) => bloc.add(AuthAppStarted()),
      expect: () => [
        isA<AuthLoading>(),
        isA<AuthAuthenticated>(),
      ],
      verify: (_) {
        verify(() => mockAuthService.currentUser).called(1);
        verify(() => mockAuthService.isCurrentTokenValid()).called(1);
        verify(() => mockAuthService.getUserProfile(mockUser.id)).called(1);
      },
    );
  });
}
