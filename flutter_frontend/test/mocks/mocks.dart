// test/mocks/mocks.dart
import 'package:mocktail/mocktail.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:soccer_frontend/core/interfaces/auth_service_interface.dart';
import 'package:soccer_frontend/core/interfaces/supabase_service_interface.dart';

// Define classes needed for fallback values
class SignInWithPasswordCredentials {
  final String email;
  final String password;
  const SignInWithPasswordCredentials({required this.email, required this.password});
}

class SignUpWithPasswordCredentials {
  final String email;
  final String password;
  const SignUpWithPasswordCredentials({required this.email, required this.password});
}

class AuthOptions {
  const AuthOptions();
}

// Mock External Dependencies
class MockSupabaseClient extends Mock implements SupabaseClient {}
class MockGoTrueClient extends Mock implements GoTrueClient {}
class MockFlutterSecureStorage extends Mock implements FlutterSecureStorage {}
class MockSupabaseQueryBuilder extends Mock implements SupabaseQueryBuilder {}
class MockPostgrestFilterBuilder extends Mock implements PostgrestFilterBuilder {}
class MockFunctionsClient extends Mock implements FunctionsClient {}
// Removed StorageClient mock as it's not found
class MockRealtimeClient extends Mock implements RealtimeClient {}

// Mock Internal Services
class MockAuthService extends Mock implements AuthServiceInterface {}
class MockSupabaseService extends Mock implements SupabaseServiceInterface {}

// Mock Supabase Models
class MockUser extends Mock implements User {
  final String _id;
  final String? _email;
  final Map<String, dynamic> _appMetadata;
  final Map<String, dynamic> _userMetadata;
  final String? _createdAt;
  final String? _emailConfirmedAt;
  final String? _phoneConfirmedAt;

  MockUser({
    String id = 'mock-user-id',
    String? email = '<EMAIL>',
    Map<String, dynamic> appMetadata = const {},
    Map<String, dynamic> userMetadata = const {},
    String? createdAt,
    String? emailConfirmedAt,
    String? phoneConfirmedAt,
  }) : _id = id,
       _email = email,
       _appMetadata = appMetadata,
       _userMetadata = userMetadata,
       _createdAt = createdAt,
       _emailConfirmedAt = emailConfirmedAt,
       _phoneConfirmedAt = phoneConfirmedAt;

  @override
  String get id => _id;

  @override
  String? get email => _email;

  @override
  Map<String, dynamic> get appMetadata => _appMetadata;

  @override
  Map<String, dynamic> get userMetadata => _userMetadata;

  @override
  String get createdAt => _createdAt ?? DateTime.now().toIso8601String();

  @override
  String? get emailConfirmedAt => _emailConfirmedAt;

  @override
  String? get phoneConfirmedAt => _phoneConfirmedAt;

  // Mock equality based on ID for simplicity in tests
  @override
  bool operator ==(Object other) =>
      identical(this, other) || other is MockUser && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;
}

class MockSession extends Mock implements Session {
  final User _user;
  final String _accessToken;
  final String _refreshToken;

  MockSession({
    User? user,
    String accessToken = 'mock-access-token',
    String refreshToken = 'mock-refresh-token',
  }) : _user = user ?? MockUser(),
       _accessToken = accessToken,
       _refreshToken = refreshToken;

  @override
  User get user => _user;

  @override
  String get accessToken => _accessToken;

  @override
  String get refreshToken => _refreshToken;
}
class MockAuthState extends Mock implements AuthState {}

// Mock UserResponse for updateUser tests
class MockUserResponse extends Mock implements UserResponse {
  final User _user;

  MockUserResponse({required User user}) : _user = user;

  @override
  User get user => _user;
}

// Helper to register fallback values for mocktail
void registerFallbackValues() {
  // Basic Types
  registerFallbackValue(Uri());
  registerFallbackValue(<String, dynamic>{}); // For Maps

  // Supabase Specific Types used as arguments or return types
  registerFallbackValue(MockUser()); // For User objects
  registerFallbackValue(MockSession()); // For Session objects
  registerFallbackValue(MockAuthState()); // For Supabase AuthState objects
  registerFallbackValue(AuthChangeEvent.signedIn); // Specific enum value
  registerFallbackValue(UserAttributes(password: 'password')); // For updateUser/updatePassword

  // Add these for SignInWithPassword and SignUp methods
  registerFallbackValue(const SignInWithPasswordCredentials(
    email: '<EMAIL>',
    password: 'password'
  ));
  registerFallbackValue(const SignUpWithPasswordCredentials(
    email: '<EMAIL>',
    password: 'password'
  ));

  // Add AuthOptions for other auth methods
  registerFallbackValue(const AuthOptions());
}
