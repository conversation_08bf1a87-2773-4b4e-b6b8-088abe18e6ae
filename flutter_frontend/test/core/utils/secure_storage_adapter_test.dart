// test/core/utils/secure_storage_adapter_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:soccer_frontend/core/utils/secure_storage_adapter.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../mocks/mocks.dart';

void main() {
  late SecureStorageAdapter secureStorageAdapter;
  late MockFlutterSecureStorage mockFlutterSecureStorage;
  const testSessionKey = supabasePersistSessionKey;
  const testSessionValue = '{"access_token":"abc","refresh_token":"xyz"}';

  setUp(() {
    mockFlutterSecureStorage = MockFlutterSecureStorage();

    // Now we can inject the mock storage
    secureStorageAdapter = SecureStorageAdapter(storage: mockFlutterSecureStorage);

    // Default stubs for read/write/delete
    when(() => mockFlutterSecureStorage.write(key: any(named: 'key'), value: any(named: 'value')))
        .thenAnswer((_) async => Future.value());
    when(() => mockFlutterSecureStorage.read(key: any(named: 'key')))
        .thenAnswer((_) async => null);
    when(() => mockFlutterSecureStorage.delete(key: any(named: 'key')))
        .thenAnswer((_) async => Future.value());
  });

  test('initialize completes successfully (no-op)', () async {
    await expectLater(secureStorageAdapter.initialize(), completes);
  });

  group('SecureStorageAdapter', () {
    test('persistSession writes session string to secure storage', () async {
      // Arrange
      when(() => mockFlutterSecureStorage.write(key: testSessionKey, value: testSessionValue))
          .thenAnswer((_) async => Future.value());

      // Act
      await secureStorageAdapter.persistSession(testSessionValue);

      // Assert
      verify(() => mockFlutterSecureStorage.write(key: testSessionKey, value: testSessionValue)).called(1);
    });

    test('hasAccessToken returns true when session exists in storage', () async {
      // Arrange
      when(() => mockFlutterSecureStorage.read(key: testSessionKey))
          .thenAnswer((_) async => testSessionValue);

      // Act
      final result = await secureStorageAdapter.hasAccessToken();

      // Assert
      expect(result, isTrue);
      verify(() => mockFlutterSecureStorage.read(key: testSessionKey)).called(1);
    });

    test('hasAccessToken returns false when session does not exist', () async {
      // Arrange
      when(() => mockFlutterSecureStorage.read(key: testSessionKey))
          .thenAnswer((_) async => null);

      // Act
      final result = await secureStorageAdapter.hasAccessToken();

      // Assert
      expect(result, isFalse);
      verify(() => mockFlutterSecureStorage.read(key: testSessionKey)).called(1);
    });

    test('accessToken returns session string when it exists', () async {
      // Arrange
      when(() => mockFlutterSecureStorage.read(key: testSessionKey))
          .thenAnswer((_) async => testSessionValue);

      // Act
      final result = await secureStorageAdapter.accessToken();

      // Assert
      expect(result, testSessionValue);
      verify(() => mockFlutterSecureStorage.read(key: testSessionKey)).called(1);
    });

    test('accessToken returns null when session does not exist', () async {
      // Arrange
      when(() => mockFlutterSecureStorage.read(key: testSessionKey))
          .thenAnswer((_) async => null);

      // Act
      final result = await secureStorageAdapter.accessToken();

      // Assert
      expect(result, isNull);
      verify(() => mockFlutterSecureStorage.read(key: testSessionKey)).called(1);
    });

    test('removePersistedSession deletes session from storage', () async {
      // Arrange
      when(() => mockFlutterSecureStorage.delete(key: testSessionKey))
          .thenAnswer((_) async => Future.value());

      // Act
      await secureStorageAdapter.removePersistedSession();

      // Assert
      verify(() => mockFlutterSecureStorage.delete(key: testSessionKey)).called(1);
    });
  });
}
