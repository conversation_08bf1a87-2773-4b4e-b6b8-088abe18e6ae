// test/core/utils/jwt_validation_util_test.dart
import 'package:flutter_test/flutter_test.dart';
import '../../../lib/core/utils/jwt_validation_util.dart';

void main() {
  group('isTokenExpired', () {
    // --- Generate Sample Tokens (Replace with actual structure if needed) ---
    // IMPORTANT: These are structurally valid but have invalid signatures.
    // Use jwt.io or similar to create tokens with correct structure but controlled expiry.
    // Header: {"alg": "HS256", "typ": "JWT"} (Base64: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9)
    // Payload (Expired): {"exp": 1672531199, "sub": "user1"} (Unix timestamp for 2022-12-31 23:59:59 GMT)
    //                 (Base64: eyJleHAiOjE2NzI1MzExOTksInN1YiI6InVzZXIxIn0)
    // Payload (Valid): {"exp": 2672531199, "sub": "user2"} (Unix timestamp for 2054...)
    //                (Base64: eyJleHAiOjI2NzI1MzExOTksInN1YiI6InVzZXIyIn0)
    // Signature: Needs a secret - we'll use dummy signature part for structure. (Base64: abcdefg)

    const String expiredToken =
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE2NzI1MzExOTksInN1YiI6InVzZXIxIn0.abcdefg';
    const String validToken =
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjI2NzI1MzExOTksInN1YiI6InVzZXIyIn0.abcdefg'; // Far future expiry
    const String malformedToken = 'this.is.not.a.jwt';
    const String tokenWithoutExp =
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJ1c2VyMyJ9.abcdefg'; // No 'exp' claim


    test('should return true for an expired token', () {
      expect(isTokenExpired(expiredToken), isTrue);
    });

    test('should return false for a valid (non-expired) token', () {
      expect(isTokenExpired(validToken), isFalse);
    });

    test('should return true for a null token', () {
      expect(isTokenExpired(null), isTrue);
    });

    test('should return true for an empty token', () {
      expect(isTokenExpired(''), isTrue);
    });

    test('should return true for a malformed token', () {
      // jwt_decoder throws FormatException, our util should catch and return true
      expect(isTokenExpired(malformedToken), isTrue);
    });

    test('should return true for a token without an expiration claim', () {
      // jwt_decoder might throw or return false depending on implementation,
      // our wrapper should treat missing 'exp' as invalid/expired for safety.
      expect(isTokenExpired(tokenWithoutExp), isTrue);
    });
  });

  // Add tests for getTokenExpirationDate and decodeTokenPayload if you use them
  group('getTokenExpirationDate', () {
    const String expiredToken =
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE2NzI1MzExOTksInN1YiI6InVzZXIxIn0.abcdefg';
    const String validToken =
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjI2NzI1MzExOTksInN1YiI6InVzZXIyIn0.abcdefg';
    const String malformedToken = 'this.is.not.a.jwt';
    const String tokenWithoutExp =
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJ1c2VyMyJ9.abcdefg';

    test('should return correct DateTime for valid token', () {
      final date = getTokenExpirationDate(validToken);
      expect(date, isNotNull);
      // Check year or rough time range
      expect(date?.year, greaterThan(2050));
    });

    test('should return date for expired token', () {
      // It should still return the date, even if it's in the past
      final date = getTokenExpirationDate(expiredToken);
      expect(date, isNotNull);
      expect(date?.year, lessThan(DateTime.now().year + 1)); // Should be in the past
    });

    test('should return null for invalid token', () {
      expect(getTokenExpirationDate(malformedToken), isNull);
      expect(getTokenExpirationDate(null), isNull);
      expect(getTokenExpirationDate(''), isNull);
    });

    test('should return null for token without exp claim', () {
      expect(getTokenExpirationDate(tokenWithoutExp), isNull);
    });
  });

  group('decodeTokenPayload', () {
    const String validToken =
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjI2NzI1MzExOTksInN1YiI6InVzZXIyIn0.abcdefg';
    const String malformedToken = 'this.is.not.a.jwt';

    test('should decode payload for valid token', () {
      final payload = decodeTokenPayload(validToken);
      expect(payload, isNotNull);
      expect(payload?['exp'], equals(2672531199));
      expect(payload?['sub'], equals('user2'));
    });

    test('should return null for invalid token', () {
      expect(decodeTokenPayload(malformedToken), isNull);
      expect(decodeTokenPayload(null), isNull);
      expect(decodeTokenPayload(''), isNull);
    });
  });
}
