import 'package:flutter_test/flutter_test.dart';
import 'package:soccer_frontend/core/utils/auth_constants.dart';

void main() {
  group('AuthConstants', () {
    test('redirectUrl contains correct components in development mode', () {
      // This test assumes we're running in development mode
      final redirectUrl = AuthConstants.redirectUrl;
      expect(redirectUrl, contains('${AuthConstants.localDevPort}'));
      expect(redirectUrl, endsWith('/auth/callback'));
    });

    group('redirectUrl', () {
      test('returns localhost URL for web platform in development', () {
        final url = AuthConstants.getRedirectUrlForPlatform(
          isProduction: false,
          isWeb: true,
          isIOS: false,
          isAndroid: false,
        );

        expect(url, startsWith('http://localhost'));
        expect(url, endsWith('/auth/callback'));
      });

      test('returns custom scheme URL for iOS platform in development', () {
        final url = AuthConstants.getRedirectUrlForPlatform(
          isProduction: false,
          isWeb: false,
          isIOS: true,
          isAndroid: false,
        );

        expect(url, startsWith(AuthConstants.urlScheme));
        expect(url, contains('://'));
        expect(url, endsWith('/auth/callback'));
      });

      test('returns custom scheme URL for Android platform in development', () {
        final url = AuthConstants.getRedirectUrlForPlatform(
          isProduction: false,
          isWeb: false,
          isIOS: false,
          isAndroid: true,
        );

        expect(url, startsWith(AuthConstants.urlScheme));
        expect(url, contains('://'));
        expect(url, endsWith('/auth/callback'));
      });

      test('returns base URL for production regardless of platform', () {
        final webUrl = AuthConstants.getRedirectUrlForPlatform(
          isProduction: true,
          isWeb: true,
          isIOS: false,
          isAndroid: false,
        );

        final iosUrl = AuthConstants.getRedirectUrlForPlatform(
          isProduction: true,
          isWeb: false,
          isIOS: true,
          isAndroid: false,
        );

        final androidUrl = AuthConstants.getRedirectUrlForPlatform(
          isProduction: true,
          isWeb: false,
          isIOS: false,
          isAndroid: true,
        );

        // All should be the same in production
        expect(webUrl, equals(iosUrl));
        expect(webUrl, equals(androidUrl));
        expect(webUrl, endsWith('/auth/callback'));
      });
    });
  });
}
