#!/bin/bash
# Script to run all authentication-related tests

echo "Running Authentication Component Tests..."
echo "----------------------------------------"

# Run SecureStorageAdapter tests
echo "Testing SecureStorageAdapter..."
flutter test test/core/utils/secure_storage_adapter_test.dart

# Run AuthService tests
echo "Testing AuthService..."
flutter test test/core/services/auth_service_test.dart

# Run AuthBloc tests
echo "Testing AuthBloc..."
flutter test test/features/auth/blocs/auth_bloc_test.dart

# Run AuthBloc SupaUI tests
echo "Testing AuthBloc SupaUI events..."
flutter test test/features/auth/blocs/auth_bloc_supa_ui_test.dart

# Run AuthBloc Callback tests - temporarily disabled due to _AuthChangeEventOccurred event not being accessible
# echo "Testing AuthBloc Callback handling..."
# flutter test test/features/auth/blocs/auth_bloc_callback_test.dart

# Run AuthScreen tests
echo "Testing AuthScreen..."
flutter test test/features/auth/screens/auth_screen_test.dart

echo "----------------------------------------"
echo "All authentication tests completed!"
