# Testing the Authentication Flow

This document provides guidelines for testing the authentication flow in the Tournament Scheduler app, with a focus on email confirmation and password recovery.

## Unit Tests

The authentication flow is tested using several unit tests:

1. **CallbackHandler Tests**: Tests the logic for processing callback URIs and determining the appropriate action.
2. **AuthBloc Tests**: Tests the AuthBloc's reaction to authentication state changes, particularly for email confirmation and password recovery.
3. **AuthConstants Tests**: Tests the URL generation logic for different platforms.

### Running the Tests

To run all the tests:

```bash
cd flutter_frontend
flutter test
```

To run specific tests:

```bash
flutter test test/features/auth/utils/callback_handler_test.dart
flutter test test/features/auth/blocs/auth_bloc_callback_test.dart
flutter test test/core/utils/auth_constants_test.dart
```

## Manual Testing

### Email Confirmation Flow

1. **Setup**:
   - Ensure the app is running with the correct port: `flutter run -d chrome --web-port 8114`
   - For mobile testing, ensure the app is installed on your device

2. **Testing Steps**:
   - Sign up with a new account
   - Check the email in Mailtrap
   - Ensure the app is still running
   - Click the confirmation link in the email
   - Verify that the app navigates to the confirmation success screen

3. **Expected Results**:
   - The app should handle the callback and navigate to the confirmation success screen
   - The user's email should be marked as confirmed in Supabase
   - The user should be able to log in

### Password Recovery Flow

1. **Setup**:
   - Ensure the app is running with the correct port: `flutter run -d chrome --web-port 8114`
   - For mobile testing, ensure the app is installed on your device

2. **Testing Steps**:
   - Go to the login screen
   - Click "Forgot Password"
   - Enter your email address
   - Check the email in Mailtrap
   - Ensure the app is still running
   - Click the password reset link in the email
   - Verify that the app navigates to the reset password screen
   - Enter a new password
   - Verify that the password is updated and you can log in with the new password

3. **Expected Results**:
   - The app should handle the callback and navigate to the reset password screen
   - The user should be able to set a new password
   - The user should be able to log in with the new password

## Troubleshooting

### Common Issues

1. **Connection Refused Error**:
   - Make sure the app is running at `http://localhost:8114` before clicking the link
   - Check that you're running the app with `--web-port 8114`

2. **Deep Link Not Working on Mobile**:
   - Ensure the app is installed on your device
   - Check that the custom URL scheme is registered correctly
   - Try opening the app first and then clicking the link

3. **Callback Not Handled**:
   - Check the logs for any errors
   - Ensure the app router has a route for `/auth/callback` that points to the `CallbackScreen`
   - Verify that the Supabase redirect URL is configured correctly

### Debugging Tools

1. **Logging**:
   - The app uses the `logging` package to log important events
   - Check the logs for any errors or warnings

2. **Supabase Dashboard**:
   - Check the Supabase dashboard for any authentication errors
   - Verify that the redirect URL is configured correctly

3. **Mailtrap**:
   - Check the email content in Mailtrap
   - Verify that the confirmation link is correct

## Production Considerations

For production:

1. **Update Site URL**: Update the Supabase site URL to your production domain.
2. **Update Redirect URLs**: Update the allowed redirect URLs in Supabase to include your production callback URL.
3. **Update Email Templates**: Remove the development-specific instructions from the email templates.
4. **Implement Universal Links / App Links**: For a better user experience on mobile, implement Universal Links (iOS) and App Links (Android).
