# Email Confirmation Workflow

This document explains how the email confirmation flow works in the Tournament Scheduler app and provides guidelines for testing during development.

## How It Works

1. **User Signs Up**: When a user signs up, the app sends a request to Supabase to create a new user.
2. **Verification Email**: Supabase sends a verification email to the user with a confirmation link.
3. **User Clicks Link**: When the user clicks the link, Supabase verifies the token and redirects to the callback URL.
4. **App Processes Callback**: The app processes the callback and updates the authentication state.
5. **Success Screen**: The user is shown a confirmation success screen.

## Development Workflow

### Web Testing

When testing the email confirmation flow in a web browser:

1. **Run the App**: Make sure the app is running with the correct port:
   ```
   flutter run -d chrome --web-port 8114
   ```

2. **Keep the App Running**: Do not stop the app before clicking the confirmation link in the email.

3. **Click the Link**: When you click the confirmation link in the email, the browser will navigate to `http://localhost:8114/auth/callback`.

4. **Connection Refused Error**: If you get a "Connection Refused" error, it means the app is not running at `http://localhost:8114`. Make sure the app is running before clicking the link.

### Mobile Testing

When testing on a mobile device:

1. **Install the App**: Make sure the app is installed on your device.

2. **Custom URL Scheme**: The app uses the custom URL scheme `tournamentscheduler://` to handle deep links.

3. **Click the Link**: When you click the confirmation link in the email, the device should open the app automatically.

4. **Open App First**: If the app doesn't open automatically, try opening the app first and then clicking the link.

## Troubleshooting

### Web Issues

- **Connection Refused**: Make sure the app is running at `http://localhost:8114` before clicking the link.
- **Wrong Port**: Check that you're running the app with `--web-port 8114`.
- **Callback Not Handled**: Ensure the app router has a route for `/auth/callback` that points to the `CallbackScreen`.

### Mobile Issues

- **App Not Opening**: Make sure the app is installed and the custom URL scheme is registered correctly.
- **Deep Link Not Handled**: Check the deep link handling in the app.

## Production Considerations

For production:

1. **Update Site URL**: Update the Supabase site URL to your production domain.
2. **Update Redirect URLs**: Update the allowed redirect URLs in Supabase to include your production callback URL.
3. **Update Email Templates**: Remove the development-specific instructions from the email templates.
4. **Implement Universal Links / App Links**: For a better user experience on mobile, implement Universal Links (iOS) and App Links (Android).
