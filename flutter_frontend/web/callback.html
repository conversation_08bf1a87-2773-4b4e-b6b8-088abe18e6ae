<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Verification</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            background-color: #f5f5f5;
        }
        .container {
            text-align: center;
            padding: 2rem;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            max-width: 500px;
        }
        .icon {
            font-size: 64px;
            color: #2196F3;
            margin-bottom: 1rem;
        }
        h1 {
            color: #333;
            margin-bottom: 1rem;
        }
        p {
            color: #666;
            line-height: 1.5;
            margin-bottom: 1.5rem;
        }
        .button {
            background-color: #2196F3;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 4px;
            cursor: pointer;
            font-size: 1rem;
            text-decoration: none;
            display: inline-block;
        }
        .button:hover {
            background-color: #1976D2;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon">✓</div>
        <h1>Email Verification</h1>
        <p>Your email has been verified successfully! Please open the Tournament Scheduler app to continue.</p>
        <p>If the app is already open, you should be automatically redirected.</p>
        <a href="#" id="goToAppBtn" class="button">Go to App</a>
    </div>

    <script>
        // Extract token and type from URL
        const urlParams = new URLSearchParams(window.location.search);
        const token = urlParams.get('token');
        const type = urlParams.get('type');

        // Set up the button click handler
        document.getElementById('goToAppBtn').addEventListener('click', function(e) {
            e.preventDefault();
            // Redirect to the auth callback route with the token and type
            if (token && type) {
                window.location.href = `/auth/callback?token=${token}&type=${type}`;
            } else {
                // Fallback if no token/type (shouldn't happen in normal flow)
                window.location.href = '/';
            }
        });

        // Auto-redirect after a short delay if we have token and type
        if (token && type) {
            // Try to redirect to the app after 1.5 seconds
            setTimeout(function() {
                window.location.href = `/auth/callback?token=${token}&type=${type}`;
            }, 1500);
        }
    </script>
</body>
</html>
