import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mocktail/mocktail.dart';
import 'package:soccer_frontend/main.dart' as app; // Assuming main.dart can be run
import 'package:soccer_frontend/data/models/club.dart';
import 'package:soccer_frontend/data/models/field.dart';
import 'package:soccer_frontend/data/models/tournament.dart';
import 'package:soccer_frontend/data/models/venue.dart';
import 'package:soccer_frontend/features/tournament/blocs/create_tournament/create_tournament_bloc.dart';
import 'package:soccer_frontend/features/tournament/blocs/create_tournament/create_tournament_event.dart';
import 'package:soccer_frontend/features/tournament/blocs/create_tournament/create_tournament_state.dart';
import 'package:soccer_frontend/features/tournament/screens/create_tournament/create_tournament_step_1_screen.dart';
import 'package:soccer_frontend/features/tournament/screens/create_tournament/create_tournament_step_2_screen.dart';
import 'package:soccer_frontend/features/tournament/screens/create_tournament/create_tournament_step_2_venues_screen.dart' as old_step2;
import 'package:soccer_frontend/features/tournament/screens/create_tournament/create_tournament_step_3_fields_screen.dart' as old_step3;
import 'package:soccer_frontend/features/tournament/services/tournament_api_service.dart';
import 'package:soccer_frontend/core/services/supabase_service.dart'; // For TournamentApiService dependency
import 'package:soccer_frontend/core/router/app_router.dart'; // For navigation
import 'package:go_router/go_router.dart';
import 'package:soccer_frontend/core/services/auth_service.dart'; // For router dependency
import 'package:soccer_frontend/features/auth/blocs/auth_bloc.dart'; // For router dependency

// Mocks
class MockCreateTournamentBloc extends Mock implements CreateTournamentBloc {}
class MockTournamentApiService extends Mock implements TournamentApiService {}
class MockAuthBloc extends Mock implements AuthBloc {} // For AppRouter
class MockAuthService extends Mock implements AuthService {} // For AppRouter & CreateTournamentBloc provider
class MockSupabaseService extends Mock implements SupabaseService {} // For CreateTournamentBloc provider
class MockUser {
  final String id;
  MockUser({required this.id});
}

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  late MockCreateTournamentBloc mockCreateTournamentBloc;
  late MockTournamentApiService mockTournamentApiService;

  // Test Data
  final mockUser = MockUser(id: 'user-test-id');
  final initialTournament = Tournament(
      name: '', startDate: DateTime(2024,1,1), endDate: DateTime(2024,1,2), sportType: '', status: 'planning');

  final club1 = const Club(id: 'club1', name: 'FC Elite');
  final venue1 = const Venue(id: 'venue1', name: 'City Stadium', address: '123 City Rd');
  final field1_1 = const Field(id: 'field1_1', venueId: 'venue1', nameOrNumber: 'Pitch 1', fieldTypeName: 'Grass');

  setUpAll(() {
    // Fallback registration if needed by mocktail for deep object comparison or specific matchers
    // registerFallbackValue(CreateTournamentInitial()); 
    // registerFallbackValue(InitializeTournamentCreation());
    // registerFallbackValue(Tournament(name: '', startDate: DateTime.now(), endDate: DateTime.now(), sportType: '', status: ''));
  });

  setUp(() {
    mockCreateTournamentBloc = MockCreateTournamentBloc();
    mockTournamentApiService = MockTournamentApiService();

    // Default stub for the bloc state
    when(() => mockCreateTournamentBloc.state).thenReturn(
        CreateTournamentStep1InProgress(tournament: initialTournament, affiliationStatusLoading: false));
    // Default stub for the bloc stream
    when(() => mockCreateTournamentBloc.stream).thenAnswer((_) => const Stream.empty());
  });

  Future<void> pumpAppWithBloc(WidgetTester tester, Widget screen) async {
    // In a real integration test, you'd launch your main app (app.main())
    // with mocks injected, e.g., via overriding providers or a test main.
    // For this scoped test, we'll wrap the screen directly.
    // This simplifies things but doesn't test the full app navigation stack from root.

    // Setup a mock GoRouter for screen-level navigation testing if needed
    final mockGoRouter = GoRouter(routes: [
      GoRoute(path: '/', builder: (_, __) => screen),
      GoRoute(path: '/create-tournament/step1', builder: (_, __) => screen), // Adjust as needed
      GoRoute(path: '/create-tournament/step2', builder: (_, __) => const CreateTournamentStep2Screen()),
      GoRoute(path: '/create-tournament/step3', builder: (_, __) => const old_step2.CreateTournamentStep2VenuesScreen()),
      GoRoute(path: '/create-tournament/step4', builder: (_, __) => const old_step3.CreateTournamentStep3FieldsScreen()),
    ]);

    await tester.pumpWidget(
      MultiBlocProvider(
        providers: [
          // Provide the mock CreateTournamentBloc here.
          // This assumes CreateTournamentBloc is provided by a ShellRoute in the real app.
          // For this test, we provide it directly above the screen being tested.
          BlocProvider<CreateTournamentBloc>.value(value: mockCreateTournamentBloc),
          // Provide mock TournamentApiService for the BLoC to use if it were real
          // or for the widget if it directly accessed it (not typical for BLoC pattern).
          // This mock is primarily for the final verification step.
          Provider<TournamentApiService>.value(value: mockTournamentApiService),
        ],
        child: MaterialApp.router(
          routerConfig: mockGoRouter,
        ),
      ),
    );
  }


  testWidgets('Full tournament creation flow with affiliated club, venue, and field selection', (WidgetTester tester) async {
    // --- STEP 1: Affiliation and Basic Details ---
    // Initial state: Step 1, ready to fetch affiliation
    when(() => mockCreateTournamentBloc.state).thenReturn(
      CreateTournamentStep1InProgress(tournament: initialTournament, affiliationStatusLoading: true)
    );
    await pumpAppWithBloc(tester, const CreateTournamentStep1Screen());
    await tester.pumpAndSettle(); // For initial loading

    // Simulate affiliation loaded: isAffiliated = true
    when(() => mockCreateTournamentBloc.state).thenReturn(
      CreateTournamentStep1InProgress(tournament: initialTournament, isAffiliated: true, affiliationStatusLoading: false)
    );
    await tester.pumpAndSettle();

    // Tap "Affiliated Club" radio
    await tester.tap(find.byWidgetPredicate((w) => w is RadioListTile && (w.title as Text).data == 'As an Affiliated Club'));
    await tester.pumpAndSettle();
    verify(() => mockCreateTournamentBloc.add(const TournamentCreationOptionChanged("affiliated"))).called(1);

    // Simulate BLoC state update: affiliated option selected, loading clubs
    when(() => mockCreateTournamentBloc.state).thenReturn(
      CreateTournamentStep1InProgress(
          tournament: initialTournament, isAffiliated: true, selectedCreationOption: "affiliated", affiliatedClubsLoading: true)
    );
    await tester.pumpAndSettle(); // Show club loading indicator

    // Simulate BLoC state update: clubs loaded
    when(() => mockCreateTournamentBloc.state).thenReturn(
      CreateTournamentStep1InProgress(
          tournament: initialTournament, isAffiliated: true, selectedCreationOption: "affiliated", affiliatedClubs: [club1], affiliatedClubsLoading: false)
    );
    await tester.pumpAndSettle();

    // Select a club
    await tester.tap(find.byType(DropdownButtonFormField<String>));
    await tester.pumpAndSettle();
    await tester.tap(find.text(club1.name).last);
    await tester.pumpAndSettle();
    verify(() => mockCreateTournamentBloc.add(AffiliatedClubSelected(club1.id))).called(1);

    // Update BLoC state for selected club
    final tournamentAfterClub = initialTournament.copyWith(managingClubId: club1.id);
    when(() => mockCreateTournamentBloc.state).thenReturn(
      CreateTournamentStep1InProgress(
          tournament: tournamentAfterClub, // Name, sportType still empty here
          isAffiliated: true, selectedCreationOption: "affiliated", affiliatedClubs: [club1], selectedAffiliatedClubId: club1.id)
    );

    // Enter tournament details
    final tournamentName = 'Champions Cup 2024';
    final sportType = 'Football';
    await tester.enterText(find.widgetWithText(TextFormField, 'Tournament Name'), tournamentName);
    await tester.enterText(find.widgetWithText(TextFormField, 'Sport Type'), sportType);
    await tester.pumpAndSettle();

    // Update BLoC state for entered details (assuming onChanged events update BLoC)
    final tournamentWithDetails = tournamentAfterClub.copyWith(name: tournamentName, sportType: sportType);
     when(() => mockCreateTournamentBloc.state).thenReturn(
      CreateTournamentStep1InProgress(
          tournament: tournamentWithDetails,
          isAffiliated: true, selectedCreationOption: "affiliated", affiliatedClubs: [club1], selectedAffiliatedClubId: club1.id)
    );
    await tester.pumpAndSettle();

    // Tap "Next" on Step 1
    // This should trigger Step1Completed event with tournamentWithDetails
    // The BLoC listener in Step 1 screen would then navigate to /create-tournament/step2
    // For this test, we directly simulate the state change that leads to navigation.

    // Stub the Add(Step1Completed)
    // No actual navigation check here, assume BLoC handles state for next screen
    await tester.tap(find.text('Next'));
    await tester.pumpAndSettle(); // Process tap

    final expectedTournamentAfterStep1 = tournamentWithDetails; // Dates are tricky, assume they are handled
    verify(() => mockCreateTournamentBloc.add(Step1Completed(expectedTournamentAfterStep1))).called(1);


    // --- STEP 2: Venue and Field Selection ---
    // Simulate BLoC transitioning to VenueFieldSelectionStep and loading venues
    when(() => mockCreateTournamentBloc.state).thenReturn(
      CreateTournamentVenueFieldSelectionStep(
          tournament: expectedTournamentAfterStep1, selectedAffiliatedClubId: club1.id, venuesLoading: true)
    );
    // Manually navigate for the test, or ensure test setup handles GoRouter navigation based on BLoC state
    // For this test, we'll assume navigation happened and pump the new screen with the bloc.
    await pumpAppWithBloc(tester, const CreateTournamentStep2Screen());
    await tester.pumpAndSettle(); // For venue loading

    // Simulate venues loaded
    when(() => mockCreateTournamentBloc.state).thenReturn(
      CreateTournamentVenueFieldSelectionStep(
          tournament: expectedTournamentAfterStep1, selectedAffiliatedClubId: club1.id, availableVenues: [venue1], venuesLoading: false)
    );
    await tester.pumpAndSettle();

    // Select venue1
    await tester.tap(find.byKey(Key('venue_checkbox_${venue1.id}')));
    await tester.pumpAndSettle();
    verify(() => mockCreateTournamentBloc.add(ToggleVenueSelection(venue1.id!))).called(1);

    // Simulate BLoC state: venue1 selected, loading its fields
    when(() => mockCreateTournamentBloc.state).thenReturn(
      CreateTournamentVenueFieldSelectionStep(
          tournament: expectedTournamentAfterStep1, selectedAffiliatedClubId: club1.id, 
          availableVenues: [venue1], selectedVenueIds: {venue1.id!}, 
          fieldsLoadingByVenueId: {venue1.id!: true})
    );
    await tester.pumpAndSettle(); // For field loading indicator

    // Simulate BLoC state: fields for venue1 loaded
    when(() => mockCreateTournamentBloc.state).thenReturn(
      CreateTournamentVenueFieldSelectionStep(
          tournament: expectedTournamentAfterStep1, selectedAffiliatedClubId: club1.id,
          availableVenues: [venue1], selectedVenueIds: {venue1.id!},
          availableFieldsByVenueId: {venue1.id!: [field1_1]}, fieldsLoadingByVenueId: {venue1.id!: false})
    );
    await tester.pumpAndSettle();

    // Select field1_1
    await tester.tap(find.byKey(Key('field_checkbox_${field1_1.id}')));
    await tester.pumpAndSettle();
    verify(() => mockCreateTournamentBloc.add(ToggleFieldSelection(field1_1.id!))).called(1);

    // Update BLoC state for selected field
    when(() => mockCreateTournamentBloc.state).thenReturn(
      CreateTournamentVenueFieldSelectionStep(
          tournament: expectedTournamentAfterStep1, selectedAffiliatedClubId: club1.id,
          availableVenues: [venue1], selectedVenueIds: {venue1.id!},
          availableFieldsByVenueId: {venue1.id!: [field1_1]}, selectedFieldIds: {field1_1.id!})
    );
    await tester.pumpAndSettle();

    // Tap "Next" on Step 2
    await tester.tap(find.byKey(const Key('next_button')));
    await tester.pumpAndSettle();
    verify(() => mockCreateTournamentBloc.add(ProceedToNextStepFromVenueFieldSelection(
        selectedVenues: [venue1],
        selectedFieldsByVenueId: {venue1.id!: [field1_1]}
    ))).called(1);
  });
}
