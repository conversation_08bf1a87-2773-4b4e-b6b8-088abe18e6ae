# Soccer Tournament Scheduler - Architecture Guide

## Project Structure

This project follows a feature-first architecture for better scalability and maintainability.

### Directory Structure

```
/lib/
├── core/                # App-wide, non-feature-specific code
│   ├── services/        # Core services (Auth, DB connection, Connectivity)
│   ├── utils/           # Global utilities (Constants, Validators, ErrorHandler)
│   ├── router/          # Navigation logic (GoRouter setup)
│   └── theme/           # App theme data
│
├── data/                # Data layer
│   ├── models/          # Data structures (UserProfile, Tournament, etc.)
│   └── repositories/    # Abstract interfaces for data sources (Optional)
│
├── features/            # Feature-specific modules
│   ├── auth/
│   │   ├── blocs/       # AuthBloc, Event, State
│   │   ├── screens/     # AuthScreen, ClubRegistrationScreen, etc.
│   │   └── widgets/     # Widgets used ONLY within the auth feature
│   ├── profile/
│   │   ├── blocs/       # ProfileBloc, Event, State
│   │   ├── screens/     # ProfileScreen, EditProfileScreen
│   │   └── widgets/
│   ├── tournament/
│   │   ├── blocs/       # TournamentBloc, Event, State
│   │   ├── screens/     # TournamentsScreen, TournamentDetailScreen
│   │   └── widgets/
│   └── ... (other features like team, match, scheduling, bracket, referee)
│
├── presentation/        # Shared UI components
│   └── widgets/         # Common widgets (AppBar, CustomCard, LoadingIndicator)
│
├── main.dart            # App entry point, Provider/Bloc setup
└── app.dart             # Root MaterialApp widget, Router setup
```

### Key Components

1. **Core Services**:
   - `AuthService`: Handles authentication with Supabase
   - `SupabaseService`: Manages database operations
   - `ConnectivityService`: Monitors network connectivity
   - `DatabaseService`: Generic database operations

2. **Data Models**:
   - `UserProfile`: User information model
   - `ClubAffiliation`: Club membership model

3. **Feature BLoCs**:
   - `AuthBloc`: Authentication state management
   - `ProfileBloc`: User profile management
   - `TournamentBloc`: Tournament management
   - `TeamBloc`: Team management
   - `MatchBloc`: Match management
   - `SchedulingBloc`: Tournament scheduling
   - `BracketBloc`: Bracket generation
   - `RefereeBloc`: Referee management

4. **Routing**:
   - Uses GoRouter for navigation
   - Route guards based on authentication state

## Architecture Decisions

### Feature-First vs. Layer-First

We chose a feature-first architecture over a layer-first approach for the following reasons:

- **Better Scalability**: As the project grows, features remain isolated and manageable
- **Clear Boundaries**: Each feature has its own directory with all related components
- **Improved Maintainability**: Changes to one feature are less likely to affect others
- **Team Collaboration**: Different developers can work on separate features with fewer conflicts

### State Management

We use the BLoC pattern for state management because:

- **Separation of Concerns**: Business logic is separated from UI
- **Testability**: BLoCs are easy to test in isolation
- **Predictable State Changes**: Events trigger state changes in a predictable manner
- **Reactive Programming**: Stream-based approach works well with Flutter's reactive UI

### Authentication

Authentication is handled by Supabase, which provides:

- Email/password authentication
- Social login (optional)
- JWT-based sessions
- Row-level security for database access

## Development Guidelines

1. **Feature Development**:
   - Create new features in the `features/` directory
   - Keep feature-specific code within the feature directory
   - Use core services for shared functionality

2. **State Management**:
   - Create a BLoC for each feature
   - Define clear events and states
   - Keep BLoCs focused on a single responsibility

3. **UI Components**:
   - Feature-specific widgets go in the feature's `widgets/` directory
   - Shared widgets go in `presentation/widgets/`
   - Follow Material Design guidelines

4. **Testing**:
   - Write unit tests for BLoCs and services
   - Write widget tests for UI components
   - Write integration tests for critical user flows

## Migrating from Layer-First to Feature-First

If you're working with existing code that follows a layer-first approach, here's how to migrate:

1. Create the new directory structure
2. Move core services to `core/services/`
3. Move models to `data/models/`
4. Move utilities to `core/utils/`
5. Create feature directories and move related BLoCs, screens, and widgets
6. Update imports throughout the codebase
7. Test thoroughly after migration

## Best Practices

1. **Barrel Files**: Consider using barrel files (index.dart) in each directory to export all files, making imports cleaner
2. **Dependency Injection**: Use Provider or GetIt for dependency injection
3. **Error Handling**: Implement consistent error handling across the app
4. **Logging**: Use a logging framework for debugging and monitoring
5. **Documentation**: Document architecture decisions and complex features
