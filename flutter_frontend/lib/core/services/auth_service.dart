// lib/core/services/auth_service.dart
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:logging/logging.dart';
import 'dart:async';
import 'dart:convert'; // For jsonEncode/jsonDecode
import 'package:http/http.dart' as http; // HTTP package
import 'package:flutter_dotenv/flutter_dotenv.dart'; // For environment variables

// Import SupabaseService if needed for profile methods, or keep profile logic here
import '../interfaces/auth_service_interface.dart';
import '../interfaces/supabase_service_interface.dart';
import '../../data/models/user_profile.dart';
import '../../data/models/club_affiliation.dart'; // Added import
import '../utils/auth_constants.dart';
import '../utils/app_exception.dart'; // Use custom exception
import '../utils/jwt_validation_util.dart'; // Import JWT validation utilities

class AuthService implements AuthServiceInterface {
  // Get Supabase Auth client directly
  final GoTrueClient _auth;
  // Get Supabase client for RPCs/DB calls if needed
  final SupabaseClient _client;
  final Logger _log = Logger('AuthService');
  // Inject SupabaseService if profile/other DB ops are delegated
  final SupabaseServiceInterface _supabaseService;

  AuthService(this._client, this._supabaseService) : _auth = _client.auth {
    _log.info('AuthService initialized');
    // Optional: Log initial validity non-blockingly
    Future.microtask(() => logInitialSessionValidity());
  }

  // Expose the auth state stream
  @override
  Stream<AuthState> get onAuthStateChange => _auth.onAuthStateChange;

  @override
  User? get currentUser => _auth.currentUser;

  @override
  Session? get currentSession => _auth.currentSession;

  @override
  bool get isEmailConfirmationRequired {
    // Ideally, read from Supabase config if possible, otherwise assume based on setup
    _log.fine('Checking if email confirmation is required (assuming true)');
    return true;
  }

  @override
  Future<bool> isLoggedIn() async {
    // Use currentSession for a quick check
    return _auth.currentSession != null;
  }

  @override
  bool isCurrentTokenValid() {
    final session = _auth.currentSession;
    if (session == null) return false;

    final token = session.accessToken;
    if (token.isEmpty) return false;

    // Use the JWT validation utility to check if token is expired
    return !isTokenExpired(token);
  }

  @override
  Future<void> logInitialSessionValidity() async {
    final session = _auth.currentSession;
    if (session == null) {
      _log.info('Initial session check: No session found');
      return;
    }

    final token = session.accessToken;
    final isValid = !isTokenExpired(token);
    final userId = session.user.id;
    final expiryDate = getTokenExpirationDate(token);

    _log.info('Initial session validity check for user $userId:');
    _log.info('- Token valid: $isValid');
    if (expiryDate != null) {
      _log.info('- Token expires: $expiryDate');
    }
  }

  // Sign In
  @override
  Future<AuthResponse> signIn({required String email, required String password}) async {
    _log.info('AuthService: Signing in $email');
    try {
      final response = await _auth.signInWithPassword(email: email, password: password);
      _log.info('AuthService: Sign in successful.');
      return response;
    } on AuthException catch (e) {
      _log.warning('AuthService: Sign in failed - ${e.message}');
      throw AppException.fromSupabaseError(e); // Convert to AppException
    } catch (e, s) {
      _log.severe('AuthService: Unexpected sign in error', e, s);
      throw AppException('An unexpected error occurred during login.');
    }
  }

  // Individual Sign Up
  @override
  Future<AuthResponse> signUp({
      required String email, required String password,
      required String firstName, required String lastName,
      required String role}) async {
     _log.info('AuthService: Signing up $email with role $role');
     final redirectUrl = AuthConstants.redirectUrl;
      try {
       // Profile creation relies on the handle_new_user trigger
       final response = await _auth.signUp(
         email: email,
         password: password,
         emailRedirectTo: redirectUrl,
         data: { // Pass metadata for trigger
           'first_name': firstName, 'last_name': lastName,
           'user_role': role, 'full_name': '$firstName $lastName'.trim()
         },
       );
        _log.info('AuthService: Sign up call successful for $email.');
       return response;
     } on AuthException catch (e) {
       _log.warning('AuthService: Sign up failed: ${e.message}');
       throw AppException.fromSupabaseError(e);
     } catch (e, s) {
        _log.severe('AuthService: Unexpected sign up error', e, s);
        throw AppException('An unexpected error occurred during sign up.');
    }
  }

  // Club Admin Sign Up (Calls RPC)
  @override
  Future<void> registerClubAdmin({
      required String clubName, required String adminFirstName,
      required String adminLastName, required String adminEmail,
      required String adminPassword}) async {
    _log.info('AuthService: Calling backend API for club admin registration for $adminEmail');

    // Determine the API base URL. Default to localhost:8000 if not set in .env
    // Ensure your .env file has API_BASE_URL=http://your_backend_host:port
    final String apiBaseUrl = dotenv.env['API_BASE_URL'] ?? 'http://localhost:8000';
    final Uri apiUrl = Uri.parse('$apiBaseUrl/api/v1/register-club-admin');

    final requestBody = {
      'club_name': clubName,
      'admin_first_name': adminFirstName,
      'admin_last_name': adminLastName,
      'admin_email': adminEmail,
      'admin_password': adminPassword,
    };

    try {
      final response = await http.post(
        apiUrl,
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(requestBody),
      );

      if (response.statusCode >= 200 && response.statusCode < 300) {
        // Successfully created club admin and club
        final responseData = jsonDecode(response.body);
        _log.info('AuthService: Club admin registration successful via backend API. User ID: ${responseData['user_id']}');
        // The method is Future<void>, so no specific data needs to be returned.
        // The UI should handle navigation or feedback based on this success.
      } else {
        // Handle error responses from the backend
        String errorMessage = 'Club admin registration failed.';
        try {
          final errorData = jsonDecode(response.body);
          // FastAPI endpoint returns StatusResponse model for errors, which has a 'detail' field
          // that might be a string or an object with 'status' and 'message'.
          if (errorData['detail'] is Map && errorData['detail']['message'] != null) {
            errorMessage = errorData['detail']['message'];
          } else if (errorData['detail'] is String) {
            errorMessage = errorData['detail'];
          } else if (errorData['message'] != null) { // Fallback if structure is just { "message": "..." }
            errorMessage = errorData['message'];
          }
        } catch (e) {
          // If error response is not JSON or doesn't match expected structure
          _log.warning('Could not parse error response from backend: ${response.body}');
          errorMessage = 'Club admin registration failed with status code: ${response.statusCode}.';
        }
        _log.severe('AuthService: Club admin registration via backend API failed - $errorMessage');
        throw AppException(errorMessage);
      }
    } on http.ClientException catch (e, s) {
      _log.severe('AuthService: Network error during club admin registration via backend API', e, s);
      throw AppException('Network error: Could not connect to the server. Please try again later.');
    } catch (e, s) {
      _log.severe('AuthService: Unexpected error during club admin registration via backend API', e, s);
      throw AppException('An unexpected error occurred during club registration.');
    }
  }

  // Password Reset Request
  @override
  Future<void> requestPasswordReset(String email) async {
    // For PKCE flow, redirectTo should be the URL of your "update password" screen.
    // The Supabase SDK and backend handle the token_hash verification from the email link.
    // The email template itself should point to something like:
    // {{ .SiteURL }}/auth/confirm?token_hash={{ .TokenHash }}&type=recovery&next=/auth/reset-password
    // And then Supabase.initialize (with authFlowType: AuthFlowType.pkce) handles it.
    // The `redirectTo` here tells Supabase where the user should land *after* a successful OTP verification via the link.
    // This should typically be your app's callback URL that can handle the token.
    // Supabase will append #access_token=...&type=recovery etc. to this URL.
    // Your email template then constructs the link the user clicks, e.g.,
    // {{ .SiteURL }}/auth/confirm?token_hash={{ .TokenHash }}&type=recovery&next=/auth/reset-password
    final String redirectUrl = AuthConstants.redirectUrl; // e.g. http://localhost:8114/auth/callback

    _log.info('[AuthService] 🔑 resetPasswordForEmail → email=$email, new redirectTo=$redirectUrl (PKCE flow)');

    // ***** Existing PRINT STATEMENT - can be kept or removed *****
    // print('>>> AuthService sending reset request with PKCE redirectTo: $redirectUrl'); 
    // ***************************************************************

    try {
      _log.fine('[AuthService] Making Supabase API call to resetPasswordForEmail...');
      // print('>>> CALLING _auth.resetPasswordForEmail with PKCE redirectTo: $redirectUrl'); // Can be kept or removed
      // For PKCE, the redirectTo in resetPasswordForEmail is where the user lands after clicking the link AND Supabase verifies the token.
      await _auth.resetPasswordForEmail(email, redirectTo: redirectUrl);
      _log.info('[AuthService] ✔ resetPasswordForEmail succeeded for $email (PKCE flow)');
    } on AuthException catch (e) {
      _log.warning('[AuthService] ❌ Password reset failed with AuthException: ${e.message}');
      _log.fine('[AuthService] AuthException details: ${e.statusCode}, ${e.runtimeType}');
      throw AppException.fromSupabaseError(e);
    } catch (e, s) {
      _log.severe('[AuthService] ❌ Unexpected error in requestPasswordReset', e, s);
      throw AppException('An unexpected error occurred requesting password reset.');
    }
  }

  // Sign Out
  @override
  Future<void> signOut() async {
    _log.info('AuthService: Signing out.');
    try {
      await _auth.signOut();
      _log.info('AuthService: Sign out successful.');
    } catch (e, s) {
      _log.severe('AuthService: Sign out error', e, s);
      // Don't usually need to throw, listener handles state
    }
  }

  // --- Profile Methods (Delegated or Implemented Here) ---

  // Option 1: Delegate to SupabaseService
  @override
  Future<UserProfile?> getUserProfile(String userId) async {
     try {
        // Assuming SupabaseService has getRawUserProfile which returns Map or null
        final rawProfile = await _supabaseService.getRawUserProfile(userId);
        return rawProfile == null ? null : UserProfile.fromJson(rawProfile);
     } catch (e) {
        _log.severe('AuthService: Failed to get user profile via SupabaseService: $e');
         return null; // Return null on error for BLoC handling
     }
  }

  // Option 2: Keep direct call here (as shown in previous version)
  /*
  Future<UserProfile?> getUserProfile(String userId) async {
     _log.fine('AuthService: Fetching profile for user: $userId');
     try {
        final response = await _client // Use Supabase client directly
            .from('profiles')
            .select(...) // Select required columns
            .eq('id', userId)
            .maybeSingle();
         return response == null ? null : UserProfile.fromJson(response);
     } catch (e, s) { ... }
  }
  */

  // Update Password (used in password recovery flow)
  @override
  Future<void> updatePassword(String newPassword) async {
     _log.info('AuthService: Updating password (recovery flow).');
     try {
        await _auth.updateUser(UserAttributes(password: newPassword));
        _log.info('AuthService: Password updated successfully (recovery flow).');
     } on AuthException catch (e) {
        _log.warning('AuthService: Password update failed (recovery flow): ${e.message}');
        throw AppException.fromSupabaseError(e);
     } catch (e,s) {
         _log.severe('AuthService: Unexpected error updating password (recovery flow): $e', e, s);
         throw AppException('An unexpected error occurred updating password.');
     }
   }

    // Update Profile (Could also delegate to SupabaseService)
   @override
   Future<void> updateProfile(String userId, Map<String, dynamic> data) async {
      _log.info('AuthService: Updating profile for user: $userId');
       try {
           await _supabaseService.updateUserProfile(userId, data); // Delegate
            _log.info('AuthService: Profile updated successfully.');
       } catch (e) {
           _log.severe('AuthService: Profile update failed: $e');
           // Re-throw as AppException or handle as needed
            if (e is AppException) rethrow;
            throw AppException('Failed to update profile.');
       }
   }

  @override
  Future<List<ClubAffiliation>> getDirectorClubAffiliations(String userId) async {
    _log.info('AuthService: Fetching club affiliations for director: $userId');
    try {
      final response = await _client
          .from('club_directors')
          .select('*, clubs(club_name)') // Join with clubs table to get club_name
          .eq('user_id', userId);

      if (response.isEmpty) {
        _log.info('AuthService: No club affiliations found for director: $userId');
        return [];
      }

      final affiliations = response
          .map((item) {
            // The join puts the 'clubs' table data into a nested map.
            // We need to extract 'club_name' from it.
            final clubData = item['clubs'] as Map<String, dynamic>?;
            final clubName = clubData?['club_name'] as String? ?? 'Unknown Club';
            
            // Create a new map that includes club_name at the top level for ClubAffiliation.fromJson
            final flatItem = Map<String, dynamic>.from(item);
            flatItem['club_name'] = clubName;
            flatItem.remove('clubs'); // Remove the nested clubs map

            return ClubAffiliation.fromJson(flatItem);
          })
          .toList();
      _log.info('AuthService: Successfully fetched ${affiliations.length} club affiliations for director: $userId');
      return affiliations;
    } on PostgrestException catch (e) {
      _log.severe('AuthService: Error fetching club affiliations - ${e.message}', e);
      throw AppException.fromSupabaseError(e);
    } catch (e, s) {
      _log.severe('AuthService: Unexpected error fetching club affiliations', e, s);
      throw AppException('An unexpected error occurred while fetching club affiliations.');
    }
  }

  @override
  Future<void> changePassword({required String currentPassword, required String newPassword}) async {
    _log.info('AuthService: Attempting to change password for current user.');
    final user = _auth.currentUser;
    if (user == null || user.email == null) {
      _log.warning('AuthService: No authenticated user or user email found to change password.');
      throw AppException('User not authenticated. Cannot change password.');
    }

    try {
      // Step 1: Re-authenticate with current password.
      // This is a common pattern, but Supabase's updateUser with a new password
      // while authenticated *should* be enough. However, explicit re-auth can be safer.
      // For simplicity here, we'll rely on Supabase's own checks when updateUser is called.
      // If Supabase requires re-authentication for password changes, its updateUser call will fail.
      // A more robust flow might involve signInWithPassword again if updateUser fails with a specific error.

      _log.fine('AuthService: Calling Supabase updateUser to change password.');
      await _auth.updateUser(
        UserAttributes(password: newPassword),
      );
      _log.info('AuthService: Password changed successfully via updateUser.');

      // Note: Supabase might invalidate other sessions after a password change.
      // The onAuthStateChange listener should handle any resulting session changes.

    } on AuthException catch (e) {
      _log.warning('AuthService: Change password failed - ${e.message}');
      // Check for specific errors, e.g., "Invalid current password" if Supabase provides such.
      // Supabase might return a generic "Unauthorized" or similar if current password check is implicit.
      if (e.message.toLowerCase().contains('password') || e.statusCode == 401 || e.statusCode == 400) {
         throw AppException('Failed to change password. Please check your current password.');
      }
      throw AppException.fromSupabaseError(e);
    } catch (e, s) {
      _log.severe('AuthService: Unexpected error changing password', e, s);
      throw AppException('An unexpected error occurred while changing password.');
    }
  }
   /*
   // --- Club Affiliation methods commented out for now ---
   // These methods are not relevant to the JWT validation task

   Future<List<ClubAffiliation>> getClubAffiliations(String userId) async {
       // Implementation omitted
   }

   Future<void> requestClubAffiliation(String clubId) async { /* ... call RPC ... */ }
   Future<void> respondToClubInvitation(String affiliationId, bool accept) async { /* ... call RPC ... */ }
   Future<void> actionDirectorAffiliationRequest(String affiliationId, DirectorClubStatus status, String? notes) async { /* ... call RPC ... */ }
   Future<void> inviteDirectorToClub(String email, String clubId) async { /* ... call RPC ... */ }
   */
}
