// lib/core/services/supabase_service.dart
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:logging/logging.dart';
import '../utils/app_exception.dart';
import '../interfaces/supabase_service_interface.dart';

// Basic wrapper around the Supabase client instance
class SupabaseService implements SupabaseServiceInterface {
  final SupabaseClient client; // Inject client
  final Logger _log = Logger('SupabaseService');

  SupabaseService(this.client) {
    _log.info('SupabaseService Initialized');
  }

  // --- Generic DB Helpers (Use with Caution) ---
  Future<List<Map<String, dynamic>>> fetchAll(String table, {String select = '*'}) async {
    _log.fine('Fetching all from table: $table (select: $select)');
    try {
      final response = await client.from(table).select(select);
      return response;
    } on PostgrestException catch (e, s) {
      _log.severe('PostgrestException fetching all from $table: ${e.message}', e, s);
      throw AppException.fromSupabaseError(e);
    } catch (e, s) {
      _log.severe('Unexpected error fetching all from $table: $e', e, s);
      throw AppException('An unexpected error occurred.');
    }
  }

  // --- Profile Specific Methods ---
  Future<Map<String, dynamic>?> getRawUserProfile(String userId) async {
    _log.fine('DB Service: Getting raw profile for $userId');
    try {
      final data = await client
          .from('profiles')
          .select() // Fetch all columns defined in DB
          .eq('id', userId)
          .maybeSingle();
      return data;
    } on PostgrestException catch (e, s) {
      _log.severe('DB Service: Error getting profile: ${e.message}', e, s);
      throw AppException.fromSupabaseError(e);
    } catch (e, s) {
       _log.severe('DB Service: Unexpected error getting profile: $e', e, s);
       throw AppException('An unexpected error occurred fetching the profile.');
    }
  }

  Future<Map<String, dynamic>> createUserProfile(Map<String, dynamic> data) async {
     _log.info('DB Service: Creating profile for ${data['id']}');
     try {
         // Ensure 'created_at' and 'updated_at' are set if DB doesn't default
         data['created_at'] ??= DateTime.now().toIso8601String();
         data['updated_at'] ??= DateTime.now().toIso8601String();

         final response = await client.from('profiles').insert(data).select().single();
         _log.fine('User profile created successfully in DB');
         return response;
     } on PostgrestException catch (e,s) {
        _log.severe('DB Service: Error creating profile: ${e.message}', e, s);
        throw AppException.fromSupabaseError(e);
     } catch (e,s) {
         _log.severe('DB Service: Unexpected error creating profile: $e', e, s);
         throw AppException('An unexpected error occurred creating profile.');
     }
  }

  Future<Map<String, dynamic>> updateUserProfile(String id, Map<String, dynamic> data) async {
     _log.info('DB Service: Updating profile for $id');
      try {
         final response = await client
             .from('profiles')
             .update(data..['updated_at'] = DateTime.now().toIso8601String()) // Ensure timestamp
             .eq('id', id)
             .select() // Return updated record
             .single();
         _log.fine('User profile updated successfully in DB');
         return response;
     } on PostgrestException catch (e,s) {
        _log.severe('DB Service: Error updating profile: ${e.message}', e, s);
        throw AppException.fromSupabaseError(e);
     } catch (e,s) {
         _log.severe('DB Service: Unexpected error updating profile: $e', e, s);
         throw AppException('An unexpected error occurred updating profile.');
     }
  }

  // --- Tournament Methods ---
  Future<List<Map<String, dynamic>>> getTournaments() async {
    try {
      final response = await client
          .from('tournaments')
          .select()
          .order('start_date', ascending: false);
      return List<Map<String, dynamic>>.from(response);
    } catch (e, s) {
      _log.severe('Error getting tournaments: $e', e, s);
      throw AppException('Failed to load tournaments');
    }
  }

  Future<Map<String, dynamic>> getTournamentById(String id) async {
    try {
      final response = await client
          .from('tournaments')
          .select()
          .eq('id', id)
          .single();
      return response;
    } catch (e, s) {
      _log.severe('Error getting tournament by ID: $e', e, s);
      throw AppException('Failed to load tournament details');
    }
  }

  // --- Team Methods ---
  Future<List<Map<String, dynamic>>> getTeamsByTournamentId(String tournamentId) async {
    try {
      final response = await client
          .from('teams')
          .select()
          .eq('tournament_id', tournamentId);
      return List<Map<String, dynamic>>.from(response);
    } catch (e, s) {
      _log.severe('Error getting teams by tournament ID: $e', e, s);
      throw AppException('Failed to load teams');
    }
  }

  // --- Match Methods ---
  Future<List<Map<String, dynamic>>> getMatchesByTournamentId(String tournamentId) async {
    try {
      final response = await client
          .from('matches')
          .select()
          .eq('tournament_id', tournamentId)
          .order('scheduled_time', ascending: true);
      return List<Map<String, dynamic>>.from(response);
    } catch (e, s) {
      _log.severe('Error getting matches by tournament ID: $e', e, s);
      throw AppException('Failed to load matches');
    }
  }

  // --- Scheduling Methods ---
  Future<Map<String, dynamic>> scheduleTournament(String tournamentId) async {
    try {
      final response = await client
          .rpc('schedule_tournament', params: {'p_tournament_id': tournamentId});
      return response;
    } catch (e, s) {
      _log.severe('Error scheduling tournament: $e', e, s);
      throw AppException('Failed to schedule tournament');
    }
  }

  // --- Bracket Methods ---
  Future<Map<String, dynamic>> generateBracket(String tournamentId) async {
    try {
      final response = await client
          .rpc('generate_bracket', params: {'p_tournament_id': tournamentId});
      return response;
    } catch (e, s) {
      _log.severe('Error generating bracket: $e', e, s);
      throw AppException('Failed to generate bracket');
    }
  }

  // --- Referee Methods ---
  Future<List<Map<String, dynamic>>> getRefereesByTournamentId(String tournamentId) async {
    try {
      final response = await client
          .from('referees')
          .select()
          .eq('tournament_id', tournamentId);
      return List<Map<String, dynamic>>.from(response);
    } catch (e, s) {
      _log.severe('Error getting referees by tournament ID: $e', e, s);
      throw AppException('Failed to load referees');
    }
  }

  // --- Realtime Subscriptions ---
  RealtimeChannel subscribeToTournament(String tournamentId, void Function(Map<String, dynamic>) callback) {
    _log.warning('Realtime subscribeToTournament is temporarily disabled due to package upgrade incompatibilities.');
    final channel = client.channel('public:tournaments:id=eq.$tournamentId');
    // TODO: Update Realtime subscription logic for supabase_flutter v2.x
    // The following code is for older versions and will cause errors.
    /*
    channel.on(
      RealtimeListenTypes.postgresChanges,
      ChannelFilter(event: '*', schema: 'public', table: 'tournaments'),
      (payload, [ref]) {
        callback(payload.newRecord as Map<String, dynamic>);
      },
    );

    channel.subscribe();
    */
    // For now, just return the channel without subscribing to avoid errors.
    // This means realtime updates for tournaments will not work until this is fixed.
    return channel;
  }
}
