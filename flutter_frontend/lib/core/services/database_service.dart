// lib/core/services/database_service.dart
import 'package:logging/logging.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../utils/app_exception.dart';

/// Service for database operations
class DatabaseService {
  final SupabaseClient _client;
  final Logger _log = Logger('DatabaseService');

  DatabaseService(this._client) {
    _log.info('DatabaseService initialized');
  }

  // --- Generic Database Methods ---

  /// Fetch all records from a table
  Future<List<Map<String, dynamic>>> fetchAll(String table, {String select = '*'}) async {
    _log.fine('Fetching all records from $table');
    try {
      final response = await _client.from(table).select(select);
      return List<Map<String, dynamic>>.from(response);
    } on PostgrestException catch (e, s) {
      _log.severe('Error fetching all from $table: ${e.message}', e, s);
      throw AppException.fromSupabaseError(e);
    } catch (e, s) {
      _log.severe('Unexpected error fetching all from $table: $e', e, s);
      throw AppException('An unexpected error occurred.');
    }
  }

  /// Fetch a single record by ID
  Future<Map<String, dynamic>?> fetchById(String table, String id, {String select = '*'}) async {
    _log.fine('Fetching record from $table with ID: $id');
    try {
      final response = await _client
          .from(table)
          .select(select)
          .eq('id', id)
          .maybeSingle();
      return response;
    } on PostgrestException catch (e, s) {
      _log.severe('Error fetching from $table by ID: ${e.message}', e, s);
      throw AppException.fromSupabaseError(e);
    } catch (e, s) {
      _log.severe('Unexpected error fetching from $table by ID: $e', e, s);
      throw AppException('An unexpected error occurred.');
    }
  }

  /// Insert a new record
  Future<Map<String, dynamic>> insert(String table, Map<String, dynamic> data) async {
    _log.fine('Inserting record into $table: $data');
    try {
      final response = await _client
          .from(table)
          .insert(data)
          .select()
          .single();
      return response;
    } on PostgrestException catch (e, s) {
      _log.severe('Error inserting into $table: ${e.message}', e, s);
      throw AppException.fromSupabaseError(e);
    } catch (e, s) {
      _log.severe('Unexpected error inserting into $table: $e', e, s);
      throw AppException('An unexpected error occurred.');
    }
  }

  /// Update a record
  Future<Map<String, dynamic>> update(String table, String id, Map<String, dynamic> data) async {
    _log.fine('Updating record in $table with ID: $id, data: $data');
    try {
      final response = await _client
          .from(table)
          .update(data)
          .eq('id', id)
          .select()
          .single();
      return response;
    } on PostgrestException catch (e, s) {
      _log.severe('Error updating $table: ${e.message}', e, s);
      throw AppException.fromSupabaseError(e);
    } catch (e, s) {
      _log.severe('Unexpected error updating $table: $e', e, s);
      throw AppException('An unexpected error occurred.');
    }
  }

  /// Delete a record
  Future<void> delete(String table, String id) async {
    _log.fine('Deleting record from $table with ID: $id');
    try {
      await _client
          .from(table)
          .delete()
          .eq('id', id);
    } on PostgrestException catch (e, s) {
      _log.severe('Error deleting from $table: ${e.message}', e, s);
      throw AppException.fromSupabaseError(e);
    } catch (e, s) {
      _log.severe('Unexpected error deleting from $table: $e', e, s);
      throw AppException('An unexpected error occurred.');
    }
  }

  /// Call a stored procedure
  Future<dynamic> callRpc(String function, {Map<String, dynamic>? params}) async {
    _log.fine('Calling RPC function: $function with params: $params');
    try {
      final response = await _client.rpc(function, params: params);
      return response;
    } on PostgrestException catch (e, s) {
      _log.severe('Error calling RPC $function: ${e.message}', e, s);
      throw AppException.fromSupabaseError(e);
    } catch (e, s) {
      _log.severe('Unexpected error calling RPC $function: $e', e, s);
      throw AppException('An unexpected error occurred.');
    }
  }

  // --- Specific Entity Methods ---
  // You can add methods for specific entities here, e.g.:

  /// Fetch tournaments
  Future<List<Map<String, dynamic>>> fetchTournaments() async {
    _log.fine('Fetching tournaments');
    try {
      final response = await _client
          .from('tournaments')
          .select('*, created_by_profile:profiles(first_name, last_name)')
          .order('start_date', ascending: false);

      return List<Map<String, dynamic>>.from(response);
    } on PostgrestException catch (e, s) {
      _log.severe('Error fetching tournaments: ${e.message}', e, s);
      throw AppException.fromSupabaseError(e);
    } catch (e, s) {
      _log.severe('Unexpected error fetching tournaments: $e', e, s);
      throw AppException('An unexpected error occurred.');
    }
  }

  // Add more specific methods as needed
}
