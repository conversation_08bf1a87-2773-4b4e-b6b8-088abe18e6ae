// lib/core/interfaces/auth_service_interface.dart
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../data/models/user_profile.dart';
import '../../data/models/club_affiliation.dart'; // Added import

/// Interface for authentication service operations
abstract class AuthServiceInterface {
  /// Get the current authenticated user
  User? get currentUser;

  /// Get the current session
  Session? get currentSession;

  /// Get the auth state change stream
  Stream<AuthState> get onAuthStateChange;

  /// Check if email confirmation is required
  bool get isEmailConfirmationRequired;

  /// Check if a user is logged in
  Future<bool> isLoggedIn();

  /// Checks if the current session's access token is present and not expired
  bool isCurrentTokenValid();

  /// Log the initial session validity (for debugging purposes)
  Future<void> logInitialSessionValidity();

  /// Sign in with email and password
  Future<AuthResponse> signIn({required String email, required String password});

  /// Sign up with email and password
  Future<AuthResponse> signUp({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
    required String role
  });

  /// Register a club admin
  Future<void> registerClubAdmin({
    required String clubName,
    required String adminFirstName,
    required String adminLastName,
    required String adminEmail,
    required String adminPassword
  });

  /// Request a password reset
  Future<void> requestPasswordReset(String email);

  /// Sign out the current user
  Future<void> signOut();

  /// Get a user profile by ID
  Future<UserProfile?> getUserProfile(String userId);

  /// Update a user's password
  Future<void> updatePassword(String newPassword);

  /// Update a user's profile
  Future<void> updateProfile(String userId, Map<String, dynamic> data);

  /// Get club affiliations for a Tournament Director
  Future<List<ClubAffiliation>> getDirectorClubAffiliations(String userId);

  /// Change current user's password
  Future<void> changePassword({required String currentPassword, required String newPassword});
}
