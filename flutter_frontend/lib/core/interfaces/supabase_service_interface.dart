// lib/core/interfaces/supabase_service_interface.dart
import 'package:supabase_flutter/supabase_flutter.dart';

/// Interface for Supabase service operations
abstract class SupabaseServiceInterface {
  /// Get the raw user profile data for a user
  Future<Map<String, dynamic>?> getRawUserProfile(String userId);
  
  /// Update a user profile
  Future<Map<String, dynamic>> updateUserProfile(String userId, Map<String, dynamic> data);
  
  /// Create a user profile
  Future<Map<String, dynamic>> createUserProfile(Map<String, dynamic> data);
  
  /// Get tournaments
  Future<List<Map<String, dynamic>>> getTournaments();
  
  /// Get tournament by ID
  Future<Map<String, dynamic>> getTournamentById(String id);
  
  /// Get teams by tournament ID
  Future<List<Map<String, dynamic>>> getTeamsByTournamentId(String tournamentId);
  
  /// Get matches by tournament ID
  Future<List<Map<String, dynamic>>> getMatchesByTournamentId(String tournamentId);
  
  /// Schedule a tournament
  Future<Map<String, dynamic>> scheduleTournament(String tournamentId);
  
  /// Generate a bracket for a tournament
  Future<Map<String, dynamic>> generateBracket(String tournamentId);
  
  /// Get referees by tournament ID
  Future<List<Map<String, dynamic>>> getRefereesByTournamentId(String tournamentId);
  
  /// Subscribe to tournament changes
  RealtimeChannel subscribeToTournament(String tournamentId, void Function(Map<String, dynamic>) callback);
}
