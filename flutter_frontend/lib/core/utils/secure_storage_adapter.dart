import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:logging/logging.dart';

/// A LocalStorage implementation using flutter_secure_storage.
class SecureStorageAdapter implements LocalStorage {
  final FlutterSecureStorage _storage;
  static const _sessionKey = supabasePersistSessionKey; // Use Supabase's constant key
  late final Logger _log;

  /// Creates a LocalStorage instance implemented by flutter_secure_storage
  ///
  /// If [storage] is provided, it will be used instead of creating a new instance.
  /// This is useful for testing.
  SecureStorageAdapter({FlutterSecureStorage? storage})
      : _storage = storage ?? const FlutterSecureStorage() {
    _log = Logger('SecureStorageAdapter');
  }

  // Implement the required functions from the LocalStorage interface
  @override
  Future<void> initialize() async {
    _log.fine('Initializing SecureStorageAdapter (no-op for flutter_secure_storage).');
    // No specific initialization needed for flutter_secure_storage itself here.
    // It's ready to use upon instantiation.
  }

  @override
  Future<void> persistSession(String persistSessionString) async {
    _log.fine('Persisting session to secure storage.');
    await _storage.write(key: _sessionKey, value: persistSessionString);
  }

  @override
  Future<bool> hasAccessToken() async {
    final hasToken = (await _storage.read(key: _sessionKey)) != null;
    _log.finest('Checking for access token in secure storage: $hasToken');
    return hasToken;
  }

  @override
  Future<String?> accessToken() async {
    _log.finest('Retrieving session from secure storage.');
    return await _storage.read(key: _sessionKey);
  }

  @override
  Future<void> removePersistedSession() async {
    _log.fine('Removing session from secure storage.');
    await _storage.delete(key: _sessionKey);
  }
}
