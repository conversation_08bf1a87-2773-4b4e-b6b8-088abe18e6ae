// lib/core/utils/app_bloc_observer.dart
import 'package:bloc/bloc.dart';
import 'package:flutter/foundation.dart' show kDebugMode;
import 'package:logging/logging.dart';
import '../utils/app_exception.dart';

class AppBlocObserver extends BlocObserver {
  final Logger _log = Logger('BlocObserver');

  @override
  void onCreate(BlocBase bloc) {
    super.onCreate(bloc);
    // Optional: Log only in very verbose modes if needed
    _log.finest('onCreate -- ${bloc.runtimeType}');
  }

  @override
  void onEvent(Bloc bloc, Object? event) {
    super.onEvent(bloc, event);
    // Log events only in debug mode to reduce noise
    if (kDebugMode) {
      _log.info('Event -- ${bloc.runtimeType}: ${event.runtimeType}');
       _log.fine('Event details -- $event'); // Log full event details at finer level
    }
  }

  @override
  void onChange(BlocBase bloc, Change change) {
    super.onChange(bloc, change);
    // Log state changes only in debug mode
    if (kDebugMode) {
      final current = change.currentState;
      final next = change.nextState;
      final String blocType = bloc.runtimeType.toString();
      final String currentState = current.runtimeType.toString();
      final String nextState = next.runtimeType.toString();

      if (currentState != nextState) {
        // Log state *type* changes clearly
        _log.info('🔄 $blocType: $currentState → $nextState');
        // Log full state objects at fine level
        _log.fine('   • CurrentState data: $current');
        _log.fine('   • NextState    data: $next');

        // DEBUG: Print state changes for AuthBloc
        if (blocType == 'AuthBloc') {
          print('>>> STATE CHANGE: $blocType: $currentState → $nextState');

          // Special focus on AuthPasswordResetEmailSent
          if (nextState == 'AuthPasswordResetEmailSent') {
            print('>>> DETECTED AuthPasswordResetEmailSent in BlocObserver');
            print('>>> Full state: $next');
          }
        }
      } else {
        // Optionally log fine details if state type is same but content changed
        _log.finest('Change -- $blocType: $currentState (content changed)');
        _log.finest('   • CurrentState data: $current');
        _log.finest('   • NextState    data: $next');
      }
    }
  }

  @override
  void onTransition(Bloc bloc, Transition transition) {
    super.onTransition(bloc, transition);
    // Log transitions only in debug mode
    if (kDebugMode) {
       final current = transition.currentState;
       final next = transition.nextState;
       final event = transition.event;

       final String blocType = bloc.runtimeType.toString();
       final String eventType = event.runtimeType.toString();
       final String currentState = current.runtimeType.toString();
       final String nextState = next.runtimeType.toString();

       // Log clearly when state type changes
       if (currentState != nextState) {
          _log.info('⚡ $blocType: $eventType → $currentState → $nextState');
          _log.fine('   • Event: $event');
          _log.fine('   • CurrentState: $current');
          _log.fine('   • NextState: $next');
       } else {
          // Log less prominently if only content changes
          _log.fine('Transition -- $blocType -- Event: $eventType -- State: $currentState (content changed)');
          _log.finest('   • Event: $event');
          _log.finest('   • CurrentState: $current');
          _log.finest('   • NextState: $next');
       }
    }
  }

  @override
  void onError(BlocBase bloc, Object error, StackTrace stackTrace) {
    super.onError(bloc, error, stackTrace);
    // Always log errors, regardless of mode
    _log.severe('ERROR -- ${bloc.runtimeType}: ${error.runtimeType}', error, stackTrace);
    // Also log the specific error message if it's an AppException
    if (error is AppException) {
       _log.severe('  AppException Message: ${error.message}');
    }
  }

  @override
  void onClose(BlocBase bloc) {
    super.onClose(bloc);
    // Optional: Log only in very verbose modes if needed
    _log.finest('onClose -- ${bloc.runtimeType}');
  }
}
