// lib/core/utils/jwt_validation_util.dart
import 'package:jwt_decoder/jwt_decoder.dart';
import 'package:logging/logging.dart';

final _log = Logger('JwtValidationUtil');

/// Checks if a JWT token string is expired.
///
/// Returns `true` if the token is expired, null, empty, or invalid.
/// Returns `false` if the token is valid and not expired.
bool isTokenExpired(String? token) {
  if (token == null || token.isEmpty) {
    _log.fine('Token is null or empty, considering expired.');
    return true; // Treat null/empty as expired
  }
  try {
    final bool expired = JwtDecoder.isExpired(token);
    _log.finest('Token expiry check result: $expired');
    return expired;
  } catch (e) {
    // Handle potential errors during decoding (e.g., malformed token)
    _log.warning('Error decoding token during expiry check: $e. Treating as expired.');
    return true; // Treat errors as expired
  }
}

/// Optional: Helper to get expiration date
DateTime? getTokenExpirationDate(String? token) {
   if (token == null || token.isEmpty) return null;
   try {
     return JwtDecoder.getExpirationDate(token);
   } catch (e) {
     _log.warning('Error decoding token for expiration date: $e');
     return null;
   }
}

/// Optional: Helper to decode payload
Map<String, dynamic>? decodeTokenPayload(String? token) {
   if (token == null || token.isEmpty) return null;
    try {
     return JwtDecoder.decode(token);
   } catch (e) {
     _log.warning('Error decoding token payload: $e');
     return null;
   }
}
