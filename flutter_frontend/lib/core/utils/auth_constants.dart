// ignore_for_file: constant_identifier_names

import 'dart:io' show Platform;
import 'package:flutter/foundation.dart' show kIsWeb, kReleaseMode, kDebugMode, visibleForTesting;
import 'package:logging/logging.dart';
// dotenv import can be removed if only used for Supabase keys previously
// import 'package:flutter_dotenv/flutter_dotenv.dart'; 

/// Constants for authentication, URLs, and roles.
class AuthConstants {
  static final Logger _log = Logger('AuthConstants');

  // --- Configuration ---
  static const int localDevPort = 8114; // Port for `flutter run -d chrome --web-port`

  // --- Environment ---
  /// Use Flutter's built-in compile-time constant for release mode check.
  static const bool isProduction = kReleaseMode;

  // --- Hosts & Schemes ---
  static const String _devWebHost = 'http://localhost:$localDevPort';
  static const String _prodHost   = 'https://your-production-app.com'; // <<< SET YOUR ACTUAL PRODUCTION DOMAIN
  static const String urlScheme   = 'tournamentscheduler'; // <<< YOUR IOS/ANDROID CUSTOM URL SCHEME (e.g., com.yourcompany.appname)

  // --- Paths ---
  /// The specific path within your app that handles Supabase auth callbacks.
  static const String callbackPath = '/auth/callback';
  static const String welcomePath = '/'; // <<< Path to redirect to after confirmation

  // --- Supabase Credentials (Using String.fromEnvironment) ---
  // These values MUST be provided during the build/run command for web/release
  // Example: flutter run -d chrome --dart-define=SUPA_URL=YOUR_URL --dart-define=SUPA_ANON=YOUR_KEY
  static const String supabaseUrl = String.fromEnvironment(
    'SUPA_URL',
    // Fallback ONLY for local `flutter run` WITHOUT --dart-define flags.
    defaultValue: kDebugMode ? 'https://apxcvcnizxmjfrzaksoz.supabase.co' : '', 
  );
  static const String supabaseAnonKey = String.fromEnvironment(
    'SUPA_ANON',
    // Fallback ONLY for local `flutter run` WITHOUT --dart-define flags.
    // WARNING: Avoid committing real keys here. Use --dart-define in practice.
    defaultValue: kDebugMode ? 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFweGN2Y25penhtamZyemFrc296Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM4MjYwNjYsImV4cCI6MjA1OTQwMjA2Nn0.mW0bY_p0jCN7rXXdeKNNE45o9eHC4iB5JYjHIm-wx3c' : '', 
  );

  // --- Computed URLs ---
  /// Gets the base host (scheme://domain:port) depending on the environment.
  static String get _currentBaseHost => isProduction ? _prodHost : _devWebHost;

  /// The canonical redirect URL sent to Supabase for email links (confirm, recovery).
  /// Determines the correct format based on platform and environment.
  /// This MUST match entries in your Supabase Auth Redirect URLs list.
  static String get redirectUrl {
    String url;
    // kIsWeb is compile-time, Platform is runtime
    if (kIsWeb) {
      // Web always uses HTTP/S base URL + callback path
      url = '$_currentBaseHost$callbackPath';
    } else if (!isProduction && (Platform.isIOS || Platform.isAndroid)) {
      // Mobile Development uses custom scheme + callback path separator '://'
      url = '$urlScheme:/$callbackPath'; // Default format, check Supabase requirements
      // Alternative if needed: url = '$urlScheme://$callbackPath';
    } else {
      // Production Mobile (Universal Links/App Links) or Other platforms
      // typically use the standard HTTPS URL.
      url = '$_currentBaseHost$callbackPath';
    }

    // ***** ADD THIS PRINT STATEMENT *****
    print('>>> AuthConstants.redirectUrl IS: $url (isWeb: $kIsWeb, isProd: $isProduction)');
    // ***********************************

    if (kDebugMode) {
      _log.fine('Using redirect URL for Supabase: $url');
    }
    return url;
  }

  /// Helper method mainly for unit testing - allows explicitly checking URL generation logic.
  @visibleForTesting
  static String getRedirectUrlForPlatform({
    required bool isProduction,
    required bool isWeb,
    required bool isIOS,
    required bool isAndroid,
  }) {
     final String baseHost = isProduction ? _prodHost : _devWebHost;
    // Production uses HTTPS regardless of platform for Universal Links / App Links
    if (isProduction) {
      return '$baseHost$callbackPath';
    }
    // Development Web uses HTTP localhost
    if (isWeb) {
       return '$baseHost$callbackPath';
     }
     // Development Mobile uses custom scheme
     else if (isIOS || isAndroid) {
       return '$urlScheme:/$callbackPath'; // Match the main getter logic
     }
     // Fallback for other platforms during testing/dev
     else {
       return '$baseHost$callbackPath';
     }
  }

  // --- Other Constants ---
  /// Minimum password length enforced by Supabase default (adjust if changed in Supabase).
  static const int minPasswordLength = 6;

  // Define roles consistently using constants
  static const String rolePlayerParent = 'player_parent';
  static const String roleCoach = 'coach';
  static const String roleTeamManager = 'team_manager';
  static const String roleReferee = 'referee';
  static const String roleTournamentDirector = 'tournament_director';
  static const String roleClubAdmin = 'club_admin';
  static const String roleAdmin = 'admin';

  /// Ensures essential Supabase configuration values are present. Call early in main().
  /// Call this *after* potential dotenv load (if used for other vars) but *before* Supabase init.
  static void checkConfiguration() {
     // Check the final values being used by the getters
     final url = AuthConstants.supabaseUrl;
     final key = AuthConstants.supabaseAnonKey;

     if (url.isEmpty) { // Removed check for 'YOUR_PROJECT_ID' as defaultValue handles it
       throw StateError('FATAL ERROR: Supabase URL is not configured. Provide via --dart-define=SUPA_URL=...');
     }
      if (key.isEmpty) { // Removed check for 'YOUR_ANON_KEY'
       throw StateError('FATAL ERROR: Supabase Anon Key is not configured. Provide via --dart-define=SUPA_ANON=...');
     }
     _log.info('AuthConstants: Supabase URL and Key seem configured.');
     // Keep the debug print for redirect URL
     if (kDebugMode) print('>>> AuthConstants.redirectUrl = ${AuthConstants.redirectUrl}');
  }
}
