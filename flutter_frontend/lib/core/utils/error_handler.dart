// lib/src/utils/error_handler.dart
import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:logging/logging.dart';

/// Global error handler for the application
class ErrorHandler {
  static final Logger _log = Logger('ErrorHandler');
  
  // Private constructor to prevent instantiation
  ErrorHandler._();
  
  /// Initialize the global error handler
  static void initialize() {
    _log.info('Initializing global error handler');
    
    // Handle Flutter errors
    FlutterError.onError = (FlutterErrorDetails details) {
      _log.severe('Flutter error: ${details.exception}', details.exception, details.stack);
      // Forward to Flutter's original error handler
      if (kDebugMode) {
        FlutterError.dumpErrorToConsole(details);
      }
    };
    
    // Handle Dart errors
    PlatformDispatcher.instance.onError = (error, stack) {
      _log.severe('Uncaught platform error: $error', error, stack);
      // Return true to prevent the error from propagating
      return true;
    };
    
    // Handle Zone errors
    runZonedGuarded(
      () {
        _log.info('Zone error handler registered');
      },
      (error, stackTrace) {
        _log.severe('Uncaught zone error: $error', error, stackTrace);
      },
    );
    
    _log.info('Global error handler initialized');
  }
  
  /// Handle a specific error and return a user-friendly message
  static String handleError(dynamic error, [StackTrace? stackTrace]) {
    _log.severe('Handling error: $error', error, stackTrace);
    
    // Handle specific error types
    if (error is SocketException) {
      return 'Network error. Please check your internet connection.';
    } else if (error is TimeoutException) {
      return 'Request timed out. Please try again.';
    } else if (error is FormatException) {
      return 'Invalid data format. Please try again.';
    } else {
      // Generic error message for unknown errors
      return 'An unexpected error occurred. Please try again later.';
    }
  }
  
  /// Log an error without showing it to the user
  static void logError(String message, [dynamic error, StackTrace? stackTrace]) {
    _log.severe(message, error, stackTrace);
  }
  
  /// Show an error dialog to the user
  static void showErrorDialog(BuildContext context, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Error'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
  
  /// Show a snackbar with an error message
  static void showErrorSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Theme.of(context).colorScheme.error,
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 3),
      ),
    );
  }
}
