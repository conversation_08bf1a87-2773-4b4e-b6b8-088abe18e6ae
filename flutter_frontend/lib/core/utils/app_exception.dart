// lib/utils/app_exception.dart
import 'package:supabase_flutter/supabase_flutter.dart';

/// Custom exception class for application-specific errors
class AppException implements Exception {
  final String message;
  final String? code;
  final dynamic originalError;

  AppException(
    this.message, {
    this.code,
    this.originalError,
  });

  /// Factory constructor to create an AppException from a Supabase error
  factory AppException.fromSupabaseError(dynamic error) {
    if (error is AuthException) {
      return AppException(
        _getReadableAuthMessage(error.message),
        code: error.statusCode?.toString() ?? 'auth-error',
        originalError: error,
      );
    } else if (error is PostgrestException) {
      return AppException(
        _getReadablePostgrestMessage(error.message),
        code: error.code,
        originalError: error,
      );
    } else {
      return AppException(
        'An unexpected error occurred',
        code: 'unknown',
        originalError: error,
      );
    }
  }

  @override
  String toString() => 'AppException: $message${code != null ? ' (Code: $code)' : ''}';

  /// Convert Supabase Auth error messages to more user-friendly messages
  static String _getReadableAuthMessage(String message) {
    // Map common Supabase error messages to user-friendly messages
    if (message.contains('Invalid login credentials')) {
      return 'Invalid email or password. Please try again.';
    } else if (message.contains('Email not confirmed')) {
      return 'Please confirm your email before logging in.';
    } else if (message.contains('User already registered')) {
      return 'An account with this email already exists.';
    } else if (message.contains('Password should be at least')) {
      return 'Password is too short. Please use at least 6 characters.';
    } else if (message.contains('Rate limit') || message.contains('over_email_send_rate_limit')) {
      return 'Too many password reset attempts. Please try again in 60 minutes.';
    }

    // Return the original message if no mapping exists
    return message;
  }

  /// Convert Postgrest error messages to more user-friendly messages
  static String _getReadablePostgrestMessage(String message) {
    // Map common Postgrest error messages to user-friendly messages
    if (message.contains('duplicate key')) {
      return 'This record already exists.';
    } else if (message.contains('violates foreign key constraint')) {
      return 'This operation references a record that does not exist.';
    } else if (message.contains('violates check constraint')) {
      return 'The data you provided is invalid.';
    } else if (message.contains('violates not-null constraint')) {
      return 'Required information is missing.';
    }

    // Return the original message if no mapping exists
    return message;
  }
}
