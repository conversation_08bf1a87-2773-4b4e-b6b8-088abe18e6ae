import 'package:supabase_flutter/supabase_flutter.dart';
import 'dart:io';

// Import your auth constants
import '../core/utils/auth_constants.dart';

void main() async {
  // Initialize Supabase
  await Supabase.initialize(
    url: AuthConstants.supabaseUrl,
    anonKey: AuthConstants.supabaseAnonKey,
  );

  final supabase = Supabase.instance.client;
  
  try {
    print('Checking database schema...');

    // List all tables in the public schema
    try {
      final tablesData = await supabase.rpc('get_tables_info');
      if (tablesData is List) {
        print('\n=== Tables ===');
        for (final table in tablesData) {
           if (table is Map<String, dynamic>) {
             print('Table: ${table['table_name']}');
           }
        }
      } else {
         print('Unexpected data format for tables: $tablesData');
      }
    } on PostgrestException catch (e) {
      print('Error fetching tables: ${e.message}');
      exit(1); // Exit if we can't even get tables
    } catch (e) {
      print('Unexpected error fetching tables: $e');
      exit(1);
    }

    // Check profiles table structure
    try {
      final profilesData = await supabase.rpc('get_table_columns', params: {'p_table_name': 'profiles'}); // Ensure param name matches function definition
       if (profilesData is List) {
         print('\n=== Profiles Table Columns ===');
         for (final column in profilesData) {
            if (column is Map<String, dynamic>) {
              print('${column['column_name']} (${column['data_type']})${column['is_nullable'] == 'NO' ? ' NOT NULL' : ''}');
            }
         }
       } else {
          print('Unexpected data format for profiles columns: $profilesData');
       }
    } on PostgrestException catch (e) {
      print('Error fetching profiles schema: ${e.message}');
    } catch (e) {
      print('Unexpected error fetching profiles schema: $e');
    }

    // Check RLS policies on profiles table
    try {
      final rlsData = await supabase.rpc('get_rls_policies', params: {'p_table_name': 'profiles'}); // Ensure param name matches function definition
      if (rlsData is List) {
        print('\n=== RLS Policies on Profiles Table ===');
        if (rlsData.isEmpty) {
          print('No RLS policies found for profiles table.');
        } else {
          for (final policy in rlsData) {
             if (policy is Map<String, dynamic>) {
               print('Policy: ${policy['policyname']} - ${policy['permissive']} - ${policy['cmd']} - ${policy['qual']}');
             }
          }
        }
      } else {
         print('Unexpected data format for RLS policies: $rlsData');
      }
    } on PostgrestException catch (e) {
      print('Error fetching RLS policies: ${e.message}');
    } catch (e) {
      print('Unexpected error fetching RLS policies: $e');
    }

    // Check handle_new_user trigger
    try {
      final triggerData = await supabase.rpc('get_trigger_info', params: {'p_trigger_name': 'handle_new_user'}); // Ensure param name matches function definition
      if (triggerData is List) {
        print('\n=== handle_new_user Trigger ===');
         if (triggerData.isEmpty) {
           print('Trigger "handle_new_user" not found.');
         } else {
           for (final trigger in triggerData) {
              if (trigger is Map<String, dynamic>) {
                print('Trigger: ${trigger['trigger_name']} on ${trigger['event_manipulation']} - Function: ${trigger['action_statement']}');
              }
           }
         }
      } else {
         print('Unexpected data format for trigger info: $triggerData');
      }
    } on PostgrestException catch (e) {
      print('Error fetching trigger info: ${e.message}');
    } catch (e) {
      print('Unexpected error fetching trigger info: $e');
    }

  } catch (e) { // Catch errors during Supabase initialization or other setup
    print('Error: $e');
    exit(1);
  }
}
