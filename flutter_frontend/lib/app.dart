import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:logging/logging.dart';
import 'package:supabase_flutter/supabase_flutter.dart' hide AuthState;
import 'package:uni_links/uni_links.dart';

// Import CallbackHandler
import 'features/auth/utils/callback_handler.dart' as auth_callback;
import 'core/router/app_router.dart'; // Import AppRouter

import 'features/auth/blocs/auth_bloc.dart';

// Import primary screens

// Import debug screens

import 'core/theme/app_theme.dart';
import 'presentation/widgets/widgets.dart'; // For ErrorListener, OfflineIndicator

class TournamentSchedulerApp extends StatefulWidget {
  const TournamentSchedulerApp({super.key});

  @override
  State<TournamentSchedulerApp> createState() => _TournamentSchedulerAppState();
}

class _TournamentSchedulerAppState extends State<TournamentSchedulerApp> {
  late final GoRouter _router;
  late final auth_callback.CallbackHandler _callbackHandler; // Added
  final Logger _log = Logger('TournamentSchedulerApp');

  @override
  void initState() {
    super.initState();
    _log.info('TournamentSchedulerApp initializing...');
    _callbackHandler = auth_callback.CallbackHandler(); // Instantiate
    
    // Use the AppRouter class from lib/core/router/app_router.dart
    // Ensure AppRouter is imported
    // import 'core/router/app_router.dart'; // Make sure this import exists or add it
    final appRouter = AppRouter(authBloc: context.read<AuthBloc>(), callbackHandler: _callbackHandler);
    _router = appRouter.router; 

    // Listen for deep links (email confirmation)
    _setupDeepLinkListener();
    _log.info('GoRouter instance from AppRouter class created.');
  }

  // Handle incoming links - both initial and dynamic links
  StreamSubscription? _deepLinkSubscription;
  bool _initialUriHandled = false;

  void _setupDeepLinkListener() {
    _log.info('Setting up deep link listeners for email confirmation');

    // 1. Listen for Supabase auth state changes
    // The AuthBloc now handles these state changes and updates its own state.
    // GoRouter listens to AuthBloc for navigation.
    // The specific logic for navigating to /auth/confirmation-success after email confirmation
    // should be handled more carefully, perhaps within the CallbackScreen or by AuthBloc emitting a specific state
    // that GoRouter can use for a one-time redirect.
    // Removing the direct navigation from here to prevent conflicts with GoRouter's main redirect logic.
    // Supabase.instance.client.auth.onAuthStateChange.listen((data) {
    //   final event = data.event;
    //   _log.info('Auth state changed: $event (app.dart listener - consider removal)');
    //   // The navigation logic previously here was causing issues with post-login redirects.
    // });

    // Handle the current URL for web platforms
    if (kIsWeb) {
      _log.info('Web platform detected, handling current URL');
      _handleWebDeepLink();
    } else {
      // For mobile platforms, use uni_links
      _log.info('Mobile platform detected, setting up deep link handling');

      // 2. Handle initial URI if the app was started by a link
      _handleInitialUri();

      // 3. Listen for incoming links while the app is running
      _handleIncomingLinks();
    }
  }

  void _handleWebDeepLink() {
    try {
      // For web, we can access the current URL directly
      final uri = Uri.base;
      _log.info('Web URI detected: $uri');
      _handleDeepLink(uri);
    } catch (e) {
      _log.severe('Error handling web URI: $e');
    }
  }

  Future<void> _handleInitialUri() async {
    if (!_initialUriHandled) {
      _initialUriHandled = true;
      try {
        // This is only for mobile platforms
        if (!kIsWeb) {
          final uri = await getInitialUri();
          if (uri == null) {
            _log.info('No initial URI detected');
            return;
          }
          _log.info('Initial URI detected: $uri');
          _handleDeepLink(uri);
        }
      } catch (e) {
        _log.severe('Error handling initial URI: $e');
      }
    }
  }

  void _handleIncomingLinks() {
    // This is only for mobile platforms
    if (!kIsWeb) {
      try {
        _deepLinkSubscription = uriLinkStream.listen((Uri? uri) {
          if (uri != null) {
            _log.info('Incoming URI detected: $uri');
            _handleDeepLink(uri);
          }
        }, onError: (Object error) {
          _log.severe('Error handling incoming URI: $error');
        });
      } catch (e) {
        _log.info('Deep link stream not available on this platform: $e');
      }
    }
  }

  void _handleDeepLink(Uri uri) {
    try {
      _log.info('Processing deep link: $uri');

      // Extract the path and query parameters
      final path = uri.path;
      final queryParams = uri.queryParameters;

      _log.info('Deep link path: $path, query params: $queryParams');

      // Handle different types of deep links
      if (path.contains('/auth/callback')) {
        _log.info('Processing auth callback from deep link');

        // Navigate to the callback screen
        Future.delayed(Duration.zero, () {
          if (mounted) {
            // The CallbackScreen will handle the rest
            _router.go('/auth/callback');
          }
        });
      }
    } catch (e) {
      _log.severe('Error processing deep link: $e');
    }
  }

  @override
  void dispose() {
    _deepLinkSubscription?.cancel();
    super.dispose();
  }

  // GoRouter _createRouter() { // This local _createRouter is no longer needed if AppRouter class is used.
  //   // ... existing local router definition ...
  // }

  @override
  Widget build(BuildContext context) {
    return MaterialApp.router(
      title: 'Tournament Scheduler App',
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: ThemeMode.system,
      routerConfig: _router, // Use the router created in initState
      debugShowCheckedModeBanner: false,
      // Wrap the Router's output, not the MaterialApp itself
      builder: (context, child) {
        // IMPORTANT: 'child' here is the widget returned by GoRouter for the current route
        if (child == null) return const SizedBox.shrink(); // Handle null case

        return ErrorListener(
          child: OfflineIndicator(
            child: child, // The actual page widget built by GoRouter
          ),
        );
      },
    );
  }
}

// Helper class to bridge Bloc stream to Listenable for GoRouter
class GoRouterRefreshStream extends ChangeNotifier {
  late final StreamSubscription<dynamic> _subscription;
  final Logger _log = Logger('GoRouterRefreshStream');

  GoRouterRefreshStream(Stream<dynamic> stream) {
    notifyListeners(); // Initial notify might be needed
    _subscription = stream.asBroadcastStream().listen(
      (dynamic _) {
         _log.fine('Received stream event, notifying GoRouter.');
         notifyListeners(); // Notify GoRouter on every stream event
      },
      onError: (error) {
          _log.warning('Error in refresh stream: $error');
           notifyListeners(); // Still notify on error? Maybe not needed.
      }
    );
  }

  @override
  void dispose() {
    _subscription.cancel();
    _log.info('Refresh stream subscription cancelled.');
    super.dispose();
  }
}
