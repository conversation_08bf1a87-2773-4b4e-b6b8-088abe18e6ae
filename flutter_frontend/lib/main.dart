// lib/main.dart
import 'dart:async';
import 'package:flutter/foundation.dart'; // For kDebugMode
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
// For RepositoryProvider
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:supabase_auth_ui/supabase_auth_ui.dart';
import 'package:logging/logging.dart';
import 'package:flutter_web_plugins/url_strategy.dart'; // Use PathUrlStrategy

// Core App
import 'app.dart';

// Core Services
import 'core/services/auth_service.dart';
import 'core/services/supabase_service.dart';
import 'core/services/connectivity_service.dart';
import 'core/services/database_service.dart';
import 'features/tournament/services/tournament_api_service.dart'; // Added
import 'features/scheduling/services/scheduling_api_service.dart'; // Added
import 'services/venue_service.dart'; // Added
import 'services/field_service.dart'; // Added

// Interfaces
import 'core/interfaces/auth_service_interface.dart';
import 'core/interfaces/supabase_service_interface.dart';

// Feature BLoCs
import 'features/auth/blocs/auth_bloc.dart';
import 'features/profile/blocs/profile_bloc.dart';
import 'features/tournament/blocs/tournament_bloc.dart';
import 'features/team/blocs/team_bloc.dart';
import 'features/match/blocs/match_bloc.dart';
import 'features/scheduling/blocs/scheduling_bloc.dart';
import 'features/bracket/blocs/bracket_bloc.dart';
import 'features/referee/blocs/referee_bloc.dart';

// Core Utils & Widgets
import 'core/utils/auth_constants.dart';
import 'presentation/widgets/supabase_error_app.dart';
import 'core/utils/app_bloc_observer.dart';
import 'core/utils/error_handler.dart';

Future<void> main() async {
  // 1. Ensure Flutter Init
  WidgetsFlutterBinding.ensureInitialized();
  // 2. Use Path URL Strategy for web
  // This ensures URLs don't have the hash (#) symbol
  // and makes deep linking work better
  usePathUrlStrategy();

  // Print the port information for debugging
  if (kDebugMode) {
    print('=== IMPORTANT DEVELOPMENT WORKFLOW ===');
    print('1. Make sure you run the app with: flutter run -d chrome --web-port ${AuthConstants.localDevPort}');
    print('2. Current expected port: ${AuthConstants.localDevPort}');
    print('3. KEEP THE APP RUNNING when testing email confirmation links');
    print('4. The app must be running at http://localhost:${AuthConstants.localDevPort} when you click the confirmation link');
    print('5. If you get "Connection Refused" errors, ensure the app is running before clicking the link');
    print('=========================================');
  }

  // 3. Setup Logging
  Logger.root.level = kDebugMode ? Level.ALL : Level.INFO;
  Logger.root.onRecord.listen((record) {
    debugPrint('${record.level.name}: ${record.time.toIso8601String()}: ${record.loggerName}: ${record.message}');
    if (record.error != null) debugPrint('>>> Error: ${record.error}');
    if (kDebugMode && record.stackTrace != null) {
      // Avoid printing huge stack traces in release
      // debugPrint('>>> StackTrace: ${record.stackTrace}');
    }
  });
  final log = Logger('main');
  log.info('===========================================');
  log.info('Application Starting...');
  log.info('===========================================');

  // 4. Setup BLoC Observer
  Bloc.observer = AppBlocObserver();
  log.info('BlocObserver registered.');

  // 5. Load Environment Variables (Optional but recommended)
  try {
    await dotenv.load(fileName: ".env");
    log.info('.env file loaded successfully.');
  } catch (e) {
    log.warning('Could not load .env file: $e. Using constants or build arguments.');
  }

  // 6. Initialize Supabase
  // *** Check configuration AFTER potential dotenv load but BEFORE Supabase init ***
  try {
     AuthConstants.checkConfiguration(); // This now checks the constants directly
  } catch (e) {
     log.severe(e.toString());
     runApp(SupabaseErrorApp(message: e.toString(), error: 'Configuration Error'));
     return;
  }

  // Initialize Supabase using values from AuthConstants
  log.info('Initializing Supabase: ${AuthConstants.supabaseUrl}');
  try {
    await Supabase.initialize(
      // *** Read directly from AuthConstants ***
      url: AuthConstants.supabaseUrl,
      anonKey: AuthConstants.supabaseAnonKey,
      debug: kDebugMode,
      // authOptions: AuthClientOptions( // Pass custom auth options
      //   store: SecureStorageAdapter(), // Use your existing adapter
      // ), // Reverted: Rely on default storage for now
    );
    log.info('Supabase initialized successfully.'); // Reverted log message

    // No need for explicit SupabaseAuthUI initialization in newer versions
    log.info('SupabaseAuthUI ready to use.');

    // DEBUG: Print the redirectUrl for verification
    print('>>> AuthConstants.redirectUrl = ${AuthConstants.redirectUrl}');
    log.info('AuthConstants.redirectUrl = ${AuthConstants.redirectUrl}');
  } catch (e, stackTrace) {
    log.severe('FATAL: Supabase Initialization Error: $e', e, stackTrace);
    runApp(SupabaseErrorApp(message: 'Failed to initialize Supabase.', error: e));
    return;
  }

  // 7. Initialize Global Error Handler (if it has setup logic)
  ErrorHandler.initialize(); // Assuming a static init method
  log.info('Global error handler initialized.');

  // 8. Create Service Instances
  log.info('Creating services...');
  // Use try-catch if service constructors can fail
  final supabaseClient = Supabase.instance.client;
  final supabaseService = SupabaseService(supabaseClient); // Pass client instance
  final authService = AuthService(supabaseClient, supabaseService); // Pass both client and service
  final connectivityService = ConnectivityService(); // Initialize connectivity listener
  final databaseService = DatabaseService(supabaseClient); // Pass client instance
  // final analyticsService = AnalyticsService();
  // final performanceService = PerformanceService();
  // final localStorageService = LocalStorageService(); await localStorageService.init();
  // final realtimeService = RealtimeService();
  // final syncService = SyncService(...);
  log.info('Services created.');

  // 9. Run the App with Providers
  log.info('Setting up providers and running app...');
  runApp(
    MultiRepositoryProvider(
      providers: [
        // Provide singleton instances of services
        RepositoryProvider.value(value: supabaseClient), // Provide Supabase client directly
        RepositoryProvider<SupabaseServiceInterface>.value(value: supabaseService),
        RepositoryProvider<SupabaseService>.value(value: supabaseService), // Also provide concrete implementation
        RepositoryProvider<AuthServiceInterface>.value(value: authService),
        RepositoryProvider<AuthService>.value(value: authService), // Also provide concrete implementation
        RepositoryProvider.value(value: connectivityService),
        RepositoryProvider.value(value: databaseService),
        RepositoryProvider<TournamentApiService>(create: (context) => TournamentApiService()), // Added
        RepositoryProvider<SchedulingApiService>(create: (context) => SchedulingApiService()), // Added
        RepositoryProvider<VenueService>(create: (context) => VenueService()), // Added
        RepositoryProvider<FieldService>(create: (context) => FieldService()), // Added
        // RepositoryProvider.value(value: analyticsService),
        // RepositoryProvider.value(value: performanceService),
        // RepositoryProvider.value(value: localStorageService),
        // RepositoryProvider.value(value: realtimeService),
        // RepositoryProvider.value(value: syncService),
      ],
      child: MultiBlocProvider(
        providers: [
          // Create and provide BLoCs, injecting services via context.read
          BlocProvider<AuthBloc>(
            create: (context) => AuthBloc(
              authService: context.read<AuthServiceInterface>(),
            )..add(AuthAppStarted()), // Check auth state immediately
            lazy: false, // Ensure AuthBloc is created and listening from start
          ),
          BlocProvider<ProfileBloc>(
            create: (context) => ProfileBloc(
              authService: context.read<AuthServiceInterface>(),
              supabaseService: context.read<SupabaseServiceInterface>(), // Use interface
              authBloc: context.read<AuthBloc>(), // ProfileBloc listens to AuthBloc
            ),
            lazy: false, // Load profile based on initial AuthBloc state
          ),
          // Provide other feature BLoCs
          BlocProvider<TournamentBloc>(
            create: (context) => TournamentBloc(tournamentApiService: context.read<TournamentApiService>()),
          ),
          BlocProvider<SchedulingBloc>(
            create: (context) => SchedulingBloc(supabaseService: context.read<SupabaseService>()),
          ),
          BlocProvider<BracketBloc>(
            create: (context) => BracketBloc(supabaseService: context.read<SupabaseService>()),
          ),
          BlocProvider<RefereeBloc>(
            create: (context) => RefereeBloc(supabaseService: context.read<SupabaseService>()),
          ),
          BlocProvider<TeamBloc>(
            create: (context) => TeamBloc(supabaseService: context.read<SupabaseService>()),
          ),
          BlocProvider<MatchBloc>(
            create: (context) => MatchBloc(supabaseService: context.read<SupabaseService>()),
          ),
          // ... other BLoCs ...
        ],
        child: const TournamentSchedulerApp(), // Your root App widget
      ),
    ),
  );
}
