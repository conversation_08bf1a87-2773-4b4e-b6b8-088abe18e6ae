import 'package:flutter/foundation.dart';

@immutable
class FieldType {
  final String id; // UUID
  final String name; // e.g., "4v4 Mini", "7v7"
  final String? description;
  final DateTime? createdAt;

  const FieldType({
    required this.id,
    required this.name,
    this.description,
    this.createdAt,
  });

  factory FieldType.fromJson(Map<String, dynamic> json) {
    return FieldType(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'] as String)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'created_at': createdAt?.toIso8601String(),
    };
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is FieldType &&
          runtimeType == other.runtimeType &&
          id == other.id &&
          name == other.name;

  @override
  int get hashCode => id.hashCode ^ name.hashCode;
}
