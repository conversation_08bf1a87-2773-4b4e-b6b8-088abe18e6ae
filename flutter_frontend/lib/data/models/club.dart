import 'package:equatable/equatable.dart';

class Club extends Equatable {
  final String id;
  final String name;
  final String? website;
  final String? logoUrl;
  final String? city;
  final String? state;
  final String? country;
  final bool? isVerified;
  final String? verificationNotes;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const Club({
    required this.id,
    required this.name,
    this.website,
    this.logoUrl,
    this.city,
    this.state,
    this.country,
    this.isVerified,
    this.verificationNotes,
    this.createdAt,
    this.updatedAt,
  });

  @override
  List<Object?> get props => [
    id, 
    name, 
    website, 
    logoUrl, 
    city, 
    state, 
    country, 
    isVerified, 
    verificationNotes, 
    createdAt, 
    updatedAt
  ];

  factory Club.fromJson(Map<String, dynamic> json) {
    return Club(
      id: json['id'] as String,
      name: json['name'] as String,
      website: json['website'] as String?,
      logoUrl: json['logo_url'] as String?,
      city: json['city'] as String?,
      state: json['state'] as String?,
      country: json['country'] as String?,
      isVerified: json['is_verified'] as bool?,
      verificationNotes: json['verification_notes'] as String?,
      createdAt: json['created_at'] != null ? DateTime.tryParse(json['created_at'] as String) : null,
      updatedAt: json['updated_at'] != null ? DateTime.tryParse(json['updated_at'] as String) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'website': website,
      'logo_url': logoUrl,
      'city': city,
      'state': state,
      'country': country,
      'is_verified': isVerified,
      'verification_notes': verificationNotes,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  Club copyWith({
    String? id,
    String? name,
    String? website,
    String? logoUrl,
    String? city,
    String? state,
    String? country,
    bool? isVerified,
    String? verificationNotes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Club(
      id: id ?? this.id,
      name: name ?? this.name,
      website: website ?? this.website,
      logoUrl: logoUrl ?? this.logoUrl,
      city: city ?? this.city,
      state: state ?? this.state,
      country: country ?? this.country,
      isVerified: isVerified ?? this.isVerified,
      verificationNotes: verificationNotes ?? this.verificationNotes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
