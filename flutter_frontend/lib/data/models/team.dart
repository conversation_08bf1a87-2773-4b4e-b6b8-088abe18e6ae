import 'package:flutter/foundation.dart';

@immutable
class Team {
  final String id;
  final String name;
  final String tournamentId; // Assuming a team is scoped to a tournament in this context
  // Add other fields like club_id, coach_id, roster, etc., if needed

  const Team({
    required this.id,
    required this.name,
    required this.tournamentId,
  });

  Team copyWith({
    String? id,
    String? name,
    String? tournamentId,
  }) {
    return Team(
      id: id ?? this.id,
      name: name ?? this.name,
      tournamentId: tournamentId ?? this.tournamentId,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'tournament_id': tournamentId, // Match DB/RPC key
    };
  }

  factory Team.fromJson(Map<String, dynamic> json) {
    return Team(
      id: json['id'] as String,
      name: json['name'] as String,
      tournamentId: json['tournament_id'] as String, // Match DB/RPC key
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Team &&
          runtimeType == other.runtimeType &&
          id == other.id &&
          name == other.name &&
          tournamentId == other.tournamentId;

  @override
  int get hashCode => id.hashCode ^ name.hashCode ^ tournamentId.hashCode;
}
