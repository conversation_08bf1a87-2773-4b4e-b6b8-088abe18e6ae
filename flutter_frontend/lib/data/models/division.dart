// soccer_frontend/data/models/division.dart
import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart'; // For @immutable

@immutable
class Division extends Equatable {
  final String id; // Unique ID for this division instance (could be UUID)
  final String name; // e.g., "Boys U10 Premier", "Girls U12 Recreational"
  final String ageGroup; // e.g., "U10", "U12", "Adult"
  final String gender; // e.g., "Boys", "Girls", "Coed", "Mixed"
  final String competitiveLevel; // e.g., "Premier", "Select", "Recreational", "Open"
  final String playFormat; // e.g., "4v4", "7v7", "9v9", "11v11"
  final double registrationFee; // Fee for this specific division
  final String? notes; // Any specific notes for this division

  const Division({
    required this.id,
    required this.name,
    required this.ageGroup,
    required this.gender,
    required this.competitiveLevel,
    required this.playFormat,
    required this.registrationFee,
    this.notes,
  });

  Division copyWith({
    String? id,
    String? name,
    String? ageGroup,
    String? gender,
    String? competitiveLevel,
    String? playFormat,
    double? registrationFee,
    String? notes,
  }) {
    return Division(
      id: id ?? this.id,
      name: name ?? this.name,
      ageGroup: ageGroup ?? this.ageGroup,
      gender: gender ?? this.gender,
      competitiveLevel: competitiveLevel ?? this.competitiveLevel,
      playFormat: playFormat ?? this.playFormat,
      registrationFee: registrationFee ?? this.registrationFee,
      notes: notes ?? this.notes,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'age_group': ageGroup,
      'gender': gender,
      'competitive_level': competitiveLevel,
      'play_format': playFormat,
      'registration_fee': registrationFee,
      'notes': notes,
    };
  }

  factory Division.fromJson(Map<String, dynamic> json) {
    return Division(
      id: json['id'] as String,
      name: json['name'] as String,
      ageGroup: json['age_group'] as String,
      gender: json['gender'] as String,
      competitiveLevel: json['competitive_level'] as String,
      playFormat: json['play_format'] as String,
      registrationFee: (json['registration_fee'] as num).toDouble(),
      notes: json['notes'] as String?,
    );
  }

  @override
  List<Object?> get props => [
        id,
        name,
        ageGroup,
        gender,
        competitiveLevel,
        playFormat,
        registrationFee,
        notes,
      ];
}