// lib/src/models/user_profile.dart
import 'package:equatable/equatable.dart';
import 'package:logging/logging.dart';

class UserProfile extends Equatable {
  final String id;
  final String? email;
  final String? role; // Ensure 'role' column exists in DB
  final String? firstName;
  final String? lastName;
  final String? phoneNumber;
  final String? managingClubId; // For Club Admins
  final DateTime? createdAt;
  final DateTime? updatedAt;

  String get fullName {
    final fn = firstName ?? '';
    final ln = lastName ?? '';
    if (fn.isNotEmpty && ln.isNotEmpty) return '$fn $ln';
    if (fn.isNotEmpty) return fn;
    if (ln.isNotEmpty) return ln;
    return '';
  }

  String get displayName => fullName.isNotEmpty ? fullName : email ?? id;

  const UserProfile({
    required this.id,
    this.email,
    this.role,
    this.firstName,
    this.lastName,
    this.phoneNumber,
    this.managingClubId,
    this.createdAt,
    this.updatedAt,
  });

  factory UserProfile.fromJson(Map<String, dynamic> json) {
    final log = Logger('UserProfile.fromJson');
    log.fine('Parsing: $json');
    try {
      return UserProfile(
        id: json['id'] as String,
        email: json['email'] as String?,
        role: json['role'] as String?, // ** MATCH YOUR DB COLUMN **
        firstName: json['first_name'] as String?,
        lastName: json['last_name'] as String?,
        phoneNumber: json['phone_number'] as String?,
        managingClubId: json['managing_club_id'] as String?,
        createdAt: json['created_at'] == null ? null : DateTime.tryParse(json['created_at'] as String),
        updatedAt: json['updated_at'] == null ? null : DateTime.tryParse(json['updated_at'] as String),
      );
    } catch (e, s) {
      log.severe('ERROR parsing UserProfile JSON: $e', e, s);
      // Return a default or throw, depending on desired handling
      // Returning a default might mask issues, throwing is often better.
      throw FormatException('Error parsing UserProfile: $e');
    }
  }

  Map<String, dynamic> toJson() => {
        // Used for updates/inserts - ensure keys match DB columns
        'id': id,
        'email': email,
        'role': role,
        'first_name': firstName,
        'last_name': lastName,
        'phone_number': phoneNumber,
        'managing_club_id': managingClubId,
        'created_at': createdAt?.toIso8601String(),
        'updated_at': updatedAt?.toIso8601String(),
      };

  @override
  List<Object?> get props => [
        id, email, role, firstName, lastName, phoneNumber,
        managingClubId, createdAt, updatedAt
      ];
}
