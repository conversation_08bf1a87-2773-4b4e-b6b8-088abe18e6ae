import 'package:equatable/equatable.dart';

class ClubAffiliation extends Equatable {
  final String clubId;
  final String clubName;
  final String status; // e.g., 'pending_approval', 'approved', 'rejected', 'revoked'
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const ClubAffiliation({
    required this.clubId,
    required this.clubName,
    required this.status,
    this.createdAt,
    this.updatedAt,
  });

  @override
  List<Object?> get props => [clubId, clubName, status, createdAt, updatedAt];

  factory ClubAffiliation.fromJson(Map<String, dynamic> json) {
    // This factory will be used when fetching data from Supabase.
    // It assumes the query joins club_directors with clubs and selects club_name.
    return ClubAffiliation(
      clubId: json['club_id'] as String,
      // Assuming the joined query will provide 'club_name' directly
      clubName: json['club_name'] as String? ?? 'Unknown Club', 
      status: json['status'] as String,
      createdAt: json['created_at'] != null ? DateTime.tryParse(json['created_at'] as String) : null,
      updatedAt: json['updated_at'] != null ? DateTime.tryParse(json['updated_at'] as String) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'club_id': clubId,
      'club_name': clubName,
      'status': status,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }
}
