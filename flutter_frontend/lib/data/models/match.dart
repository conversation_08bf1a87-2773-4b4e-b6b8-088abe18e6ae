import 'package:flutter/foundation.dart';

@immutable
class Match {
  final String id;
  final String tournamentId;
  final String homeTeamId;
  final String awayTeamId;
  final String? scheduledFieldId;
  final DateTime? startTime;
  final int durationInMinutes;
  final String status;
  // Add other fields like team1_score, team2_score, winner_id, etc., if needed by the app
  // final int? team1Score;
  // final int? team2Score;
  // final String? winnerId;


  const Match({
    required this.id,
    required this.tournamentId,
    required this.homeTeamId,
    required this.awayTeamId,
    this.scheduledFieldId,
    this.startTime,
    required this.durationInMinutes,
    required this.status,
    // this.team1Score,
    // this.team2Score,
    // this.winnerId,
  });

  Match copyWith({
    String? id,
    String? tournamentId,
    String? homeTeamId,
    String? awayTeamId,
    String? scheduledFieldId,
    DateTime? startTime,
    int? durationInMinutes,
    String? status,
    // int? team1Score,
    // int? team2Score,
    // ValueGetter<String?>? winnerId, // For nullable fields that can be explicitly set to null
  }) {
    return Match(
      id: id ?? this.id,
      tournamentId: tournamentId ?? this.tournamentId,
      homeTeamId: homeTeamId ?? this.homeTeamId,
      awayTeamId: awayTeamId ?? this.awayTeamId,
      scheduledFieldId: scheduledFieldId ?? this.scheduledFieldId,
      startTime: startTime ?? this.startTime,
      durationInMinutes: durationInMinutes ?? this.durationInMinutes,
      status: status ?? this.status,
      // team1Score: team1Score ?? this.team1Score,
      // team2Score: team2Score ?? this.team2Score,
      // winnerId: winnerId != null ? winnerId() : this.winnerId,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'tournament_id': tournamentId, // Match DB/RPC key
      'home_team_id': homeTeamId,   // Match DB/RPC key
      'away_team_id': awayTeamId,   // Match DB/RPC key
      'scheduled_field_id': scheduledFieldId,
      'start_time': startTime?.toIso8601String(),
      'duration_in_minutes': durationInMinutes,
      'status': status,
      // 'team1_score': team1Score,
      // 'team2_score': team2Score,
      // 'winner_id': winnerId,
    };
  }

  factory Match.fromJson(Map<String, dynamic> json) {
    return Match(
      id: json['id'] as String,
      tournamentId: json['tournament_id'] as String, // Match DB/RPC key
      homeTeamId: json['home_team_id'] as String,   // Match DB/RPC key
      awayTeamId: json['away_team_id'] as String,   // Match DB/RPC key
      scheduledFieldId: json['scheduled_field_id'] as String?,
      startTime: json['start_time'] == null
          ? null
          : DateTime.parse(json['start_time'] as String),
      durationInMinutes: json['duration_in_minutes'] as int,
      status: json['status'] as String,
      // team1Score: json['team1_score'] as int?,
      // team2Score: json['team2_score'] as int?,
      // winnerId: json['winner_id'] as String?,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Match &&
          runtimeType == other.runtimeType &&
          id == other.id &&
          tournamentId == other.tournamentId &&
          homeTeamId == other.homeTeamId &&
          awayTeamId == other.awayTeamId &&
          scheduledFieldId == other.scheduledFieldId &&
          startTime == other.startTime &&
          durationInMinutes == other.durationInMinutes &&
          status == other.status;
          // && team1Score == other.team1Score
          // && team2Score == other.team2Score
          // && winnerId == other.winnerId;


  @override
  int get hashCode =>
      id.hashCode ^
      tournamentId.hashCode ^
      homeTeamId.hashCode ^
      awayTeamId.hashCode ^
      scheduledFieldId.hashCode ^
      startTime.hashCode ^
      durationInMinutes.hashCode ^
      status.hashCode;
      // ^ team1Score.hashCode
      // ^ team2Score.hashCode
      // ^ winnerId.hashCode;
}
