import 'package:flutter/foundation.dart';

@immutable
class Field {
  final String? id;
  final String venueId;
  final String nameOrNumber;
  final String? surfaceType; // Renamed from fieldTypeName, e.g., "Grass", "Turf"
  final String? size; // e.g., "Full", "7v7", "9v9"
  final String? fieldTypeId; // UUID from DB
  final String? status; // Default: "Open"
  final DateTime? fieldStartTime; // Overrides venue time
  final DateTime? fieldEndTime; // Overrides venue time
  final String? notes;
  final String? locationType; // "Indoor" or "Outdoor"
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const Field({
    this.id,
    required this.venueId,
    required this.nameOrNumber,
    this.surfaceType, // Renamed
    this.size,
    this.fieldTypeId,
    this.status = 'Open',
    this.fieldStartTime,
    this.fieldEndTime,
    this.notes,
    this.locationType,
    this.createdAt,
    this.updatedAt,
  });

  Field copyWith({
    String? id,
    String? venueId,
    String? nameOrNumber,
    String? surfaceType, // Renamed
    String? size,
    String? fieldTypeId,
    String? status,
    DateTime? fieldStartTime,
    DateTime? fieldEndTime,
    String? notes,
    String? locationType,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Field(
      id: id ?? this.id,
      venueId: venueId ?? this.venueId,
      nameOrNumber: nameOrNumber ?? this.nameOrNumber,
      surfaceType: surfaceType ?? this.surfaceType, // Renamed
      size: size ?? this.size,
      fieldTypeId: fieldTypeId ?? this.fieldTypeId,
      status: status ?? this.status,
      fieldStartTime: fieldStartTime ?? this.fieldStartTime,
      fieldEndTime: fieldEndTime ?? this.fieldEndTime,
      notes: notes ?? this.notes,
      locationType: locationType ?? this.locationType,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'venue_id': venueId, // Match DB/RPC key
      'name_number': nameOrNumber, // Match DB/RPC key
      'surface_type': surfaceType, // Renamed and matches DB key
      'size': size,
      'field_type_id': fieldTypeId, // Match DB/RPC key
      'field_status': status,
      'field_start_time': fieldStartTime != null
          ? '${fieldStartTime!.hour.toString().padLeft(2, '0')}:${fieldStartTime!.minute.toString().padLeft(2, '0')}:00'
          : null,
      'field_end_time': fieldEndTime != null
          ? '${fieldEndTime!.hour.toString().padLeft(2, '0')}:${fieldEndTime!.minute.toString().padLeft(2, '0')}:00'
          : null,
      'notes': notes,
      'location_type': locationType,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  factory Field.fromJson(Map<String, dynamic> json) {
    // This function now expects 'field_type_name' (e.g., "4v4 Mini") to be present in the json map
    // if size is to be derived from it. The BLoC is responsible for adding this.
    String? extractSizeFromFullFieldTypeName(String? fullFieldTypeName) {
      if (fullFieldTypeName == null) return null;
      if (fullFieldTypeName.contains('4v4')) return '4v4';
      if (fullFieldTypeName.contains('7v7')) return '7v7';
      if (fullFieldTypeName.contains('9v9')) return '9v9';
      if (fullFieldTypeName.contains('11v11')) return '11v11';
      // Add more specific extractions if needed, e.g., "Full-Size" -> "11v11"
      return null; // Or return fullFieldTypeName if no specific extraction pattern matches
    }

    return Field(
      id: json['id'] as String?,
      venueId: json['venue_id'] as String,
      nameOrNumber: json['name_number'] as String,
      surfaceType: json['surface_type'] as String?, // Directly from DB
      // Size is derived from 'field_type_name' (e.g. "4v4 Mini") if 'size' is not directly in json.
      // 'field_type_name' is expected to be injected by the BLoC.
      size: json['size'] as String? ?? extractSizeFromFullFieldTypeName(json['field_type_name'] as String?),
      fieldTypeId: json['field_type_id'] as String?,
      status: json['field_status'] as String?,
      // Handle both field_start_time (from API) and operational_start_time (from DB)
      fieldStartTime: (json['field_start_time'] ?? json['operational_start_time']) != null
          ? DateTime.parse((json['field_start_time'] ?? json['operational_start_time']) as String)
          : null,
      fieldEndTime: (json['field_end_time'] ?? json['operational_end_time']) != null
          ? DateTime.parse((json['field_end_time'] ?? json['operational_end_time']) as String)
          : null,
      notes: json['notes'] as String?,
      locationType: json['location_type'] as String?,
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'] as String)
          : null,
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'] as String)
          : null,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Field &&
          runtimeType == other.runtimeType &&
          id == other.id &&
          venueId == other.venueId &&
          nameOrNumber == other.nameOrNumber &&
          surfaceType == other.surfaceType && // Renamed
          size == other.size &&
          fieldTypeId == other.fieldTypeId &&
          status == other.status &&
          fieldStartTime == other.fieldStartTime &&
          fieldEndTime == other.fieldEndTime &&
          notes == other.notes &&
          locationType == other.locationType;

  @override
  int get hashCode =>
      id.hashCode ^
      venueId.hashCode ^
      nameOrNumber.hashCode ^
      surfaceType.hashCode ^ // Renamed
      size.hashCode ^
      fieldTypeId.hashCode ^
      status.hashCode ^
      fieldStartTime.hashCode ^
      fieldEndTime.hashCode ^
      notes.hashCode ^
      locationType.hashCode;
}
