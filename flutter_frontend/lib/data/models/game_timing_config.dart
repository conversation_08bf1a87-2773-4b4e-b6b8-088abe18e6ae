class GameTimingConfig {
  final String ageGroup;
  final String fieldSize;
  final int gameDurationMinutes;
  final int halftimeMinutes;
  final int bufferTimeMinutes;
  final String notes;

  const GameTimingConfig({
    required this.ageGroup,
    required this.fieldSize,
    required this.gameDurationMinutes,
    required this.halftimeMinutes,
    required this.bufferTimeMinutes,
    required this.notes,
  });

  int get totalTimeSlotMinutes => 
      gameDurationMinutes + halftimeMinutes + bufferTimeMinutes;

  static GameTimingConfig? getConfigurationFor(String ageGroup, String fieldSize) {
    // Common age group/field size pairings with recommended durations
    final defaults = {
      'U8_4v4': GameTimingConfig(
        ageGroup: 'U8',
        fieldSize: '4v4',
        gameDurationMinutes: 40,
        halftimeMinutes: 5,
        bufferTimeMinutes: 5,
        notes: 'Recommended U8/4v4 timing based on USYS guidelines',
      ),
      'U10_7v7': GameTimingConfig(
        ageGroup: 'U10',
        fieldSize: '7v7',
        gameDurationMinutes: 50,
        halftimeMinutes: 5,
        bufferTimeMinutes: 10,
        notes: 'Recommended U10/7v7 timing based on USYS guidelines',
      ),
      'U12_9v9': GameTimingConfig(
        ageGroup: 'U12',
        fieldSize: '9v9',
        gameDurationMinutes: 60,
        halftimeMinutes: 10,
        bufferTimeMinutes: 10,
        notes: 'Recommended U12/9v9 timing based on USYS guidelines',
      ),
      'U14_11v11': GameTimingConfig(
        ageGroup: 'U14',
        fieldSize: '11v11',
        gameDurationMinutes: 70,
        halftimeMinutes: 10,
        bufferTimeMinutes: 10,
        notes: 'Recommended U14/11v11 timing based on USYS guidelines',
      ),
      'U19_11v11': GameTimingConfig(
        ageGroup: 'U19',
        fieldSize: '11v11',
        gameDurationMinutes: 90,
        halftimeMinutes: 15,
        bufferTimeMinutes: 15,
        notes: 'Recommended U19/11v11 timing based on FIFA guidelines',
      ),
      'Adult_11v11': GameTimingConfig(
        ageGroup: 'Adult',
        fieldSize: '11v11',
        gameDurationMinutes: 90,
        halftimeMinutes: 15,
        bufferTimeMinutes: 15,
        notes: 'Standard adult match timing',
      ),
    };

    return defaults['${ageGroup}_$fieldSize'];
  }

  GameTimingConfig copyWith({
    String? ageGroup,
    String? fieldSize,
    int? gameDurationMinutes,
    int? halftimeMinutes,
    int? bufferTimeMinutes,
    String? notes,
  }) {
    return GameTimingConfig(
      ageGroup: ageGroup ?? this.ageGroup,
      fieldSize: fieldSize ?? this.fieldSize,
      gameDurationMinutes: gameDurationMinutes ?? this.gameDurationMinutes,
      halftimeMinutes: halftimeMinutes ?? this.halftimeMinutes,
      bufferTimeMinutes: bufferTimeMinutes ?? this.bufferTimeMinutes,
      notes: notes ?? this.notes,
    );
  }

  @override
  String toString() {
    return 'GameTimingConfig(ageGroup: $ageGroup, fieldSize: $fieldSize, gameDurationMinutes: $gameDurationMinutes, halftimeMinutes: $halftimeMinutes, bufferTimeMinutes: $bufferTimeMinutes, notes: $notes)';
  }

  Map<String, dynamic> toJson() {
    return {
      'ageGroup': ageGroup,
      'fieldSize': fieldSize,
      'gameDurationMinutes': gameDurationMinutes,
      'halftimeMinutes': halftimeMinutes,
      'bufferTimeMinutes': bufferTimeMinutes,
      'notes': notes,
    };
  }

  factory GameTimingConfig.fromJson(Map<String, dynamic> json) {
    return GameTimingConfig(
      ageGroup: json['ageGroup'] as String,
      fieldSize: json['fieldSize'] as String,
      gameDurationMinutes: json['gameDurationMinutes'] as int,
      halftimeMinutes: json['halftimeMinutes'] as int,
      bufferTimeMinutes: json['bufferTimeMinutes'] as int,
      notes: json['notes'] as String,
    );
  }
}
