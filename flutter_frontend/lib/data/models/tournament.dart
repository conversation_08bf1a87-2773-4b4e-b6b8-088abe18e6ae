// soccer_frontend/data/models/tournament.dart
import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart'; // For @immutable, @Deprecated, listEquals, mapEquals, setEquals
import 'game_timing_config.dart'; // Ensure this is imported
import 'division.dart'; // << NEW IMPORT

@immutable
class Tournament extends Equatable {
  final String? id; // Null for new tournaments
  final String name;
  final String sportType;
  final DateTime startDate;
  final DateTime endDate;
  final String status; // e.g., 'planning', 'registration_open', 'live', 'completed'
  final String? city;
  final String? state;
  final DateTime? registrationDeadline;

  // Division-based structure
  final List<Division>? divisions;

  final int? gamesPerTeam; // This could potentially move to Division level in future
  final String? managingClubId; // If created via an affiliated club
  final String? rules;
  final String? refundPolicy;
  final String? directorName;
  final String? directorEmail;
  final String? directorPhone;
  final String? tournamentFormat;

  // New fields for Additional Info screen
  final String? description;
  final String? facebookUrl;
  final String? twitterUrl;
  final String? instagramUrl;
  final String? websiteUrl;
  final DateTime? earlyBirdDeadline;
  final double? earlyBirdDiscount;   // Changed from earlyBirdFee to discount
  final double? lateFeeAmount;       // Changed from lateFee to amount
  final DateTime? lateRegistrationStartDate; // Changed from lateRegistrationStart
  final int? maxTeams;
  // @Deprecated('Game duration is now per age/field in gameTimingConfigurations')
  // final int? gameDurationMinutes; // This field should be removed if it existed as a global setting

  final int? minRosterSize;
  final int? maxRosterSize;
  final String? awards;
  final bool? hasConcessions;
  final bool? hasMerchandise;
  final bool? hasMedical;
  final String? admissionFee;
  final String? parkingInfo;
  final String? spectatorInfo;
  final String? secondaryContactName;
  final String? secondaryContactEmail;
  final String? secondaryContactPhone;
  final String? secondaryContactRole;

  // Game timing configurations map
  final Map<String, GameTimingConfig>? gameTimingConfigurations; // << Existing field

  // Selected venue and field IDs for linking
  final Set<String>? selectedVenueIds;
  final Set<String>? selectedFieldIds;

  const Tournament({
    this.id,
    required this.name,
    required this.sportType,
    required this.startDate,
    required this.endDate,
    required this.status,
    this.city,
    this.state,
    this.registrationDeadline,
    this.divisions,
    this.gamesPerTeam,
    this.managingClubId,
    this.rules,
    this.refundPolicy,
    this.directorName,
    this.directorEmail,
    this.directorPhone,
    this.tournamentFormat,
    this.description,
    this.facebookUrl,
    this.twitterUrl,
    this.instagramUrl,
    this.websiteUrl,
    this.earlyBirdDeadline,
    this.earlyBirdDiscount,
    this.lateFeeAmount,
    this.lateRegistrationStartDate,
    this.maxTeams,
    // this.gameDurationMinutes, // If this existed globally, ensure it's removed
    this.minRosterSize,
    this.maxRosterSize,
    this.awards,
    this.hasConcessions,
    this.hasMerchandise,
    this.hasMedical,
    this.admissionFee,
    this.parkingInfo,
    this.spectatorInfo,
    this.secondaryContactName,
    this.secondaryContactEmail,
    this.secondaryContactPhone,
    this.secondaryContactRole,
    this.gameTimingConfigurations,
    this.selectedVenueIds,
    this.selectedFieldIds,
  });

  Tournament copyWith({
    String? id,
    String? name,
    String? sportType,
    DateTime? startDate,
    DateTime? endDate,
    String? status,
    String? city,
    String? state,
    DateTime? registrationDeadline,
    List<Division>? divisions,
    int? gamesPerTeam,
    String? managingClubId,
    String? rules,
    String? refundPolicy,
    String? directorName,
    String? directorEmail,
    String? directorPhone,
    String? tournamentFormat,
    String? description,
    String? facebookUrl,
    String? twitterUrl,
    String? instagramUrl,
    String? websiteUrl,
    DateTime? earlyBirdDeadline,
    double? earlyBirdDiscount,
    double? lateFeeAmount,
    DateTime? lateRegistrationStartDate,
    int? maxTeams,
    // int? gameDurationMinutes, // If this existed globally, ensure it's removed
    int? minRosterSize,
    int? maxRosterSize,
    String? awards,
    bool? hasConcessions,
    bool? hasMerchandise,
    bool? hasMedical,
    String? admissionFee,
    String? parkingInfo,
    String? spectatorInfo,
    String? secondaryContactName,
    String? secondaryContactEmail,
    String? secondaryContactPhone,
    String? secondaryContactRole,
    Map<String, GameTimingConfig>? gameTimingConfigurations,
    Set<String>? selectedVenueIds,
    Set<String>? selectedFieldIds,
  }) {
    return Tournament(
      id: id ?? this.id,
      name: name ?? this.name,
      sportType: sportType ?? this.sportType,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      status: status ?? this.status,
      city: city ?? this.city,
      state: state ?? this.state,
      registrationDeadline: registrationDeadline ?? this.registrationDeadline,
      divisions: divisions ?? this.divisions,
      gamesPerTeam: gamesPerTeam ?? this.gamesPerTeam,
      managingClubId: managingClubId ?? this.managingClubId,
      rules: rules ?? this.rules,
      refundPolicy: refundPolicy ?? this.refundPolicy,
      directorName: directorName ?? this.directorName,
      directorEmail: directorEmail ?? this.directorEmail,
      directorPhone: directorPhone ?? this.directorPhone,
      tournamentFormat: tournamentFormat ?? this.tournamentFormat,
      description: description ?? this.description,
      facebookUrl: facebookUrl ?? this.facebookUrl,
      twitterUrl: twitterUrl ?? this.twitterUrl,
      instagramUrl: instagramUrl ?? this.instagramUrl,
      websiteUrl: websiteUrl ?? this.websiteUrl,
      earlyBirdDeadline: earlyBirdDeadline ?? this.earlyBirdDeadline,
      earlyBirdDiscount: earlyBirdDiscount ?? this.earlyBirdDiscount,
      lateFeeAmount: lateFeeAmount ?? this.lateFeeAmount,
      lateRegistrationStartDate: lateRegistrationStartDate ?? this.lateRegistrationStartDate,
      maxTeams: maxTeams ?? this.maxTeams,
      // gameDurationMinutes: gameDurationMinutes ?? this.gameDurationMinutes, // If removed, this line is gone
      minRosterSize: minRosterSize ?? this.minRosterSize,
      maxRosterSize: maxRosterSize ?? this.maxRosterSize,
      awards: awards ?? this.awards,
      hasConcessions: hasConcessions ?? this.hasConcessions,
      hasMerchandise: hasMerchandise ?? this.hasMerchandise,
      hasMedical: hasMedical ?? this.hasMedical,
      admissionFee: admissionFee ?? this.admissionFee,
      parkingInfo: parkingInfo ?? this.parkingInfo,
      spectatorInfo: spectatorInfo ?? this.spectatorInfo,
      secondaryContactName: secondaryContactName ?? this.secondaryContactName,
      secondaryContactEmail: secondaryContactEmail ?? this.secondaryContactEmail,
      secondaryContactPhone: secondaryContactPhone ?? this.secondaryContactPhone,
      secondaryContactRole: secondaryContactRole ?? this.secondaryContactRole,
      gameTimingConfigurations: gameTimingConfigurations ?? this.gameTimingConfigurations,
      selectedVenueIds: selectedVenueIds ?? this.selectedVenueIds,
      selectedFieldIds: selectedFieldIds ?? this.selectedFieldIds,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'sport_type': sportType,
      'start_date': startDate.toIso8601String(),
      'end_date': endDate.toIso8601String(),
      'status': status,
      'city': city,
      'state': state,
      'registration_deadline': registrationDeadline?.toIso8601String(),
      'divisions': divisions?.map((d) => d.toJson()).toList(),
      'games_per_team': gamesPerTeam,
      'managing_club_id': managingClubId,
      'rules': rules,
      'refund_policy': refundPolicy,
      'director_name': directorName,
      'director_email': directorEmail,
      'director_phone': directorPhone,
      'tournament_format': tournamentFormat,
      'description': description,
      'facebook_url': facebookUrl,
      'twitter_url': twitterUrl,
      'instagram_url': instagramUrl,
      'website_url': websiteUrl,
      'early_bird_deadline': earlyBirdDeadline?.toIso8601String(),
      'early_bird_discount': earlyBirdDiscount,
      'late_fee_amount': lateFeeAmount,
      'late_registration_start_date': lateRegistrationStartDate?.toIso8601String(),
      'max_teams': maxTeams,
      // 'game_duration_minutes': gameDurationMinutes, // If removed, this line is gone
      'min_roster_size': minRosterSize,
      'max_roster_size': maxRosterSize,
      'awards': awards,
      'has_concessions': hasConcessions,
      'has_merchandise': hasMerchandise,
      'has_medical': hasMedical,
      'admission_fee': admissionFee,
      'parking_info': parkingInfo,
      'spectator_info': spectatorInfo,
      'secondary_contact_name': secondaryContactName,
      'secondary_contact_email': secondaryContactEmail,
      'secondary_contact_phone': secondaryContactPhone,
      'secondary_contact_role': secondaryContactRole,
      // Game timing configurations
      'game_timing_configurations': gameTimingConfigurations?.map(
        (key, value) => MapEntry(key, value.toJson()),
      ),
      // Selected venue and field IDs for linking
      'selected_venue_ids': selectedVenueIds?.toList(),
      'selected_field_ids': selectedFieldIds?.toList(),
    };
  }

  factory Tournament.fromJson(Map<String, dynamic> json) {
    // Extract custom_settings for fields stored there
    final customSettings = json['custom_settings'] as Map<String, dynamic>? ?? {};

    return Tournament(
      id: json['id'] as String?,
      name: json['name'] as String? ?? 'Unnamed Tournament',
      sportType: customSettings['sport_type'] as String? ??
                 customSettings['sportType'] as String? ??
                 json['sport_type'] as String? ??
                 'Soccer',
      startDate: DateTime.parse(json['start_date'] as String? ?? DateTime.now().toIso8601String()),
      endDate: DateTime.parse(json['end_date'] as String? ?? DateTime.now().add(const Duration(days: 1)).toIso8601String()),
      status: json['status'] as String? ?? 'planning',
      city: json['location_city'] as String? ?? json['city'] as String?,
      state: json['location_state'] as String? ?? json['state'] as String?,
      registrationDeadline: json['registration_deadline'] != null
          ? DateTime.parse(json['registration_deadline'] as String)
          : null,
      divisions: (json['divisions'] as List<dynamic>?)
          ?.map((d) => Division.fromJson(d as Map<String, dynamic>))
          .toList(), // << NEW: Deserialize divisions
      gamesPerTeam: json['min_games_guaranteed'] as int? ?? json['games_per_team'] as int?,
      managingClubId: json['managing_club_id'] as String?,
      rules: json['rules_text'] as String? ?? json['rules'] as String?,
      refundPolicy: customSettings['refund_policy'] as String? ?? json['refund_policy'] as String?,
      directorName: customSettings['director_name'] as String? ?? json['director_name'] as String?,
      directorEmail: customSettings['director_email'] as String? ?? json['director_email'] as String?,
      directorPhone: customSettings['director_phone'] as String? ?? json['director_phone'] as String?,
      tournamentFormat: customSettings['tournament_format'] as String? ?? json['tournament_format'] as String?,
      description: customSettings['description'] as String? ?? json['description'] as String?,
      facebookUrl: customSettings['facebook_url'] as String? ?? json['facebook_url'] as String?,
      twitterUrl: customSettings['twitter_url'] as String? ?? json['twitter_url'] as String?,
      instagramUrl: customSettings['instagram_url'] as String? ?? json['instagram_url'] as String?,
      websiteUrl: customSettings['website_url'] as String? ?? json['website_url'] as String?,
      earlyBirdDeadline: json['early_bird_deadline'] != null
          ? DateTime.parse(json['early_bird_deadline'] as String)
          : null,
      earlyBirdDiscount: (json['early_bird_discount'] as num?)?.toDouble(),
      lateFeeAmount: (json['late_fee_amount'] as num?)?.toDouble(),
      lateRegistrationStartDate: json['late_registration_start_date'] != null
          ? DateTime.parse(json['late_registration_start_date'] as String)
          : null,
      maxTeams: (customSettings['max_teams'] as num?)?.toInt() ?? (json['max_teams'] as num?)?.toInt(),
      // gameDurationMinutes: json['game_duration_minutes'] as int?, // If removed, this line is gone
      minRosterSize: (customSettings['min_roster_size'] as num?)?.toInt() ?? (json['min_roster_size'] as num?)?.toInt(),
      maxRosterSize: (customSettings['max_roster_size'] as num?)?.toInt() ?? (json['max_roster_size'] as num?)?.toInt(),
      awards: customSettings['awards'] as String? ?? json['awards'] as String?,
      hasConcessions: customSettings['has_concessions'] as bool? ?? json['has_concessions'] as bool?,
      hasMerchandise: customSettings['has_merchandise'] as bool? ?? json['has_merchandise'] as bool?,
      hasMedical: customSettings['has_medical'] as bool? ?? json['has_medical'] as bool?,
      admissionFee: customSettings['admission_fee'] as String? ?? json['admission_fee'] as String?,
      parkingInfo: customSettings['parking_info'] as String? ?? json['parking_info'] as String?,
      spectatorInfo: customSettings['spectator_info'] as String? ?? json['spectator_info'] as String?,
      secondaryContactName: json['secondary_contact_name'] as String?,
      secondaryContactEmail: json['secondary_contact_email'] as String?,
      secondaryContactPhone: json['secondary_contact_phone'] as String?,
      secondaryContactRole: json['secondary_contact_role'] as String?,
      // Game timing configurations
      gameTimingConfigurations: (json['game_timing_configurations'] as Map<String, dynamic>?)?.map(
        (key, value) => MapEntry(key, GameTimingConfig.fromJson(value as Map<String, dynamic>)),
      ),
      // Selected venue and field IDs for linking
      selectedVenueIds: (json['selected_venue_ids'] as List<dynamic>?)?.map((e) => e as String).toSet(),
      selectedFieldIds: (json['selected_field_ids'] as List<dynamic>?)?.map((e) => e as String).toSet(),
    );
  }

  @override
  List<Object?> get props => [
        id, name, sportType, startDate, endDate, status, city, state,
        registrationDeadline,
        listEquals(divisions, null) ? null : Object.hashAll(divisions!), // Handle null for list hashcode
        gamesPerTeam, managingClubId, rules, refundPolicy, directorName,
        directorEmail, directorPhone, tournamentFormat, description, facebookUrl,
        twitterUrl, instagramUrl, websiteUrl, earlyBirdDeadline, earlyBirdDiscount,
        lateFeeAmount, lateRegistrationStartDate, maxTeams,
        minRosterSize, maxRosterSize, awards, hasConcessions, hasMerchandise,
        hasMedical, admissionFee, parkingInfo, spectatorInfo,
        secondaryContactName, secondaryContactEmail, secondaryContactPhone,
        secondaryContactRole,
        mapEquals(gameTimingConfigurations, null) ? null : Object.hashAll(gameTimingConfigurations!.entries), // Handle null for map hashcode
        selectedVenueIds, selectedFieldIds,
      ];

  // Manual comparison for deprecated map equality in props for deep comparison
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    // Ensure comparison against same runtimeType if Equatable is not handled by base class
    // Equatable handles runtimeType internally, so usually just `other is Tournament` is fine.
    return other is Tournament &&
        id == other.id &&
        name == other.name &&
        sportType == other.sportType &&
        startDate == other.startDate &&
        endDate == other.endDate &&
        status == other.status &&
        city == other.city &&
        state == other.state &&
        registrationDeadline == other.registrationDeadline &&
        listEquals(divisions, other.divisions) && // Use listEquals for divisions
        gamesPerTeam == other.gamesPerTeam &&
        managingClubId == other.managingClubId &&
        rules == other.rules &&
        refundPolicy == other.refundPolicy &&
        directorName == other.directorName &&
        directorEmail == other.directorEmail &&
        directorPhone == other.directorPhone &&
        tournamentFormat == other.tournamentFormat &&
        description == other.description &&
        facebookUrl == other.facebookUrl &&
        twitterUrl == other.twitterUrl &&
        instagramUrl == other.instagramUrl &&
        websiteUrl == other.websiteUrl &&
        earlyBirdDeadline == other.earlyBirdDeadline &&
        earlyBirdDiscount == other.earlyBirdDiscount &&
        lateFeeAmount == other.lateFeeAmount &&
        lateRegistrationStartDate == other.lateRegistrationStartDate &&
        maxTeams == other.maxTeams &&
        minRosterSize == other.minRosterSize &&
        maxRosterSize == other.maxRosterSize &&
        awards == other.awards &&
        hasConcessions == other.hasConcessions &&
        hasMerchandise == other.hasMerchandise &&
        hasMedical == other.hasMedical &&
        admissionFee == other.admissionFee &&
        parkingInfo == other.parkingInfo &&
        spectatorInfo == other.spectatorInfo &&
        secondaryContactName == other.secondaryContactName &&
        secondaryContactEmail == other.secondaryContactEmail &&
        secondaryContactPhone == other.secondaryContactPhone &&
        secondaryContactRole == other.secondaryContactRole &&
        mapEquals(gameTimingConfigurations, other.gameTimingConfigurations) && // Use mapEquals for timing configs
        setEquals(selectedVenueIds, other.selectedVenueIds) &&
        setEquals(selectedFieldIds, other.selectedFieldIds);
  }

  @override
  int get hashCode => Object.hashAll([
        id, name, sportType, startDate, endDate, status, city, state,
        registrationDeadline,
        // Use Object.hashAll for lists and maps to get a deep hash, handle null lists/maps
        listEquals(divisions, null) ? null : Object.hashAll(divisions!),
        gamesPerTeam, managingClubId, rules, refundPolicy, directorName,
        directorEmail, directorPhone, tournamentFormat, description, facebookUrl,
        twitterUrl, instagramUrl, websiteUrl, earlyBirdDeadline, earlyBirdDiscount,
        lateFeeAmount, lateRegistrationStartDate, maxTeams, minRosterSize, maxRosterSize,
        awards, hasConcessions, hasMerchandise, hasMedical, admissionFee, parkingInfo,
        spectatorInfo, secondaryContactName, secondaryContactEmail, secondaryContactPhone,
        secondaryContactRole,
        listEquals(gameTimingConfigurations?.entries.toList(), null) ? null : Object.hashAll(gameTimingConfigurations!.entries),
        selectedVenueIds, selectedFieldIds,
      ]);
}

// These functions are imported from 'package:flutter/foundation.dart', so no need to redefine them here.
// bool mapEquals<K, V>(Map<K, V>? a, Map<K, V>? b) { ... }
// bool listEquals<T>(List<T>? a, List<T>? b) { ... }