import 'package:flutter/foundation.dart';

@immutable
class Venue {
  final String? id;
  final String name;
  final String address;
  final String? city;
  final String? state;
  final String? zipCode;
  final String? contactName;
  final String? contactEmail;
  final String? contactPhone;
  final DateTime? operationalStartTime;
  final DateTime? operationalEndTime;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const Venue({
    this.id,
    required this.name,
    required this.address,
    this.city,
    this.state,
    this.zipCode,
    this.contactName,
    this.contactEmail,
    this.contactPhone,
    this.operationalStartTime,
    this.operationalEndTime,
    this.createdAt,
    this.updatedAt,
  });

  Venue copyWith({
    String? id,
    String? name,
    String? address,
    String? city,
    String? state,
    String? zipCode,
    String? contactName,
    String? contactEmail,
    String? contactPhone,
    DateTime? operationalStartTime,
    DateTime? operationalEndTime,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Venue(
      id: id ?? this.id,
      name: name ?? this.name,
      address: address ?? this.address,
      city: city ?? this.city,
      state: state ?? this.state,
      zipCode: zipCode ?? this.zipCode,
      contactName: contactName ?? this.contactName,
      contactEmail: contactEmail ?? this.contactEmail,
      contactPhone: contactPhone ?? this.contactPhone,
      operationalStartTime: operationalStartTime ?? this.operationalStartTime,
      operationalEndTime: operationalEndTime ?? this.operationalEndTime,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'address': address,
      'city': city,
      'state': state,
      'zip_code': zipCode,
      'contact_person': contactName, // Database expects contact_person
      'contact_email': contactEmail,   // Matching RPC/DB
      'contact_phone': contactPhone,   // Matching RPC/DB
      'operational_start_time': operationalStartTime != null
          ? '${operationalStartTime!.hour.toString().padLeft(2, '0')}:${operationalStartTime!.minute.toString().padLeft(2, '0')}:00'
          : null,
      'operational_end_time': operationalEndTime != null
          ? '${operationalEndTime!.hour.toString().padLeft(2, '0')}:${operationalEndTime!.minute.toString().padLeft(2, '0')}:00'
          : null,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  static DateTime? _parseTimeString(String timeString) {
    try {
      // Handle time format like "17:15:00" by creating a DateTime with today's date
      final parts = timeString.split(':');
      if (parts.length >= 2) {
        final hour = int.parse(parts[0]);
        final minute = int.parse(parts[1]);
        final second = parts.length > 2 ? int.parse(parts[2]) : 0;

        final now = DateTime.now();
        return DateTime(now.year, now.month, now.day, hour, minute, second);
      }
      // If it's already a full datetime string, parse it normally
      return DateTime.parse(timeString);
    } catch (e) {
      print('Error parsing time string "$timeString": $e');
      return null;
    }
  }

  factory Venue.fromJson(Map<String, dynamic> json) {
    return Venue(
      id: json['id'] as String?,
      name: json['name'] as String,
      address: json['address'] as String,
      city: json['city'] as String?,
      state: json['state'] as String?,
      zipCode: json['zip_code'] as String?,
      contactName: json['contact_person'] as String? ?? json['contact_name'] as String? ?? json['contactName'] as String?, // Handle all key styles
      contactEmail: json['contact_email'] as String? ?? json['contactEmail'] as String?,
      contactPhone: json['contact_phone'] as String? ?? json['contactPhone'] as String?,
      operationalStartTime: json['operational_start_time'] != null
          ? _parseTimeString(json['operational_start_time'] as String)
          : null,
      operationalEndTime: json['operational_end_time'] != null
          ? _parseTimeString(json['operational_end_time'] as String)
          : null,
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'] as String)
          : null,
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'] as String)
          : null,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Venue &&
          runtimeType == other.runtimeType &&
          id == other.id &&
          name == other.name &&
          address == other.address &&
          city == other.city &&
          state == other.state &&
          zipCode == other.zipCode &&
          contactName == other.contactName &&
          contactEmail == other.contactEmail &&
          contactPhone == other.contactPhone &&
          operationalStartTime == other.operationalStartTime &&
          operationalEndTime == other.operationalEndTime;

  @override
  int get hashCode =>
      id.hashCode ^
      name.hashCode ^
      address.hashCode ^
      city.hashCode ^
      state.hashCode ^
      zipCode.hashCode ^
      contactName.hashCode ^
      contactEmail.hashCode ^
      contactPhone.hashCode ^
      operationalStartTime.hashCode ^
      operationalEndTime.hashCode;
}
