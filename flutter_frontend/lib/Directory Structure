Here's the breakdown within flutter_frontend/lib/:
lib/features/: This is the main directory for your application's distinct functional areas.
lib/features/<feature_name>/: Create a sub-directory for each major feature. Examples based on your previous file lists:
lib/features/auth/
lib/features/profile/
lib/features/tournament/
lib/features/admin/
lib/features/debug/
lib/features/scheduling/
lib/features/referee/
lib/features/team/
lib/features/bracket/
(Add others as needed)
lib/features/<feature_name>/screens/: Inside each feature directory, create a screens sub-directory. This is where the actual screen Dart files go.
Examples:
AuthScreen (the combined Login/Register) -> flutter_frontend/lib/features/auth/screens/auth_screen.dart
ClubRegistrationScreen -> flutter_frontend/lib/features/auth/screens/club_registration_screen.dart
CallbackScreen -> flutter_frontend/lib/features/auth/screens/callback_screen.dart
ResetPasswordScreen -> flutter_frontend/lib/features/auth/screens/reset_password_screen.dart
CheckEmailScreen -> flutter_frontend/lib/features/auth/screens/check_email_screen.dart
ConfirmationSuccessScreen -> flutter_frontend/lib/features/auth/screens/confirmation_success/clean architecture principles, your screens should go inside thefeatures` directory, organized by the specific feature they belong to.
Here's the breakdown within flutter_frontend/lib/:
features/: This is the main directory holding different modules or sections of your application.
features/<feature_name>/: Each subdirectory here represents a distinct feature, like auth, profile, tournament, admin, debug, etc.
features/<feature_name>/screens/ (or pages/ or views/): This is where the primary screen widgets go. Each file typically represents a full screen view that the user navigates to.
Examples:
Authentication Screens:
flutter_frontend/lib/features/auth/screens/auth_screen.dart (Consolidated Login/Signup)
flutter_frontend/lib/features/auth/screens/club_registration_screen.dart
flutter_frontend/lib/features/auth/screens/callback_screen.dart
flutter_frontend/lib/features/auth/screens/reset_password_screen.dart
flutter_frontend/lib/features/auth/screens/check_email_screen.dart
flutter_frontend/lib/features/auth/screens/confirmation_success_screen.dart
flutter_frontend/lib/features/auth/screens/invalid_link_screen.dart
Profile Screens:
flutter_frontend/lib/features/profile/screens/profile_screen.dart
flutter_frontend/lib/features/profile/screens/edit_profile_screen.dart
Tournament Screens:
flutter_frontend/lib/features/tournament/screens/tournament_list_screen.dart
flutter_frontend/lib/features/tournament/screens/tournament_detail_screen.dart
flutter_frontend/lib/features/tournament/screens/tournament_wizard_screen.dart
flutter_frontend/lib/features/tournament/screens/realtime_tournament_screen.dart
Scheduling Screens:
flutter_frontend/lib/features/scheduling/screens/scheduling_dashboard_screen.dart
flutter_frontend/lib/features/scheduling/screens/manual_schedule_edit_screen.dart
Admin Screens:
flutter_frontend/lib/features/admin/screens/admin_dashboard_screen.dart
flutter_frontend/lib/features/admin/screens/user_management_screen.dart
flutter_frontend/lib/features/admin/screens/club_management_screen.dart
Debug Screens:
flutter_frontend/lib/features/debug/screens/debug_menu_screen.dart
flutter_frontend/lib/features/debug/screens/auth_debug_screen.dart
Where other UI code goes:
presentation/widgets/: Contains shared, reusable widgets used across multiple features (e.g., CommonAppBar, CustomCard, LoadingIndicator, OfflineIndicator).
features/<feature_name>/widgets/: Contains widgets specific only to that particular feature (e.g., a LoginForm widget used only within AuthScreen).
