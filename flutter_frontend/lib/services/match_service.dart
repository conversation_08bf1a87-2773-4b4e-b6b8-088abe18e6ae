import 'package:supabase_flutter/supabase_flutter.dart';
// import 'dart:convert'; // For jsonEncode if needed for filters, but Supabase client handles map to JSONB.

class MatchService {
  final SupabaseClient _supabaseClient = Supabase.instance.client;

  Future<List<Map<String, dynamic>>> getMatchesFiltered(String tournamentId, {Map<String, dynamic>? filters}) async {
    try {
      final Map<String, dynamic> params = {'p_tournament_id': tournamentId};
      if (filters != null) {
        // Supabase client handles map to JSON conversion for JSONB parameters.
        params['p_filters'] = filters;
      }
      final response = await _supabaseClient.rpc('get_matches_filtered', params: params);
      
      // RPCs that return SETOF record will typically return a List<dynamic>
      return List<Map<String, dynamic>>.from(response ?? []);
    } on PostgrestException catch (e) {
      print('Supabase Error in getMatchesFiltered: ${e.message}');
      throw Exception('Failed to get matches: ${e.message}');
    } catch (e) {
      print('Unexpected error in getMatchesFiltered: $e');
      throw Exception('An unexpected error occurred while fetching matches.');
    }
  }

  Future<List<Map<String, dynamic>>> getPlayoffBracketMatches(String tournamentId, String ageGroupId) async {
    try {
      final params = {'p_tournament_id': tournamentId, 'p_age_group_id': ageGroupId};
      final response = await _supabaseClient.rpc('get_playoff_bracket_matches', params: params);
      return List<Map<String, dynamic>>.from(response ?? []);
    } on PostgrestException catch (e) {
      print('Supabase Error in getPlayoffBracketMatches: ${e.message}');
      throw Exception('Failed to get playoff matches: ${e.message}');
    } catch (e) {
      print('Unexpected error in getPlayoffBracketMatches: $e');
      throw Exception('An unexpected error occurred while fetching playoff matches.');
    }
  }

  Future<Map<String, dynamic>> submitMatchScore(
    String matchId,
    int team1Score,
    int team2Score,
    bool isFinal,
    {String? status, int? team1PenaltyScore, int? team2PenaltyScore}
  ) async {
    try {
      final params = {
        'p_match_id': matchId,
        'p_team1_score': team1Score,
        'p_team2_score': team2Score,
        'p_is_final': isFinal,
        'p_status': status ?? (isFinal ? 'completed' : 'in_progress'), 
        'p_team1_penalty_score': team1PenaltyScore,
        'p_team2_penalty_score': team2PenaltyScore,
      };
      // Remove null penalty scores so they don't overwrite existing non-null values in DB if RPC handles COALESCE
      // Or ensure RPC handles NULL inputs appropriately (e.g. COALESCE(p_team1_penalty_score, team1_penalty_score))
      // For this implementation, we pass them as is, assuming RPC handles it.
      // If not, filter them: params.removeWhere((key, value) => (key == 'p_team1_penalty_score' || key == 'p_team2_penalty_score') && value == null);


      final response = await _supabaseClient.rpc('submit_match_score', params: params);
      
      if (response is List && response.isNotEmpty) {
         return response.first as Map<String, dynamic>;
      } else if (response is Map) {
         return response as Map<String, dynamic>;
      }
      throw Exception('Unexpected data format or empty response from submit_match_score RPC.');
    } on PostgrestException catch (e) {
      print('Supabase Error in submitMatchScore: ${e.message}');
      throw Exception('Failed to submit match score: ${e.message}');
    } catch (e) {
      print('Unexpected error in submitMatchScore: $e');
      throw Exception('An unexpected error occurred while submitting the match score.');
    }
  }

  Stream<List<Map<String, dynamic>>> streamMatches(String tournamentId, {Map<String, dynamic>? filters}) {
    // TODO: Implement actual Supabase Realtime subscription.
    // This often involves listening to table changes directly or using Supabase's broadcast feature
    // if updates are triggered by RPCs that don't directly modify a table in a way Realtime can pick up.
    // For now, returning a placeholder stream.
    
    print('Placeholder: Supabase Realtime stream for matches in tournament $tournamentId with filters $filters is not yet fully implemented with live data.');
    
    // Example of listening to a table (if 'matches' table is set up for RLS and Realtime)
    // String filterString = 'tournament_id=eq.$tournamentId';
    // if (filters != null) {
    //   filters.forEach((key, value) {
    //     // This is a simplistic filter builder; a real one would be more robust.
    //     filterString += ',$key=eq.$value';
    //   });
    // }
    // try {
    //   return _supabaseClient
    //     .from('matches') // Ensure RLS is enabled for 'matches' and select is allowed
    //     .stream(primaryKey: ['id']) // Assuming 'id' is the primary key
    //     // .eq('tournament_id', tournamentId) // Basic filter example
    //     // Add more filters based on the 'filters' map if direct table streaming supports it well.
    //     // Or, listen to a broader stream and filter client-side, or use a Realtime channel with broadcasts.
    //     .map((listOfMaps) => listOfMaps.map((map) => Map<String,dynamic>.from(map)).toList())
    //     .handleError((error) {
    //       print('Supabase Realtime Error in streamMatches: $error');
    //       // Optionally, rethrow or transform into a stream error event for UI.
    //     });
    // } catch (e) {
    //    print('Error setting up streamMatches: $e');
    //    return Stream.error(Exception('Failed to setup match stream: $e'));
    // }

    // Placeholder stream:
    return Stream.periodic(const Duration(seconds: 10), (count) {
      print('Mock Realtime (MatchService): Emitting mock match update $count for tournament $tournamentId');
      // Simulate fetching data or a mock update
      return [
        {'id': 'match_stream_update_$count', 'team1_id': 'teamStreamA', 'team2_id': 'teamStreamB', 'status': 'in_progress_stream', 'team1_score': count % 4, 'team2_score': count % 3}
      ];
    });
  }
}
