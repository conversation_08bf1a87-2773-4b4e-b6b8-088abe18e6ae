import 'package:supabase_flutter/supabase_flutter.dart';

class TournamentSettingsService {
  final SupabaseClient _supabaseClient = Supabase.instance.client;

  Future<Map<String, dynamic>> getTournamentSettings(String tournamentId) async {
    try {
      final response = await _supabaseClient.rpc(
        'get_tournament_settings',
        params: {'p_tournament_id': tournamentId},
      );
      // Assuming the RPC returns a list with a single object, or just the object itself.
      // The SQL migration uses SETOF, so a List is expected.
      if (response is List && response.isNotEmpty) {
         return response.first as Map<String, dynamic>;
      } else if (response is Map) { // Should not happen with SETOF but good to check
         return response as Map<String, dynamic>;
      }
      // If the tournament is not found, the list might be empty.
      // Depending on desired behavior, either throw an error or return a default/empty map.
      // For now, throwing an error if not found or format is wrong.
      throw Exception('Tournament settings not found or unexpected data format from get_tournament_settings RPC.');
    } on PostgrestException catch (e) {
      print('Supabase Error in getTournamentSettings: ${e.message}');
      throw Exception('Failed to get tournament settings: ${e.message}');
    } catch (e) {
      print('Unexpected error in getTournamentSettings: $e');
      throw Exception('An unexpected error occurred while fetching tournament settings.');
    }
  }

  Future<Map<String, dynamic>> updateTournamentSettings(String tournamentId, Map<String, dynamic> settingsData) async {
    try {
      final response = await _supabaseClient.rpc(
        'update_tournament_settings',
        params: {'p_tournament_id': tournamentId, 'p_settings': settingsData},
      );
      // Assuming the RPC returns the updated row/object, potentially in a list
      if (response is List && response.isNotEmpty) {
         return response.first as Map<String, dynamic>;
      } else if (response is Map) {
         return response as Map<String, dynamic>;
      }
      throw Exception('Unexpected data format or empty response from update_tournament_settings RPC.');
    } on PostgrestException catch (e) {
      print('Supabase Error in updateTournamentSettings: ${e.message}');
      throw Exception('Failed to update tournament settings: ${e.message}');
    } catch (e) {
      print('Unexpected error in updateTournamentSettings: $e');
      throw Exception('An unexpected error occurred while updating tournament settings.');
    }
  }
}
