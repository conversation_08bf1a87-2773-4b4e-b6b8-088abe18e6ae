import 'package:supabase_flutter/supabase_flutter.dart';

// Define a custom exception for Supabase RPC errors if not already globally defined.
// For now, we'll throw generic Exceptions or rethrow Supabase's PostgrestException.
// class SupabaseRpcException implements Exception {
//   final String message;
//   SupabaseRpcException(this.message);
//   @override
//   String toString() => message;
// }

class VenueService {
  final SupabaseClient _supabaseClient = Supabase.instance.client;

  Future<Map<String, dynamic>> createVenue(Map<String, dynamic> venueData) async {
    try {
      final response = await _supabaseClient.rpc(
        'create_venue',
        params: {'p_venue_data': venueData},
      );

      // Assuming RPC returns the new venue object, potentially in a list
      // Supabase RPCs that return SETOF record will return a List<dynamic>
      if (response is List && response.isNotEmpty) {
         return response.first as Map<String, dynamic>;
      } else if (response is Map) {
         // This case might occur if the RPC is defined to return a single record directly (not SETOF)
         // However, the SQL migrations use SETOF, so List is expected.
         return response as Map<String, dynamic>;
      }
      // If response is null or not a list/map, or an empty list, it's an issue.
      // The .rpc method itself throws PostgrestException on network/db errors,
      // so we might not reach here if response.error was set.
      // If response is simply empty without an error, it means the RPC returned nothing.
      throw Exception('Unexpected data format or empty response from create_venue RPC.');
    } on PostgrestException catch (e) {
      print('Supabase Error in createVenue: ${e.message}');
      throw Exception('Failed to create venue: ${e.message}');
    } catch (e) {
      print('Unexpected error in createVenue: $e');
      throw Exception('An unexpected error occurred while creating the venue.');
    }
  }

  Future<List<Map<String, dynamic>>> getVenues() async {
    try {
      final response = await _supabaseClient.rpc('get_venues');
      // SETOF functions return List<dynamic> which needs casting.
      // If response is null (e.g. RPC returned no rows), default to empty list.
      return List<Map<String, dynamic>>.from(response ?? []);
    } on PostgrestException catch (e) {
      print('Supabase Error in getVenues: ${e.message}');
      throw Exception('Failed to get venues: ${e.message}');
    } catch (e) {
      print('Unexpected error in getVenues: $e');
      throw Exception('An unexpected error occurred while fetching venues.');
    }
  }

  Future<Map<String, dynamic>> updateVenue(String venueId, Map<String, dynamic> venueData) async {
    try {
      final response = await _supabaseClient.rpc(
        'update_venue',
        params: {'p_venue_id': venueId, 'p_venue_data': venueData},
      );

      if (response is List && response.isNotEmpty) {
         return response.first as Map<String, dynamic>;
      } else if (response is Map) {
         return response as Map<String, dynamic>;
      }
      throw Exception('Unexpected data format or empty response from update_venue RPC.');
    } on PostgrestException catch (e) {
      print('Supabase Error in updateVenue: ${e.message}');
      throw Exception('Failed to update venue: ${e.message}');
    } catch (e) {
      print('Unexpected error in updateVenue: $e');
      throw Exception('An unexpected error occurred while updating the venue.');
    }
  }

  Future<Map<String, dynamic>> createField(Map<String, dynamic> fieldData) async {
    try {
      final response = await _supabaseClient.rpc(
        'create_field',
        params: {'p_field_data': fieldData},
      );
      if (response is List && response.isNotEmpty) {
         return response.first as Map<String, dynamic>;
      } else if (response is Map) {
         return response as Map<String, dynamic>;
      }
      throw Exception('Unexpected data format or empty response from create_field RPC.');
    } on PostgrestException catch (e) {
      print('Supabase Error in createField: ${e.message}');
      throw Exception('Failed to create field: ${e.message}');
    } catch (e) {
      print('Unexpected error in createField: $e');
      throw Exception('An unexpected error occurred while creating the field.');
    }
  }

  Future<List<Map<String, dynamic>>> getFieldsForVenue(String venueId) async {
    try {
      final response = await _supabaseClient.rpc(
        'get_fields_for_venue',
        params: {'p_venue_id': venueId},
      );
      return List<Map<String, dynamic>>.from(response ?? []);
    } on PostgrestException catch (e) {
      print('Supabase Error in getFieldsForVenue: ${e.message}');
      throw Exception('Failed to get fields for venue: ${e.message}');
    } catch (e) {
      print('Unexpected error in getFieldsForVenue: $e');
      throw Exception('An unexpected error occurred while fetching fields for the venue.');
    }
  }

  Future<Map<String, dynamic>> updateField(String fieldId, Map<String, dynamic> fieldData) async {
    try {
      print('DEBUG VenueService: Calling update_field RPC with fieldId: $fieldId');
      print('DEBUG VenueService: Field data: $fieldData');

      final response = await _supabaseClient.rpc(
        'update_field',
        params: {'p_field_id': fieldId, 'p_field_data': fieldData},
      );

      print('DEBUG VenueService: Raw RPC response: $response');
      print('DEBUG VenueService: Response type: ${response.runtimeType}');

      if (response is List && response.isNotEmpty) {
         print('DEBUG VenueService: Returning first item from list response');
         return response.first as Map<String, dynamic>;
      } else if (response is Map) {
         print('DEBUG VenueService: Returning map response');
         return response as Map<String, dynamic>;
      }

      print('ERROR VenueService: Unexpected response format - response: $response');
      throw Exception('Unexpected data format or empty response from update_field RPC.');
    } on PostgrestException catch (e) {
      print('ERROR VenueService: Supabase Error in updateField: ${e.message}');
      print('ERROR VenueService: Supabase Error details: ${e.details}');
      print('ERROR VenueService: Supabase Error hint: ${e.hint}');
      throw Exception('Failed to update field: ${e.message}');
    } catch (e) {
      print('ERROR VenueService: Unexpected error in updateField: $e');
      throw Exception('An unexpected error occurred while updating the field.');
    }
  }

  Future<Map<String, dynamic>> addFieldUnavailability(Map<String, dynamic> unavailabilityData) async {
    try {
      final response = await _supabaseClient.rpc(
        'add_field_unavailability',
        params: {'p_unavailability_data': unavailabilityData},
      );
      if (response is List && response.isNotEmpty) {
         return response.first as Map<String, dynamic>;
      } else if (response is Map) {
         return response as Map<String, dynamic>;
      }
      throw Exception('Unexpected data format or empty response from add_field_unavailability RPC.');
    } on PostgrestException catch (e) {
      print('Supabase Error in addFieldUnavailability: ${e.message}');
      throw Exception('Failed to add field unavailability: ${e.message}');
    } catch (e) {
      print('Unexpected error in addFieldUnavailability: $e');
      throw Exception('An unexpected error occurred while adding field unavailability.');
    }
  }

  Future<List<Map<String, dynamic>>> getFieldUnavailabilities(String fieldId, {String? tournamentId}) async {
    try {
      final response = await _supabaseClient.rpc(
        'get_field_unavailabilities',
        params: {'p_field_id_param': fieldId, 'p_tournament_id_filter': tournamentId},
      );
      return List<Map<String, dynamic>>.from(response ?? []);
    } on PostgrestException catch (e) {
      print('Supabase Error in getFieldUnavailabilities: ${e.message}');
      throw Exception('Failed to get field unavailabilities: ${e.message}');
    } catch (e) {
      print('Unexpected error in getFieldUnavailabilities: $e');
      throw Exception('An unexpected error occurred while fetching field unavailabilities.');
    }
  }

  Future<void> deleteFieldUnavailability(String unavailabilityId) async {
    try {
      await _supabaseClient.rpc(
        'delete_field_unavailability',
        params: {'p_unavailability_id': unavailabilityId},
      );
      // No return data expected for void RPCs typically.
      // If the RPC call itself doesn't throw, it's considered successful.
    } on PostgrestException catch (e) {
      print('Supabase Error in deleteFieldUnavailability: ${e.message}');
      throw Exception('Failed to delete field unavailability: ${e.message}');
    } catch (e) {
      print('Unexpected error in deleteFieldUnavailability: $e');
      throw Exception('An unexpected error occurred while deleting field unavailability.');
    }
  }
}
