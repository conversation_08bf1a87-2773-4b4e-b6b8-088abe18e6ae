import 'package:supabase_flutter/supabase_flutter.dart';

class AgeGroupService {
  final SupabaseClient _supabaseClient = Supabase.instance.client;

  Future<Map<String, dynamic>> createTournamentAgeGroup(Map<String, dynamic> ageGroupData) async {
    try {
      final response = await _supabaseClient.rpc(
        'create_tournament_age_group',
        params: {'p_age_group_data': ageGroupData},
      );
      if (response is List && response.isNotEmpty) {
         return response.first as Map<String, dynamic>;
      } else if (response is Map) {
         return response as Map<String, dynamic>;
      }
      throw Exception('Unexpected data format or empty response from create_tournament_age_group RPC.');
    } on PostgrestException catch (e) {
      print('Supabase Error in createTournamentAgeGroup: ${e.message}');
      throw Exception('Failed to create age group: ${e.message}');
    } catch (e) {
      print('Unexpected error in createTournamentAgeGroup: $e');
      throw Exception('An unexpected error occurred while creating the age group.');
    }
  }

  Future<List<Map<String, dynamic>>> getTournamentAgeGroups(String tournamentId) async {
    try {
      final response = await _supabaseClient.rpc(
        'get_tournament_age_groups',
        params: {'p_tournament_id_param': tournamentId},
      );
      return List<Map<String, dynamic>>.from(response ?? []);
    } on PostgrestException catch (e) {
      print('Supabase Error in getTournamentAgeGroups: ${e.message}');
      throw Exception('Failed to get age groups: ${e.message}');
    } catch (e) {
      print('Unexpected error in getTournamentAgeGroups: $e');
      throw Exception('An unexpected error occurred while fetching age groups.');
    }
  }

  Future<Map<String, dynamic>> updateTournamentAgeGroup(String ageGroupId, Map<String, dynamic> ageGroupData) async {
    try {
      final response = await _supabaseClient.rpc(
        'update_tournament_age_group',
        params: {'p_age_group_id': ageGroupId, 'p_age_group_data': ageGroupData},
      );
      if (response is List && response.isNotEmpty) {
         return response.first as Map<String, dynamic>;
      } else if (response is Map) {
         return response as Map<String, dynamic>;
      }
      throw Exception('Unexpected data format or empty response from update_tournament_age_group RPC.');
    } on PostgrestException catch (e) {
      print('Supabase Error in updateTournamentAgeGroup: ${e.message}');
      throw Exception('Failed to update age group: ${e.message}');
    } catch (e) {
      print('Unexpected error in updateTournamentAgeGroup: $e');
      throw Exception('An unexpected error occurred while updating the age group.');
    }
  }

  Future<void> deleteTournamentAgeGroup(String ageGroupId) async {
    try {
      await _supabaseClient.rpc(
        'delete_tournament_age_group',
        params: {'p_age_group_id': ageGroupId},
      );
    } on PostgrestException catch (e) {
      print('Supabase Error in deleteTournamentAgeGroup: ${e.message}');
      throw Exception('Failed to delete age group: ${e.message}');
    } catch (e) {
      print('Unexpected error in deleteTournamentAgeGroup: $e');
      throw Exception('An unexpected error occurred while deleting the age group.');
    }
  }
}
