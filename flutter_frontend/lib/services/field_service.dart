import 'package:soccer_frontend/data/models/field.dart';
import 'package:soccer_frontend/services/venue_service.dart';

class FieldService {
  final VenueService _venueService = VenueService();

  /// Create a new field
  Future<Field> createField(Field field) async {
    try {
      final fieldData = field.toJson();
      fieldData.remove('id'); // Remove ID for creation
      fieldData.remove('created_at'); // Remove timestamps
      fieldData.remove('updated_at');

      final response = await _venueService.createField(fieldData);
      return Field.fromJson(response);
    } catch (e) {
      print('Error creating field: $e');
      throw Exception('Failed to create field: $e');
    }
  }

  /// Update an existing field
  Future<Field> updateField(Field field) async {
    try {
      if (field.id == null) {
        throw Exception('Field ID is required for update');
      }

      print('DEBUG FieldService: Starting field update for field ID: ${field.id}');

      final fieldData = field.toJson();
      fieldData.remove('id'); // Remove ID from data
      fieldData.remove('created_at'); // Remove timestamps
      fieldData.remove('updated_at');

      // Map field_type_name to surface_type for database compatibility
      if (fieldData.containsKey('field_type_name')) {
        fieldData['surface_type'] = fieldData['field_type_name'];
        fieldData.remove('field_type_name');
      }

      // Map field start/end times to operational times for database compatibility
      if (fieldData.containsKey('field_start_time')) {
        fieldData['operational_start_time'] = fieldData['field_start_time'];
        fieldData.remove('field_start_time');
      }
      if (fieldData.containsKey('field_end_time')) {
        fieldData['operational_end_time'] = fieldData['field_end_time'];
        fieldData.remove('field_end_time');
      }

      // Map size to field_type_id for database compatibility
      if (fieldData.containsKey('size')) {
        final size = fieldData['size'] as String?;
        if (size != null) {
          // Map size values to field_type_id
          String? fieldTypeId;
          switch (size) {
            case '4v4':
              fieldTypeId = '0e8482a0-a237-4929-a240-8ac02a7c3fce'; // 4v4 Mini
              break;
            case '7v7':
              fieldTypeId = '82708882-553b-4e5b-b1e3-259430c16cf3'; // 7v7
              break;
            case '9v9':
              fieldTypeId = '9a1b7345-a781-4207-929f-e146e2677ed9'; // 9v9
              break;
            case '11v11':
              fieldTypeId = '0d5fe0de-a32d-4bfd-b9bd-27880561c1ae'; // 11v11 Full-Size
              break;
          }
          if (fieldTypeId != null) {
            fieldData['field_type_id'] = fieldTypeId;
          }
        }
        fieldData.remove('size'); // Remove size as it's not a database column
      }

      print('DEBUG FieldService: Field data to update (after mapping): $fieldData');

      final response = await _venueService.updateField(field.id!, fieldData);
      print('DEBUG FieldService: Update response: $response');

      final updatedField = Field.fromJson(response);
      print('DEBUG FieldService: Field updated successfully: ${updatedField.nameOrNumber}');

      return updatedField;
    } catch (e) {
      print('ERROR FieldService: Error updating field: $e');
      throw Exception('Failed to update field: $e');
    }
  }

  /// Get all fields for a venue
  Future<List<Field>> getFieldsForVenue(String venueId) async {
    try {
      final fieldsData = await _venueService.getFieldsForVenue(venueId);
      return fieldsData.map((data) => Field.fromJson(data)).toList();
    } catch (e) {
      print('Error fetching fields for venue $venueId: $e');
      throw Exception('Failed to fetch fields: $e');
    }
  }
}
