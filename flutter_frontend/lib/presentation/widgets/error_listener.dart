// lib/src/widgets/error_listener.dart
import 'package:flutter/material.dart';
// Uncomment when adding BlocListeners
// import 'package:logging/logging.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import '../utils/error_handler.dart';

/// A widget that listens for errors in BLoCs and shows appropriate UI feedback
class ErrorListener extends StatefulWidget {
  final Widget child;

  const ErrorListener({
    super.key,
    required this.child,
  });

  @override
  State<ErrorListener> createState() => _ErrorListenerState();
}

class _ErrorListenerState extends State<ErrorListener> {
  // Uncomment when adding BlocListeners
  // final Logger _log = Logger('ErrorListener');

  @override
  Widget build(BuildContext context) {
    // Since we don't have any active listeners yet, just return the child directly
    // to avoid the empty listeners list error
    return widget.child;

    // When you're ready to add listeners, uncomment this:
    /*
    return MultiBlocListener(
      listeners: [
        // Example: Listen to AuthBloc for auth-related errors
        BlocListener<AuthBloc, AuthState>(
          listenWhen: (previous, current) => current is AuthFailure,
          listener: (context, state) {
            if (state is AuthFailure) {
              _handleError(context, state.message);
            }
          },
        ),
        // Add more BlocListeners for other BLoCs as needed
      ],
      child: widget.child,
    );
    */
  }

  // Uncomment when adding BlocListeners
  /*
  void _handleError(BuildContext context, String message) {
    _log.warning('Handling error in UI: $message');

    // Show a snackbar for most errors
    ErrorHandler.showErrorSnackBar(context, message);

    // For critical errors, you might want to show a dialog instead
    // ErrorHandler.showErrorDialog(context, message);
  }
  */
}
