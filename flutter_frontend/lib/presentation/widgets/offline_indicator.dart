// lib/src/widgets/offline_indicator.dart
import 'package:flutter/material.dart';
import 'package:logging/logging.dart';

// Assuming you have a ConnectivityBloc that monitors network status
// import '../blocs/connectivity/connectivity_bloc.dart';

/// A widget that shows an indicator when the device is offline
class OfflineIndicator extends StatefulWidget {
  final Widget child;

  const OfflineIndicator({
    super.key,
    required this.child,
  });

  @override
  State<OfflineIndicator> createState() => _OfflineIndicatorState();
}

class _OfflineIndicatorState extends State<OfflineIndicator> {
  final Logger _log = Logger('OfflineIndicator');
  bool _isOffline = false;

  @override
  void initState() {
    super.initState();
    _log.fine('OfflineIndicator initialized');
    // You would typically check initial connectivity status here
  }

  @override
  Widget build(BuildContext context) {
    // This is a placeholder implementation
    // In a real app, you would use a ConnectivityBloc to monitor network status
    return Stack(
      children: [
        widget.child,
        // Show offline banner when offline
        if (_isOffline)
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: Material(
              color: Colors.transparent,
              child: Container(
                color: Colors.red,
                padding: const EdgeInsets.symmetric(vertical: 4),
                child: const Text(
                  'You are offline',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ),
      ],
    );

    // Example of how this would be implemented with a ConnectivityBloc:
    /*
    return BlocBuilder<ConnectivityBloc, ConnectivityState>(
      builder: (context, state) {
        final bool isOffline = state is ConnectivityOffline;
        
        return Stack(
          children: [
            widget.child,
            if (isOffline)
              Positioned(
                top: 0,
                left: 0,
                right: 0,
                child: Material(
                  color: Colors.transparent,
                  child: Container(
                    color: Colors.red,
                    padding: const EdgeInsets.symmetric(vertical: 4),
                    child: const Text(
                      'You are offline',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ),
          ],
        );
      },
    );
    */
  }
}
