// lib/features/referee/blocs/referee_bloc.dart
import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:logging/logging.dart';

import '../../../core/services/supabase_service.dart';

part 'referee_event.dart';
part 'referee_state.dart';

class RefereeBloc extends Bloc<RefereeEvent, RefereeState> {
  final SupabaseService _supabaseService;
  final Logger _log = Logger('RefereeBloc');

  RefereeBloc({required SupabaseService supabaseService})
      : _supabaseService = supabaseService,
        super(RefereeInitial()) {
    on<RefereeLoadByTournamentRequested>(_onRefereeLoadByTournamentRequested);
    // Add other event handlers as needed
  }

  Future<void> _onRefereeLoadByTournamentRequested(
    RefereeLoadByTournamentRequested event,
    Emitter<RefereeState> emit,
  ) async {
    _log.info('Loading referees for tournament ID: ${event.tournamentId}');
    emit(RefereeLoading());

    try {
      final referees = await _supabaseService.getRefereesByTournamentId(event.tournamentId);
      _log.info('Loaded ${referees.length} referees');
      emit(RefereeLoaded(referees: referees));
    } catch (e, s) {
      _log.severe('Error loading referees: $e', e, s);
      emit(RefereeError(message: 'Failed to load referees: $e'));
    }
  }
}
