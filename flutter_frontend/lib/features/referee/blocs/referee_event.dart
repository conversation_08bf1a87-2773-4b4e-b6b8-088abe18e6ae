// lib/src/blocs/referee/referee_event.dart
part of 'referee_bloc.dart';

abstract class RefereeEvent extends Equatable {
  const RefereeEvent();

  @override
  List<Object?> get props => [];
}

class RefereeLoadByTournamentRequested extends RefereeEvent {
  final String tournamentId;

  const RefereeLoadByTournamentRequested({required this.tournamentId});

  @override
  List<Object> get props => [tournamentId];
}
