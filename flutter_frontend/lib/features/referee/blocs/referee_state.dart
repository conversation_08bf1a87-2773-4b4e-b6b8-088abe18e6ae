// lib/src/blocs/referee/referee_state.dart
part of 'referee_bloc.dart';

abstract class RefereeState extends Equatable {
  const RefereeState();
  
  @override
  List<Object?> get props => [];
}

class RefereeInitial extends RefereeState {}

class RefereeLoading extends RefereeState {}

class RefereeLoaded extends RefereeState {
  final List<Map<String, dynamic>> referees;

  const RefereeLoaded({required this.referees});

  @override
  List<Object> get props => [referees];
}

class RefereeE<PERSON>r extends RefereeState {
  final String message;

  const RefereeError({required this.message});

  @override
  List<Object> get props => [message];
}
