import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:soccer_frontend/data/models/match.dart';
import 'package:soccer_frontend/data/models/team.dart';
import 'package:soccer_frontend/features/scheduling/services/scheduling_api_service.dart';
import 'field_schedule_event.dart';
import 'field_schedule_state.dart';

// This abstract class was part of the original diff for FieldScheduleBloc.
// It's unusual to define it here if a concrete service exists in `services` folder.
// However, to match the diff, I'm including it.
// If `SchedulingApiService` from `../../services/scheduling_api_service.dart` is the intended one,
// this abstract class might be redundant or should be in its own file.
// For now, the BLoC will depend on this abstract definition.
abstract class SchedulingApiServiceAbstract { // Renamed to avoid conflict if a concrete one is also named SchedulingApiService
  Future<List<Match>> loadMatchesForTournament(String tournamentId);
  Future<List<Team>> loadTeamsForTournament(String tournamentId);
  Future<void> saveMatchAssignment(Match match);
  Future<void> clearMatchAssignment(Match match);
}


class FieldScheduleBloc extends Bloc<FieldScheduleEvent, FieldScheduleState> {
  // Using the concrete service from the services folder, as per typical DI.
  // The abstract class above might be for testing/mocking or an older pattern.
  final SchedulingApiService _schedulingApiService; 
  static const int defaultInterval = 30; 

  FieldScheduleBloc({required SchedulingApiService schedulingApiService})
      : _schedulingApiService = schedulingApiService,
        super(ScheduleInitial()) {
    on<LoadSchedule>(_onLoadSchedule);
    on<ViewingIntervalChanged>(_onViewingIntervalChanged);
    on<AssignMatchToSlotRequested>(_onAssignMatchToSlotRequested);
    on<ClearMatchFromSlotRequested>(_onClearMatchFromSlotRequested);
    on<MatchAddedInternally>(_onMatchAddedInternally);
    on<MatchUpdatedInternally>(_onMatchUpdatedInternally);
    on<MatchRemovedInternally>(_onMatchRemovedInternally);
  }

  Future<void> _onLoadSchedule(
      LoadSchedule event, Emitter<FieldScheduleState> emit) async {
    emit(ScheduleLoading());
    try {
      final allMatches = await _schedulingApiService.loadMatchesForTournament(event.tournamentId);
      final teams = await _schedulingApiService.loadTeamsForTournament(event.tournamentId);

      final scheduledMatches = allMatches
          .where((m) => m.scheduledFieldId == event.fieldId && m.startTime != null)
          .toList();
      final unscheduledMatches = allMatches
          .where((m) => m.scheduledFieldId == null || m.startTime == null)
          .toList();
          
      emit(ScheduleLoaded(
        scheduledMatches: scheduledMatches,
        unscheduledMatches: unscheduledMatches,
        availableTeams: teams,
        viewingIntervalInMinutes: defaultInterval,
        currentTournamentId: event.tournamentId,
        currentFieldId: event.fieldId,
      ));
    } catch (e) {
      emit(ScheduleError(e.toString()));
    }
  }

  void _onViewingIntervalChanged(
      ViewingIntervalChanged event, Emitter<FieldScheduleState> emit) {
    if (state is ScheduleLoaded) {
      final currentState = state as ScheduleLoaded;
      emit(currentState.copyWith(viewingIntervalInMinutes: event.intervalInMinutes));
    }
  }

  Future<void> _onAssignMatchToSlotRequested(
      AssignMatchToSlotRequested event, Emitter<FieldScheduleState> emit) async {
    if (state is ScheduleLoaded) {
      final currentState = state as ScheduleLoaded;
      try {
        Match? matchToUpdate = currentState.unscheduledMatches.firstWhere((m) => m.id == event.matchId, orElse: () => 
          currentState.scheduledMatches.firstWhere((m) => m.id == event.matchId, orElse: () => throw Exception("Match not found"))
        );

        final updatedMatch = matchToUpdate.copyWith(
          scheduledFieldId: event.fieldId,
          startTime: event.startTime,
          durationInMinutes: event.durationInMinutes, // Ensure this is correct, might come from match itself
          status: 'Scheduled',
        );

        await _schedulingApiService.saveMatchAssignment(updatedMatch);

        final newScheduledMatches = List<Match>.from(currentState.scheduledMatches)
          ..removeWhere((m) => m.id == updatedMatch.id) 
          ..add(updatedMatch);

        final newUnscheduledMatches = List<Match>.from(currentState.unscheduledMatches)
          ..removeWhere((m) => m.id == updatedMatch.id);

        emit(currentState.copyWith(
          scheduledMatches: newScheduledMatches,
          unscheduledMatches: newUnscheduledMatches,
          assignmentSuccess: true,
          clearAssignmentError: true, 
        ));
      } catch (e) {
        emit(currentState.copyWith(assignmentError: e.toString(), assignmentSuccess: false));
      }
    }
  }

  Future<void> _onClearMatchFromSlotRequested(
      ClearMatchFromSlotRequested event, Emitter<FieldScheduleState> emit) async {
    if (state is ScheduleLoaded) {
      final currentState = state as ScheduleLoaded;
      try {
        final matchToClear = currentState.scheduledMatches.firstWhere((m) => m.id == event.matchId);
        final updatedMatch = matchToClear.copyWith(
          scheduledFieldId: null, 
          startTime: null,        
          status: 'Unscheduled',
        );

        await _schedulingApiService.clearMatchAssignment(updatedMatch); 

        final newScheduledMatches = List<Match>.from(currentState.scheduledMatches)
          ..removeWhere((m) => m.id == updatedMatch.id);
        final newUnscheduledMatches = List<Match>.from(currentState.unscheduledMatches)
          ..removeWhere((m) => m.id == updatedMatch.id) 
          ..add(updatedMatch);

        emit(currentState.copyWith(
          scheduledMatches: newScheduledMatches,
          unscheduledMatches: newUnscheduledMatches,
          assignmentSuccess: true, 
          clearAssignmentError: true,
        ));
      } catch (e) {
        emit(currentState.copyWith(assignmentError: e.toString(), assignmentSuccess: false));
      }
    }
  }

  void _onMatchAddedInternally(
      MatchAddedInternally event, Emitter<FieldScheduleState> emit) {
    if (state is ScheduleLoaded) {
      final currentState = state as ScheduleLoaded;
      List<Match> newScheduledMatches = List.from(currentState.scheduledMatches);
      List<Match> newUnscheduledMatches = List.from(currentState.unscheduledMatches);

      if (event.match.scheduledFieldId == currentState.currentFieldId && event.match.startTime != null) {
        newScheduledMatches.add(event.match);
      } else {
        newUnscheduledMatches.add(event.match);
      }
      emit(currentState.copyWith(
          scheduledMatches: newScheduledMatches,
          unscheduledMatches: newUnscheduledMatches));
    }
  }

  void _onMatchUpdatedInternally(
      MatchUpdatedInternally event, Emitter<FieldScheduleState> emit) {
    if (state is ScheduleLoaded) {
      final currentState = state as ScheduleLoaded;
      final updatedMatch = event.match;

      List<Match> newScheduledMatches = List.from(currentState.scheduledMatches);
      List<Match> newUnscheduledMatches = List.from(currentState.unscheduledMatches);

      newScheduledMatches.removeWhere((m) => m.id == updatedMatch.id);
      newUnscheduledMatches.removeWhere((m) => m.id == updatedMatch.id);

      if (updatedMatch.scheduledFieldId == currentState.currentFieldId && updatedMatch.startTime != null) {
        newScheduledMatches.add(updatedMatch);
      } else {
        newUnscheduledMatches.add(updatedMatch);
      }

      emit(currentState.copyWith(
          scheduledMatches: newScheduledMatches,
          unscheduledMatches: newUnscheduledMatches));
    }
  }

  void _onMatchRemovedInternally(
      MatchRemovedInternally event, Emitter<FieldScheduleState> emit) {
    if (state is ScheduleLoaded) {
      final currentState = state as ScheduleLoaded;
      emit(currentState.copyWith(
        scheduledMatches: currentState.scheduledMatches.where((m) => m.id != event.matchId).toList(),
        unscheduledMatches: currentState.unscheduledMatches.where((m) => m.id != event.matchId).toList(),
      ));
    }
  }
}
