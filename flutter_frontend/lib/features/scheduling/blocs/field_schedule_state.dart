import 'package:equatable/equatable.dart';
import 'package:soccer_frontend/data/models/match.dart';
import 'package:soccer_frontend/data/models/team.dart';

abstract class FieldScheduleState extends Equatable {
  const FieldScheduleState();

  @override
  List<Object?> get props => [];
}

class ScheduleInitial extends FieldScheduleState {}

class ScheduleLoading extends FieldScheduleState {}

class ScheduleLoaded extends FieldScheduleState {
  final List<Match> scheduledMatches;
  final List<Match> unscheduledMatches;
  final List<Team> availableTeams; // All teams in the tournament, for dropdowns etc.
  final int viewingIntervalInMinutes; // e.g., 30 min, 60 min slots
  final String currentTournamentId;
  final String currentFieldId;
  final String? assignmentError; // To show specific errors for assignment/clear operations
  final bool assignmentSuccess; // Flag for UI feedback on successful assignment/clear

  const ScheduleLoaded({
    required this.scheduledMatches,
    required this.unscheduledMatches,
    required this.availableTeams,
    required this.viewingIntervalInMinutes,
    required this.currentTournamentId,
    required this.currentFieldId,
    this.assignmentError,
    this.assignmentSuccess = false,
  });

  ScheduleLoaded copyWith({
    List<Match>? scheduledMatches,
    List<Match>? unscheduledMatches,
    List<Team>? availableTeams,
    int? viewingIntervalInMinutes,
    String? currentTournamentId,
    String? currentFieldId,
    String? assignmentError,
    bool? assignmentSuccess,
    bool clearAssignmentError = false, // Special flag to nullify error
  }) {
    return ScheduleLoaded(
      scheduledMatches: scheduledMatches ?? this.scheduledMatches,
      unscheduledMatches: unscheduledMatches ?? this.unscheduledMatches,
      availableTeams: availableTeams ?? this.availableTeams,
      viewingIntervalInMinutes: viewingIntervalInMinutes ?? this.viewingIntervalInMinutes,
      currentTournamentId: currentTournamentId ?? this.currentTournamentId,
      currentFieldId: currentFieldId ?? this.currentFieldId,
      assignmentError: clearAssignmentError ? null : assignmentError ?? this.assignmentError,
      assignmentSuccess: assignmentSuccess ?? this.assignmentSuccess,
    );
  }

  @override
  List<Object?> get props => [
        scheduledMatches,
        unscheduledMatches,
        availableTeams,
        viewingIntervalInMinutes,
        currentTournamentId,
        currentFieldId,
        assignmentError,
        assignmentSuccess,
      ];
}

class ScheduleError extends FieldScheduleState {
  final String error;

  const ScheduleError(this.error);

  @override
  List<Object> get props => [error];
}
