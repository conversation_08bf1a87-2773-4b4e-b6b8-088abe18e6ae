// lib/src/blocs/scheduling/scheduling_event.dart
part of 'scheduling_bloc.dart';

abstract class SchedulingEvent extends Equatable {
  const SchedulingEvent();

  @override
  List<Object?> get props => [];
}

class ScheduleTournamentRequested extends SchedulingEvent {
  final String tournamentId;

  const ScheduleTournamentRequested({required this.tournamentId});

  @override
  List<Object> get props => [tournamentId];
}
