// lib/features/scheduling/blocs/scheduling_bloc.dart
import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:logging/logging.dart';

import '../../../core/services/supabase_service.dart';

part 'scheduling_event.dart';
part 'scheduling_state.dart';

class SchedulingBloc extends Bloc<SchedulingEvent, SchedulingState> {
  final SupabaseService _supabaseService;
  final Logger _log = Logger('SchedulingBloc');

  SchedulingBloc({required SupabaseService supabaseService})
      : _supabaseService = supabaseService,
        super(SchedulingInitial()) {
    on<ScheduleTournamentRequested>(_onScheduleTournamentRequested);
    // Add other event handlers as needed
  }

  Future<void> _onScheduleTournamentRequested(
    ScheduleTournamentRequested event,
    Emitter<SchedulingState> emit,
  ) async {
    _log.info('Scheduling tournament with ID: ${event.tournamentId}');
    emit(SchedulingInProgress());

    try {
      final result = await _supabaseService.scheduleTournament(event.tournamentId);
      _log.info('Tournament scheduled successfully');
      emit(SchedulingSuccess(result: result));
    } catch (e, s) {
      _log.severe('Error scheduling tournament: $e', e, s);
      emit(SchedulingError(message: 'Failed to schedule tournament: $e'));
    }
  }
}
