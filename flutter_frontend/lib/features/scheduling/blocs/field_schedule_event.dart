import 'package:equatable/equatable.dart';
import 'package:soccer_frontend/data/models/match.dart';

abstract class FieldScheduleEvent extends Equatable {
  const FieldScheduleEvent();

  @override
  List<Object?> get props => [];
}

class LoadSchedule extends FieldScheduleEvent {
  final String tournamentId;
  final String fieldId;

  const LoadSchedule({required this.tournamentId, required this.fieldId});

  @override
  List<Object?> get props => [tournamentId, fieldId];
}

class ViewingIntervalChanged extends FieldScheduleEvent {
  final int intervalInMinutes;

  const ViewingIntervalChanged(this.intervalInMinutes);

  @override
  List<Object> get props => [intervalInMinutes];
}

class AssignMatchToSlotRequested extends FieldScheduleEvent {
  final String matchId;
  final String fieldId; // Field to assign to
  final DateTime startTime;
  final int durationInMinutes; // Match duration, could be from match object or default

  const AssignMatchToSlotRequested({
    required this.matchId,
    required this.fieldId,
    required this.startTime,
    required this.durationInMinutes,
  });

  @override
  List<Object> get props => [matchId, fieldId, startTime, durationInMinutes];
}

class ClearMatchFromSlotRequested extends FieldScheduleEvent {
  final String matchId; // ID of the match to clear from its slot

  const ClearMatchFromSlotRequested(this.matchId);

  @override
  List<Object> get props => [matchId];
}

// Internal events for optimistic updates or real-time sync
class MatchAddedInternally extends FieldScheduleEvent {
  final Match match;
  const MatchAddedInternally(this.match);
  @override
  List<Object> get props => [match];
}

class MatchUpdatedInternally extends FieldScheduleEvent {
  final Match match;
  const MatchUpdatedInternally(this.match);
  @override
  List<Object> get props => [match];
}

class MatchRemovedInternally extends FieldScheduleEvent {
  final String matchId;
  const MatchRemovedInternally(this.matchId);
  @override
  List<Object> get props => [matchId];
}
