// lib/src/blocs/scheduling/scheduling_state.dart
part of 'scheduling_bloc.dart';

abstract class SchedulingState extends Equatable {
  const SchedulingState();
  
  @override
  List<Object?> get props => [];
}

class SchedulingInitial extends SchedulingState {}

class SchedulingInProgress extends SchedulingState {}

class SchedulingSuccess extends SchedulingState {
  final Map<String, dynamic> result;

  const SchedulingSuccess({required this.result});

  @override
  List<Object> get props => [result];
}

class SchedulingError extends SchedulingState {
  final String message;

  const SchedulingError({required this.message});

  @override
  List<Object> get props => [message];
}
