import 'dart:async';
import 'dart:math'; // For random error simulation
import 'package:soccer_frontend/data/models/match.dart';
import 'package:soccer_frontend/data/models/team.dart';

// This service was originally designed to interact with a FastAPI backend.
// For now, it will contain mock implementations as per the original diff.
// If it needs to call Supabase RPCs instead, its methods would change.

class SchedulingApiService {
  // Static mock data for demonstration
  // This data should ideally come from a shared mock data source or be injected.
  static final List<Team> _teams = [
    const Team(id: 'team1', name: 'Team Alpha', tournamentId: 'tourney1'),
    const Team(id: 'team2', name: 'Team Beta', tournamentId: 'tourney1'),
    const Team(id: 'team3', name: 'Team Gamma', tournamentId: 'tourney1'),
    const Team(id: 'team4', name: 'Team Delta', tournamentId: 'tourney1'),
    const Team(id: 'team5', name: 'Team Epsilon', tournamentId: 'tourney2'),
    const Team(id: 'team6', name: 'Team Zeta', tournamentId: 'tourney2'),
  ];

  static final List<Match> _matches = [
    Match(id: 'm1', tournamentId: 'tourney1', homeTeamId: 'team1', awayTeamId: 'team2', durationInMinutes: 60, status: 'Scheduled', startTime: DateTime.now().add(const Duration(hours: 1)), scheduledFieldId: 'fieldA'),
    Match(id: 'm2', tournamentId: 'tourney1', homeTeamId: 'team3', awayTeamId: 'team4', durationInMinutes: 75, status: 'Unscheduled', startTime: null, scheduledFieldId: null),
    Match(id: 'm3', tournamentId: 'tourney1', homeTeamId: 'team1', awayTeamId: 'team3', durationInMinutes: 60, status: 'Scheduled', startTime: DateTime.now().add(const Duration(hours: 3)), scheduledFieldId: 'fieldA'),
    Match(id: 'm4', tournamentId: 'tourney1', homeTeamId: 'team2', awayTeamId: 'team4', durationInMinutes: 75, status: 'Unscheduled', startTime: null, scheduledFieldId: null),
    Match(id: 'm5', tournamentId: 'tourney2', homeTeamId: 'team5', awayTeamId: 'team6', durationInMinutes: 90, status: 'Scheduled', startTime: DateTime.now().add(const Duration(hours: 2)), scheduledFieldId: 'fieldC'),
    Match(id: 'm6', tournamentId: 'tourney1', homeTeamId: 'team1', awayTeamId: 'team4', durationInMinutes: 60, status: 'Unscheduled', startTime: null, scheduledFieldId: null),
    Match(id: 'm7', tournamentId: 'tourney1', homeTeamId: 'team2', awayTeamId: 'team3', durationInMinutes: 75, status: 'Scheduled', startTime: DateTime.now().add(const Duration(hours: 1, minutes: 30)), scheduledFieldId: 'fieldB'),
  ];

  Future<List<Match>> loadMatchesForTournament(String tournamentId) async {
    print('SchedulingApiService: Simulating API call to load matches for tournament $tournamentId...');
    await Future.delayed(const Duration(seconds: 1));
    final filteredMatches = _matches.where((match) => match.tournamentId == tournamentId).toList();
    print('SchedulingApiService: Returning ${filteredMatches.length} mock matches for tournament $tournamentId.');
    return filteredMatches;
  }

  Future<List<Team>> loadTeamsForTournament(String tournamentId) async {
    print('SchedulingApiService: Simulating API call to load teams for tournament $tournamentId...');
    await Future.delayed(const Duration(milliseconds: 500));
    final filteredTeams = _teams.where((team) => team.tournamentId == tournamentId).toList();
    print('SchedulingApiService: Returning ${filteredTeams.length} mock teams for tournament $tournamentId.');
    return filteredTeams;
  }

  Future<void> saveMatchAssignment(Match match) async {
    print('SchedulingApiService: Simulating API call to save match assignment...');
    print('SchedulingApiService: Match data to save: ${match.toJson()}');
    await Future.delayed(const Duration(seconds: 1));

    final random = Random();
    if (random.nextDouble() < 0.2) { // 20% chance of error
      print('SchedulingApiService: Simulated network error during saveMatchAssignment!');
      throw Exception('Simulated network error: Failed to save match assignment.');
    }

    // Update local mock data
    final index = _matches.indexWhere((m) => m.id == match.id);
    if (index != -1) {
      _matches[index] = match;
    } else {
      _matches.add(match); 
    }
    print('SchedulingApiService: Match assignment saved successfully (simulated).');
  }

  Future<void> clearMatchAssignment(Match match) async {
    print('SchedulingApiService: Simulating API call to clear match assignment...');
    print('SchedulingApiService: Match data to clear (update to unscheduled): ${match.toJson()}');
    await Future.delayed(const Duration(seconds: 1));

    final index = _matches.indexWhere((m) => m.id == match.id);
    if (index != -1) {
      _matches[index] = match.copyWith(
        scheduledFieldId: null, 
        startTime: null, 
        status: 'Unscheduled'
      ); 
    }
    print('SchedulingApiService: Match assignment cleared successfully (simulated).');
  }
}
