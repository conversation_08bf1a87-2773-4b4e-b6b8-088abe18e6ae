import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:soccer_frontend/features/scheduling/blocs/field_schedule_bloc.dart';
import 'package:soccer_frontend/features/scheduling/blocs/field_schedule_event.dart';
import 'package:soccer_frontend/features/scheduling/blocs/field_schedule_state.dart';
import 'package:soccer_frontend/data/models/match.dart';
import 'package:soccer_frontend/data/models/team.dart';
import 'package:intl/intl.dart'; // For date formatting

class FieldScheduleScreen extends StatefulWidget {
  final String tournamentId;
  final String fieldId;

  const FieldScheduleScreen(
      {super.key, required this.tournamentId, required this.fieldId});

  @override
  State<FieldScheduleScreen> createState() => _FieldScheduleScreenState();
}

class _FieldScheduleScreenState extends State<FieldScheduleScreen> {
  DateTime _selectedDate = DateTime.now(); // Default to today

  @override
  void initState() {
    super.initState();
    // Dispatch initial load event
    context
        .read<FieldScheduleBloc>()
        .add(LoadSchedule(tournamentId: widget.tournamentId, fieldId: widget.fieldId));
  }

  void _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2000), // Allow selection of past dates
      lastDate: DateTime(2101),
    );
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
      // Optionally, reload schedule if it's date-specific from backend
      // For now, filtering is client-side based on _selectedDate
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Schedule - Field ${widget.fieldId}'),
        actions: [
          IconButton(
            icon: const Icon(Icons.calendar_today),
            onPressed: () => _selectDate(context),
            tooltip: 'Select Date',
          ),
          // TODO: Add interval selector if needed
        ],
      ),
      body: BlocConsumer<FieldScheduleBloc, FieldScheduleState>(
        listener: (context, state) {
          if (state is ScheduleLoaded && state.assignmentError != null) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('Error: ${state.assignmentError}')),
            );
          }
          if (state is ScheduleLoaded && state.assignmentSuccess) {
             ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Assignment updated successfully!')),
            );
            // Optionally, reload or refresh data if needed after success
          }
        },
        builder: (context, state) {
          if (state is ScheduleLoading || state is ScheduleInitial) {
            return const Center(child: CircularProgressIndicator());
          }
          if (state is ScheduleError) {
            return Center(child: Text('Error loading schedule: ${state.error}'));
          }
          if (state is ScheduleLoaded) {
            return _buildScheduleView(context, state);
          }
          return const Center(child: Text('Unknown state'));
        },
      ),
    );
  }

  Widget _buildScheduleView(BuildContext context, ScheduleLoaded state) {
    // Filter matches for the selected date
    final matchesForSelectedDate = state.scheduledMatches.where((match) {
      if (match.startTime == null) return false;
      return DateUtils.isSameDay(match.startTime, _selectedDate);
    }).toList();

    // Sort matches by start time
    matchesForSelectedDate.sort((a, b) => a.startTime!.compareTo(b.startTime!));
    
    // Determine time slots based on viewing interval
    final timeSlots = _generateTimeSlots(
        _selectedDate, state.viewingIntervalInMinutes, matchesForSelectedDate);


    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: Text(
            'Displaying schedule for: ${DateFormat.yMMMd().format(_selectedDate)}',
            style: Theme.of(context).textTheme.titleMedium,
          ),
        ),
        Expanded(
          child: ListView.builder(
            itemCount: timeSlots.length,
            itemBuilder: (context, index) {
              final slot = timeSlots[index];
              return _buildTimeSlotItem(context, slot, state);
            },
          ),
        ),
        _buildUnscheduledMatchesList(context, state),
      ],
    );
  }

  List<TimeSlot> _generateTimeSlots(DateTime date, int interval, List<Match> scheduledMatches) {
    final List<TimeSlot> slots = [];
    // Define typical operating hours, e.g., 8 AM to 10 PM
    DateTime startTime = DateTime(date.year, date.month, date.day, 8, 0); // 8:00 AM
    final DateTime endTime = DateTime(date.year, date.month, date.day, 22, 0); // 10:00 PM

    while (startTime.isBefore(endTime)) {
      final slotEndTime = startTime.add(Duration(minutes: interval));
      final matchesInSlot = scheduledMatches.where((match) {
        // A match is in the slot if it starts within the slot
        // or if it starts before and ends within or after the slot
        if (match.startTime == null) return false;
        final matchEndTime = match.startTime!.add(Duration(minutes: match.durationInMinutes));
        return (match.startTime!.isAtSameMomentAs(startTime) || match.startTime!.isAfter(startTime)) && match.startTime!.isBefore(slotEndTime) || // Starts in slot
               (match.startTime!.isBefore(startTime) && matchEndTime.isAfter(startTime)); // Overlaps slot start
      }).toList();
      
      slots.add(TimeSlot(startTime: startTime, matches: matchesInSlot));
      startTime = slotEndTime;
    }
    return slots;
  }

  Widget _buildTimeSlotItem(BuildContext context, TimeSlot slot, ScheduleLoaded state) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      child: ListTile(
        title: Text(DateFormat.jm().format(slot.startTime)), // Format time e.g., 8:00 AM
        subtitle: slot.matches.isEmpty
            ? const Text('Available')
            : Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: slot.matches.map((match) {
                  final homeTeam = state.availableTeams.firstWhere((t) => t.id == match.homeTeamId, orElse: () => Team(id: match.homeTeamId, name: 'Unknown Team', tournamentId: state.currentTournamentId));
                  final awayTeam = state.availableTeams.firstWhere((t) => t.id == match.awayTeamId, orElse: () => Team(id: match.awayTeamId, name: 'Unknown Team', tournamentId: state.currentTournamentId));
                  return Text('${homeTeam.name} vs ${awayTeam.name} (${match.status})');
                }).toList(),
              ),
        onTap: () {
          // Allow assigning a match if the slot is "empty" or to manage existing
          _showAssignMatchDialog(context, slot.startTime, state);
        },
        trailing: slot.matches.isNotEmpty ? IconButton(
          icon: const Icon(Icons.clear),
          tooltip: 'Clear Slot',
          onPressed: (){
            // Assuming one match per slot for simplicity in this clear action
            if(slot.matches.isNotEmpty) {
              context.read<FieldScheduleBloc>().add(ClearMatchFromSlotRequested(slot.matches.first.id));
            }
          },
        ) : null,
      ),
    );
  }

  Widget _buildUnscheduledMatchesList(BuildContext context, ScheduleLoaded state) {
    if (state.unscheduledMatches.isEmpty) {
      return const SizedBox.shrink(); // No unscheduled matches to show
    }
    return Container(
      padding: const EdgeInsets.all(8.0),
      decoration: BoxDecoration(
        color: Colors.grey[200],
        border: Border(top: BorderSide(color: Colors.grey[400]!))
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 8.0),
            child: Text('Unscheduled Matches:', style: Theme.of(context).textTheme.titleMedium),
          ),
          SizedBox(
            height: 120, // Fixed height for the list
            child: ListView.builder(
              itemCount: state.unscheduledMatches.length,
              itemBuilder: (context, index) {
                final match = state.unscheduledMatches[index];
                final homeTeam = state.availableTeams.firstWhere((t) => t.id == match.homeTeamId, orElse: () => Team(id: match.homeTeamId, name: 'Unknown', tournamentId: state.currentTournamentId));
                final awayTeam = state.availableTeams.firstWhere((t) => t.id == match.awayTeamId, orElse: () => Team(id: match.awayTeamId, name: 'Unknown', tournamentId: state.currentTournamentId));
                return ListTile(
                  title: Text('${homeTeam.name} vs ${awayTeam.name}'),
                  subtitle: Text('Duration: ${match.durationInMinutes} min'),
                  dense: true,
                  // Tapping an unscheduled match could pre-fill it in the assignment dialog
                  onTap: () => _showAssignMatchDialog(context, _selectedDate.copyWith(hour: 12, minute: 0), state, preSelectedMatchId: match.id),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  void _showAssignMatchDialog(BuildContext context, DateTime slotStartTime, ScheduleLoaded state, {String? preSelectedMatchId}) {
    String? selectedMatchId = preSelectedMatchId ?? (state.unscheduledMatches.isNotEmpty ? state.unscheduledMatches.first.id : null);
    
    if (state.unscheduledMatches.isEmpty && preSelectedMatchId == null) {
      ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text("No unscheduled matches available to assign.")));
      return;
    }

    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: Text('Assign Match to ${DateFormat.jm().format(slotStartTime)}'),
          content: StatefulBuilder( // Use StatefulBuilder for local state in dialog
            builder: (BuildContext context, StateSetter setState) {
              return DropdownButtonFormField<String>(
                decoration: const InputDecoration(labelText: 'Select Match'),
                value: selectedMatchId,
                items: state.unscheduledMatches.map((match) {
                  final homeTeam = state.availableTeams.firstWhere((t) => t.id == match.homeTeamId, orElse: () => Team(id: match.homeTeamId, name: '?', tournamentId: state.currentTournamentId));
                  final awayTeam = state.availableTeams.firstWhere((t) => t.id == match.awayTeamId, orElse: () => Team(id: match.awayTeamId, name: '?', tournamentId: state.currentTournamentId));
                  return DropdownMenuItem<String>(
                    value: match.id,
                    child: Text('${homeTeam.name} vs ${awayTeam.name} (${match.durationInMinutes} min)'),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  setState(() { // Use StateSetter to update dialog's local state
                    selectedMatchId = newValue;
                  });
                },
                validator: (value) => value == null ? 'Please select a match' : null,
              );
            }
          ),
          actions: <Widget>[
            TextButton(
              child: const Text('Cancel'),
              onPressed: () => Navigator.of(dialogContext).pop(),
            ),
            TextButton(
              child: const Text('Assign'),
              onPressed: () {
                if (selectedMatchId != null) {
                  final matchToAssign = state.unscheduledMatches.firstWhere((m) => m.id == selectedMatchId);
                  context.read<FieldScheduleBloc>().add(
                        AssignMatchToSlotRequested(
                          matchId: selectedMatchId!,
                          fieldId: state.currentFieldId,
                          startTime: slotStartTime,
                          durationInMinutes: matchToAssign.durationInMinutes,
                        ),
                      );
                  Navigator.of(dialogContext).pop();
                }
              },
            ),
          ],
        );
      },
    );
  }
}

// Helper class for time slots
class TimeSlot {
  final DateTime startTime;
  final List<Match> matches; // Matches scheduled in this slot

  TimeSlot({required this.startTime, this.matches = const []});
}
