// lib/features/bracket/blocs/bracket_bloc.dart
import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:logging/logging.dart';

import '../../../core/services/supabase_service.dart';

part 'bracket_event.dart';
part 'bracket_state.dart';

class BracketBloc extends Bloc<BracketEvent, BracketState> {
  final SupabaseService _supabaseService;
  final Logger _log = Logger('BracketBloc');

  BracketBloc({required SupabaseService supabaseService})
      : _supabaseService = supabaseService,
        super(BracketInitial()) {
    on<GenerateBracketRequested>(_onGenerateBracketRequested);
    // Add other event handlers as needed
  }

  Future<void> _onGenerateBracketRequested(
    GenerateBracketRequested event,
    Emitter<BracketState> emit,
  ) async {
    _log.info('Generating bracket for tournament ID: ${event.tournamentId}');
    emit(BracketGenerating());

    try {
      final result = await _supabaseService.generateBracket(event.tournamentId);
      _log.info('Bracket generated successfully');
      emit(BracketGenerated(result: result));
    } catch (e, s) {
      _log.severe('Error generating bracket: $e', e, s);
      emit(BracketError(message: 'Failed to generate bracket: $e'));
    }
  }
}
