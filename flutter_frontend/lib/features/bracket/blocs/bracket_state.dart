// lib/src/blocs/bracket/bracket_state.dart
part of 'bracket_bloc.dart';

abstract class BracketState extends Equatable {
  const BracketState();
  
  @override
  List<Object?> get props => [];
}

class BracketInitial extends BracketState {}

class BracketGenerating extends BracketState {}

class BracketGenerated extends BracketState {
  final Map<String, dynamic> result;

  const BracketGenerated({required this.result});

  @override
  List<Object> get props => [result];
}

class BracketError extends BracketState {
  final String message;

  const BracketError({required this.message});

  @override
  List<Object> get props => [message];
}
