// lib/src/blocs/match/match_state.dart
part of 'match_bloc.dart';

abstract class MatchState extends Equatable {
  const MatchState();
  
  @override
  List<Object?> get props => [];
}

class MatchInitial extends MatchState {}

class MatchLoading extends MatchState {}

class MatchLoaded extends MatchState {
  final List<Map<String, dynamic>> matches;

  const MatchLoaded({required this.matches});

  @override
  List<Object> get props => [matches];
}

class MatchError extends MatchState {
  final String message;

  const MatchError({required this.message});

  @override
  List<Object> get props => [message];
}
