// lib/features/match/blocs/match_bloc.dart
import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:logging/logging.dart';

import '../../../core/services/supabase_service.dart';

part 'match_event.dart';
part 'match_state.dart';

class MatchBloc extends Bloc<MatchEvent, MatchState> {
  final SupabaseService _supabaseService;
  final Logger _log = Logger('MatchBloc');

  MatchBloc({required SupabaseService supabaseService})
      : _supabaseService = supabaseService,
        super(MatchInitial()) {
    on<MatchLoadByTournamentRequested>(_onMatchLoadByTournamentRequested);
    // Add other event handlers as needed
  }

  Future<void> _onMatchLoadByTournamentRequested(
    MatchLoadByTournamentRequested event,
    Emitter<MatchState> emit,
  ) async {
    _log.info('Loading matches for tournament ID: ${event.tournamentId}');
    emit(MatchLoading());

    try {
      final matches = await _supabaseService.getMatchesByTournamentId(event.tournamentId);
      _log.info('Loaded ${matches.length} matches');
      emit(MatchLoaded(matches: matches));
    } catch (e, s) {
      _log.severe('Error loading matches: $e', e, s);
      emit(MatchError(message: 'Failed to load matches: $e'));
    }
  }
}
