import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart'; // Import GoRouter
import '../../../features/auth/blocs/auth_bloc.dart'; // Adjusted import path
// Import AppRouter for route names

class DirectorDashboardScreen extends StatelessWidget {
  const DirectorDashboardScreen({super.key});

  static const String routeName = '/director-dashboard';

  @override
  Widget build(BuildContext context) {
    final authState = context.watch<AuthBloc>().state;
    String directorName = 'Director'; // Default name

    if (authState is AuthAuthenticated) {
      directorName = authState.userProfile?.firstName ?? directorName;
      if (directorName == 'Director' && (authState.userProfile?.fullName.isNotEmpty ?? false)) {
        directorName = authState.userProfile!.fullName;
      }
    }

    // Placeholder: Check for pending affiliations
    // bool hasPendingAffiliations = authState is AuthAuthenticated &&
    //     (authState.directorAffiliations?.any((aff) => aff.status == 'pending_approval' || aff.status == 'pending') ?? false);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Director Dashboard'),
        actions: [
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: () {
              context.read<AuthBloc>().add(AuthSignOutRequested());
            },
            tooltip: 'Sign Out',
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Welcome, $directorName!',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 24),

            // Placeholder for pending affiliations message
            // if (hasPendingAffiliations)
            //   Card(
            //     color: Colors.amber[100],
            //     child: Padding(
            //       padding: const EdgeInsets.all(12.0),
            //       child: Row(
            //         children: [
            //           Icon(Icons.info_outline, color: Colors.amber[800]),
            //           const SizedBox(width: 10),
            //           Expanded(
            //             child: Text(
            //               'You have pending club affiliation requests. Please review them in your profile.',
            //                style: TextStyle(color: Colors.amber[800]),
            //             ),
            //           ),
            //         ],
            //       ),
            //     ),
            //   ),
            // const SizedBox(height: 16),

            Text(
              'Quick Actions:',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            _buildQuickActionButton(
              context,
              icon: Icons.add_circle_outline,
              label: 'Create New Tournament',
              onPressed: () {
                context.go('/create-tournament/step1');
              },
            ),
            const SizedBox(height: 12),
            _buildQuickActionButton(
              context,
              icon: Icons.list_alt,
              label: 'View My Tournaments',
              onPressed: () {
                context.goNamed('myTournaments'); // Navigate to MyTournamentsScreen
              },
            ),
            const SizedBox(height: 12),
            _buildQuickActionButton(
              context,
              icon: Icons.person_outline,
              label: 'Manage Profile & Affiliations',
              onPressed: () {
                context.goNamed('profile');
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionButton(BuildContext context, {required IconData icon, required String label, required VoidCallback onPressed}) {
    return ElevatedButton.icon(
      icon: Icon(icon),
      label: Text(label),
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        minimumSize: const Size(double.infinity, 50), // Make buttons full width
        alignment: Alignment.centerLeft,
        textStyle: const TextStyle(fontSize: 16),
      ),
    );
  }
}
