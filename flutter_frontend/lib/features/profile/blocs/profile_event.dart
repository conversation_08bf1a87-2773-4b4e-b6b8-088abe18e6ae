// lib/src/blocs/profile/profile_event.dart
part of 'profile_bloc.dart';

abstract class ProfileEvent extends Equatable {
  const ProfileEvent();

  @override
  List<Object?> get props => [];
}

class ProfileLoadRequested extends ProfileEvent {
  final String userId;

  const ProfileLoadRequested({required this.userId});

  @override
  List<Object> get props => [userId];
}

class ProfileUpdateRequested extends ProfileEvent {
  final String userId;
  final Map<String, dynamic> data;

  const ProfileUpdateRequested({
    required this.userId,
    required this.data,
  });

  @override
  List<Object> get props => [userId, data];
}

class ProfileAuthStateChanged extends ProfileEvent {
  final UserProfile? profile;

  const ProfileAuthStateChanged({required this.profile});

  @override
  List<Object?> get props => [profile];
}
