// lib/src/blocs/profile/profile_state.dart
part of 'profile_bloc.dart';

abstract class ProfileState extends Equatable {
  final UserProfile? profile;
  
  const ProfileState({this.profile});
  
  @override
  List<Object?> get props => [profile];
}

class ProfileInitial extends ProfileState {
  const ProfileInitial() : super();
}

class ProfileLoading extends ProfileState {
  const ProfileLoading({UserProfile? profile}) : super(profile: profile);
}

class ProfileLoaded extends ProfileState {
  const ProfileLoaded({required UserProfile profile}) : super(profile: profile);
}

class ProfileError extends ProfileState {
  final String message;
  final dynamic error;

  const ProfileError({
    required this.message,
    this.error,
    UserProfile? profile,
  }) : super(profile: profile);

  @override
  List<Object?> get props => [message, error, profile];
}
