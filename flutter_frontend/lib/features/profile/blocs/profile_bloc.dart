// lib/features/profile/blocs/profile_bloc.dart
import 'dart:async';
import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:logging/logging.dart';

import '../../../data/models/user_profile.dart';
import '../../../core/interfaces/auth_service_interface.dart';
import '../../../core/interfaces/supabase_service_interface.dart';
import '../../../core/utils/app_exception.dart';
import '../../auth/blocs/auth_bloc.dart';

part 'profile_event.dart';
part 'profile_state.dart';

class ProfileBloc extends Bloc<ProfileEvent, ProfileState> {
  final AuthServiceInterface _authService;
  final SupabaseServiceInterface _supabaseService;
  final AuthBloc _authBloc;
  final Logger _log = Logger('ProfileBloc');
  StreamSubscription? _authSubscription;

  ProfileBloc({
    required AuthServiceInterface authService,
    required SupabaseServiceInterface supabaseService,
    required AuthBloc authBloc,
  })  : _authService = authService,
        _supabaseService = supabaseService,
        _authBloc = authBloc,
        super(ProfileInitial()) {
    on<ProfileLoadRequested>(_onProfileLoadRequested);
    on<ProfileUpdateRequested>(_onProfileUpdateRequested);
    on<ProfileAuthStateChanged>(_onProfileAuthStateChanged);

    // Listen to AuthBloc state changes
    _authSubscription = _authBloc.stream.listen((authState) {
      if (authState is AuthAuthenticated) {
        add(ProfileAuthStateChanged(profile: authState.userProfile));
      } else if (authState is AuthUnauthenticated) {
        add(const ProfileAuthStateChanged(profile: null));
      }
    });
  }

  @override
  Future<void> close() {
    _authSubscription?.cancel();
    return super.close();
  }

  Future<void> _onProfileLoadRequested(
    ProfileLoadRequested event,
    Emitter<ProfileState> emit,
  ) async {
    _log.info('Loading profile for user ID: ${event.userId}');
    emit(ProfileLoading());

    try {
      final profile = await _authService.getUserProfile(event.userId);
      if (profile != null) {
        _log.info('Profile loaded successfully');
        emit(ProfileLoaded(profile: profile));
      } else {
        _log.warning('Profile not found for user ID: ${event.userId}');
        emit(const ProfileError(message: 'Profile not found'));
      }
    } catch (e, s) {
      _log.severe('Error loading profile: $e', e, s);
      emit(ProfileError(
        message: e is AppException ? e.message : 'Failed to load profile',
        error: e,
      ));
    }
  }

  Future<void> _onProfileUpdateRequested(
    ProfileUpdateRequested event,
    Emitter<ProfileState> emit,
  ) async {
    _log.info('Updating profile for user ID: ${event.userId}');

    // Keep the current profile in the loading state
    final currentProfile = state is ProfileLoaded
        ? (state as ProfileLoaded).profile
        : null;

    emit(ProfileLoading(profile: currentProfile));

    try {
      await _authService.updateProfile(event.userId, event.data);

      // Fetch the updated profile
      final updatedProfile = await _authService.getUserProfile(event.userId);

      if (updatedProfile != null) {
        _log.info('Profile updated successfully');
        emit(ProfileLoaded(profile: updatedProfile));
      } else {
        _log.warning('Updated profile not found');
        emit(const ProfileError(message: 'Failed to retrieve updated profile'));
      }
    } catch (e, s) {
      _log.severe('Error updating profile: $e', e, s);
      emit(ProfileError(
        message: e is AppException ? e.message : 'Failed to update profile',
        error: e,
        profile: currentProfile, // Keep the current profile on error
      ));
    }
  }

  Future<void> _onProfileAuthStateChanged(
    ProfileAuthStateChanged event,
    Emitter<ProfileState> emit,
  ) async {
    _log.info('Auth state changed, profile: ${event.profile?.id ?? 'null'}');

    if (event.profile != null) {
      emit(ProfileLoaded(profile: event.profile!));
    } else {
      emit(ProfileInitial());
    }
  }
}
