import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart'; // Import GoRouter
import 'package:intl/intl.dart'; // For date formatting

import '../../../features/auth/blocs/auth_bloc.dart';
import '../../../data/models/club_affiliation.dart'; // Import ClubAffiliation
// import '../../../core/router/app_router.dart'; // For navigation if needed later

class ProfileScreen extends StatelessWidget {
  const ProfileScreen({super.key});

  Widget _buildSectionTitle(BuildContext context, String title) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16.0),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
      ),
    );
  }

  Widget _buildProfileDetailItem(BuildContext context, String label, String? value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500)),
          Text(value ?? 'N/A', style: const TextStyle(fontSize: 16)),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Manage Profile & Affiliations'),
        actions: [
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: () {
              context.read<AuthBloc>().add(AuthSignOutRequested());
            },
            tooltip: 'Sign Out',
          ),
        ],
      ),
      body: BlocBuilder<AuthBloc, AuthState>(
        builder: (context, state) {
          if (state is AuthAuthenticated) {
            final user = state.user;
            final profile = state.userProfile;
            final affiliations = state.directorAffiliations;
            // Corrected role check to be case-insensitive or match the exact string from the profile
            final bool isDirector = profile?.role?.toLowerCase() == 'tournament_director';

            // Determine if director is independent
            final bool isIndependentDirector = isDirector && (affiliations?.every((aff) => aff.status != 'approved') ?? true);

            return Padding(
              padding: const EdgeInsets.all(16.0),
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Center(
                      child: Column(
                        children: [
                          CircleAvatar(
                            radius: 50,
                            backgroundColor: Theme.of(context).primaryColor,
                            child: Text(
                              (profile?.fullName.isNotEmpty ?? false)
                                  ? profile!.fullName.substring(0, 1).toUpperCase()
                                  : '?',
                              style: const TextStyle(
                                fontSize: 36,
                                color: Colors.white,
                              ),
                            ),
                          ),
                          const SizedBox(height: 12),
                          Text(
                            (profile?.fullName.isNotEmpty ?? false) ? profile!.fullName : 'User',
                            style: Theme.of(context).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            user?.email ?? 'No email',
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(color: Colors.grey),
                          ),
                           const SizedBox(height: 4),
                          Text(
                            'Role: ${profile?.role ?? 'Unknown'}',
                            style: Theme.of(context).textTheme.titleMedium,
                          ),
                        ],
                      ),
                    ),
                    
                    _buildSectionTitle(context, 'Personal Information'),
                    _buildProfileDetailItem(context, 'First Name:', profile?.firstName),
                    _buildProfileDetailItem(context, 'Last Name:', profile?.lastName),
                    _buildProfileDetailItem(context, 'Phone:', profile?.phoneNumber),
                    // _buildProfileDetailItem(context, 'Location (City):', profile?.city), // City field not in UserProfile model yet
                    const SizedBox(height: 8),
                    ElevatedButton.icon(
                      icon: const Icon(Icons.edit_outlined),
                      label: const Text('Edit Personal Details'),
                      onPressed: () {
                        if (profile != null) {
                          // Navigate to EditProfileDetailsScreen
                          // Ensure EditProfileDetailsScreen.routeName is defined and handled in your router
                          GoRouter.of(context).pushNamed('editProfileDetails', extra: profile);
                        } else {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(content: Text('Profile data not available to edit.')),
                          );
                        }
                      },
                      style: ElevatedButton.styleFrom(minimumSize: const Size(double.infinity, 40)),
                    ),

                    _buildSectionTitle(context, 'Account Security'),
                    ElevatedButton.icon(
                      icon: const Icon(Icons.lock_outline),
                      label: const Text('Change Password'),
                      onPressed: () {
                        GoRouter.of(context).pushNamed('changePassword');
                      },
                      style: ElevatedButton.styleFrom(minimumSize: const Size(double.infinity, 40)),
                    ),

                    if (isDirector) ...[
                      _buildSectionTitle(context, 'Club Affiliations'),
                      if (isIndependentDirector)
                        Padding(
                          padding: const EdgeInsets.symmetric(vertical: 8.0),
                          child: Text(
                            'Currently operating as an Independent Director.',
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(fontStyle: FontStyle.italic),
                          ),
                        ),
                      
                      if (affiliations != null && affiliations.isNotEmpty)
                        ListView.builder(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          itemCount: affiliations.length,
                          itemBuilder: (context, index) {
                            final affiliation = affiliations[index];
                            String formattedDate = affiliation.createdAt != null 
                                ? DateFormat.yMMMd().format(affiliation.createdAt!) 
                                : 'N/A';
                            String statusDisplay = affiliation.status.replaceAll('_', ' ').capitalizeFirstLetter();

                            return Card(
                              margin: const EdgeInsets.symmetric(vertical: 6.0),
                              child: Padding(
                                padding: const EdgeInsets.all(12.0),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(affiliation.clubName, style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold)),
                                    const SizedBox(height: 4),
                                    Text('Status: $statusDisplay'),
                                    Text('Since: $formattedDate'),
                                    const SizedBox(height: 8),
                                    Row(
                                      mainAxisAlignment: MainAxisAlignment.end,
                                      children: [
                                        if (affiliation.status == 'pending_director_acceptance') // Example status
                                          TextButton(onPressed: () {
                                             // TODO: Implement accept affiliation
                                             ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('Accept ${affiliation.clubName} (TODO)')));
                                          }, child: const Text('ACCEPT')),
                                        if (affiliation.status == 'pending_director_acceptance') // Example status
                                          TextButton(onPressed: () {
                                             // TODO: Implement decline affiliation
                                             ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('Decline ${affiliation.clubName} (TODO)')));
                                          }, child: Text('DECLINE', style: TextStyle(color: Theme.of(context).colorScheme.error))),
                                        if (affiliation.status == 'pending_club_approval') // Example status
                                          TextButton(onPressed: () {
                                             // TODO: Implement withdraw request
                                             ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('Withdraw from ${affiliation.clubName} (TODO)')));
                                          }, child: const Text('WITHDRAW')),
                                        // Add other contextual buttons like "Leave Club" if status is 'approved'
                                      ],
                                    )
                                  ],
                                ),
                              ),
                            );
                          },
                        )
                      else if (!isIndependentDirector) // If not independent but list is empty (should not happen if logic is correct)
                        const Padding(
                          padding: EdgeInsets.symmetric(vertical: 8.0),
                          child: Text('No affiliations to display.', style: TextStyle(fontStyle: FontStyle.italic)),
                        ),
                      const SizedBox(height: 16),
                      ElevatedButton.icon(
                        icon: const Icon(Icons.group_add_outlined),
                        label: const Text('Request New Club Affiliation'),
                        onPressed: () {
                          // TODO: Navigate to a screen/dialog to search and request affiliation
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(content: Text('Request New Club Affiliation (TODO)')),
                          );
                        },
                        style: ElevatedButton.styleFrom(minimumSize: const Size(double.infinity, 40)),
                      ),
                    ],
                  ],
                ),
              ),
            );
          } else if (state is AuthLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          } else {
            return const Center(
              child: Text('Not authenticated or error loading profile.'),
            );
          }
        },
      ),
    );
  }
}

// Helper extension for capitalizing strings
extension StringExtension on String {
    String capitalizeFirstLetter() {
      if (isEmpty) return this;
      return "${this[0].toUpperCase()}${substring(1)}";
    }
}
