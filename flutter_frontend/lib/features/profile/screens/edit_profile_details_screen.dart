import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../data/models/user_profile.dart';
import '../../../features/auth/blocs/auth_bloc.dart';
// import '../../../core/services/auth_service.dart'; // Might be needed if calling service directly, but prefer BLoC

class EditProfileDetailsScreen extends StatefulWidget {
  final UserProfile userProfile;

  const EditProfileDetailsScreen({super.key, required this.userProfile});

  static const String routeName = '/edit-profile-details'; // For navigation

  @override
  State<EditProfileDetailsScreen> createState() => _EditProfileDetailsScreenState();
}

class _EditProfileDetailsScreenState extends State<EditProfileDetailsScreen> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _firstNameController;
  late TextEditingController _lastNameController;
  late TextEditingController _phoneNumberController;
  // late TextEditingController _cityController; // For when city is added

  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _firstNameController = TextEditingController(text: widget.userProfile.firstName ?? '');
    _lastNameController = TextEditingController(text: widget.userProfile.lastName ?? '');
    _phoneNumberController = TextEditingController(text: widget.userProfile.phoneNumber ?? '');
    // _cityController = TextEditingController(text: widget.userProfile.city ?? '');
  }

  @override
  void dispose() {
    _firstNameController.dispose();
    _lastNameController.dispose();
    _phoneNumberController.dispose();
    // _cityController.dispose();
    super.dispose();
  }

  Future<void> _submitForm() async {
    if (_formKey.currentState!.validate()) {
      setState(() => _isLoading = true);
      
      final updatedData = {
        'first_name': _firstNameController.text.trim(),
        'last_name': _lastNameController.text.trim(),
        'phone_number': _phoneNumberController.text.trim(),
        // 'city': _cityController.text.trim(), // For when city is added
      };

      // Remove empty fields to avoid overwriting existing data with nulls if not intended
      updatedData.removeWhere((key, value) => value.isEmpty && widget.userProfile.toJson()[key] != null);


      try {
        // Option 1: Dispatch an event to AuthBloc (preferred)
        // This assumes AuthBloc will have an event like AuthProfileUpdateRequested
        // and AuthServiceInterface has an updateProfile method.
        // We've already added updateProfile to AuthServiceInterface and AuthService.
        // We'll need to add an event and handler in AuthBloc.
        
        context.read<AuthBloc>().add(
          AuthProfileUpdateRequested(userId: widget.userProfile.id, data: updatedData),
        );
        
        // Listen for state changes to show success/error and pop
        // This assumes AuthBloc will emit a state that indicates success or failure of the update
        // For simplicity, we'll pop immediately after dispatching.
        // A more robust solution would listen to the AuthBloc stream for a specific success/failure state.
        if (mounted) {
           ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Profile update request sent.')),
          );
          Navigator.of(context).pop();
        }

      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Failed to update profile: $e')),
          );
        }
      } finally {
        if (mounted) {
          setState(() => _isLoading = false);
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Edit Personal Details'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: ListView( // Changed to ListView for scrollability on small screens
            children: <Widget>[
              TextFormField(
                controller: _firstNameController,
                decoration: const InputDecoration(labelText: 'First Name'),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter your first name';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _lastNameController,
                decoration: const InputDecoration(labelText: 'Last Name'),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter your last name';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _phoneNumberController,
                decoration: const InputDecoration(labelText: 'Phone Number'),
                keyboardType: TextInputType.phone,
                // Add validator if needed, e.g., for format
              ),
              // const SizedBox(height: 16),
              // TextFormField(
              //   controller: _cityController,
              //   decoration: const InputDecoration(labelText: 'City'),
              // ),
              const SizedBox(height: 32),
              _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : ElevatedButton(
                      onPressed: _submitForm,
                      child: const Text('Save Changes'),
                      style: ElevatedButton.styleFrom(minimumSize: const Size(double.infinity, 50)),
                    ),
            ],
          ),
        ),
      ),
    );
  }
}
