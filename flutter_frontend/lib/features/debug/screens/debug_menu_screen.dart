import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class DebugMenuScreen extends StatelessWidget {
  const DebugMenuScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Debug Menu'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'Debug Tools',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 24),
            _buildDebugButton(
              context,
              'Auth Debug',
              '/debug/auth',
              Icons.security,
              Colors.blue,
            ),
            const SizedBox(height: 16),
            _buildDebugButton(
              context,
              'Connection Test',
              '/debug/connection',
              Icons.wifi,
              Colors.green,
            ),
            const SizedBox(height: 16),
            _buildDebugButton(
              context,
              'Data Seeder',
              '/debug/seeder',
              Icons.data_array,
              Colors.orange,
            ),
            const SizedBox(height: 16),
            _buildDebugButton(
              context,
              'Performance',
              '/debug/performance',
              Icons.speed,
              Colors.purple,
            ),
            const SizedBox(height: 16),
            _buildDebugButton(
              context,
              'Supabase Test',
              '/debug/supabase',
              Icons.storage,
              Colors.teal,
            ),
            const Spacer(),
            OutlinedButton(
              onPressed: () => context.go('/'),
              child: const Text('Back to Home'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDebugButton(
    BuildContext context,
    String title,
    String route,
    IconData icon,
    Color color,
  ) {
    return ElevatedButton.icon(
      onPressed: () => context.go(route),
      icon: Icon(icon, color: Colors.white),
      label: Text(title, style: const TextStyle(color: Colors.white)),
      style: ElevatedButton.styleFrom(
        backgroundColor: color,
        padding: const EdgeInsets.symmetric(vertical: 16),
      ),
    );
  }
}
