import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class DataSeederScreen extends StatelessWidget {
  const DataSeederScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Data Seeder'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text(
              'Data Seeder Screen',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 32),
            ElevatedButton(
              onPressed: () => context.go('/debug'),
              child: const Text('Back to Debug Menu'),
            ),
          ],
        ),
      ),
    );
  }
}
