// lib/src/blocs/team/team_state.dart
part of 'team_bloc.dart';

abstract class TeamState extends Equatable {
  const TeamState();
  
  @override
  List<Object?> get props => [];
}

class TeamInitial extends TeamState {}

class TeamLoading extends TeamState {}

class TeamLoaded extends TeamState {
  final List<Map<String, dynamic>> teams;

  const TeamLoaded({required this.teams});

  @override
  List<Object> get props => [teams];
}

class TeamError extends TeamState {
  final String message;

  const TeamError({required this.message});

  @override
  List<Object> get props => [message];
}
