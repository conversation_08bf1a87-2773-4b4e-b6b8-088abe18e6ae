// lib/features/team/blocs/team_bloc.dart
import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:logging/logging.dart';

import '../../../core/services/supabase_service.dart';

part 'team_event.dart';
part 'team_state.dart';

class TeamBloc extends Bloc<TeamEvent, TeamState> {
  final SupabaseService _supabaseService;
  final Logger _log = Logger('TeamBloc');

  TeamBloc({required SupabaseService supabaseService})
      : _supabaseService = supabaseService,
        super(TeamInitial()) {
    on<TeamLoadByTournamentRequested>(_onTeamLoadByTournamentRequested);
    // Add other event handlers as needed
  }

  Future<void> _onTeamLoadByTournamentRequested(
    TeamLoadByTournamentRequested event,
    Emitter<TeamState> emit,
  ) async {
    _log.info('Loading teams for tournament ID: ${event.tournamentId}');
    emit(TeamLoading());

    try {
      final teams = await _supabaseService.getTeamsByTournamentId(event.tournamentId);
      _log.info('Loaded ${teams.length} teams');
      emit(TeamLoaded(teams: teams));
    } catch (e, s) {
      _log.severe('Error loading teams: $e', e, s);
      emit(TeamError(message: 'Failed to load teams: $e'));
    }
  }
}
