// soccer_frontend/features/tournament/presentation/screens/field_form_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:soccer_frontend/data/models/field.dart';
import 'package:soccer_frontend/features/auth/blocs/auth_bloc.dart';
import 'package:soccer_frontend/services/field_service.dart';
import 'package:soccer_frontend/features/tournament/blocs/create_tournament/create_tournament_bloc.dart';
import 'package:soccer_frontend/features/tournament/blocs/create_tournament/create_tournament_event.dart';
import 'package:intl/intl.dart'; // For TimeOfDay.format context

class FieldFormScreen extends StatefulWidget {
  final String venueId;
  final Field? field; // null for creating new field

  const FieldFormScreen({
    super.key,
    required this.venueId,
    this.field,
  });

  @override
  State<FieldFormScreen> createState() => _FieldFormScreenState();
}

class _FieldFormScreenState extends State<FieldFormScreen> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _nameController;
  late TextEditingController _notesController;

  String? _selectedSurfaceType;
  String? _selectedLocationType;
  String? _selectedSize;
  // bool _hasLights = false; // This field is not used in the UI or Field model
  bool _isLoading = false;

  TimeOfDay? _startTime;
  TimeOfDay? _endTime;

  final List<String> _surfaceTypes = [
    'Natural Grass',
    'Artificial Turf',
    'Hybrid Grass',
    'Sand',
    'Clay',
  ];

  final List<String> _locationTypes = [
    'Outdoor',
    'Indoor',
    'Covered',
  ];

  final List<String> _fieldSizes = [
    '4v4',
    '7v7',
    '9v9',
    '11v11',
    'Full Size',
    'Half Size',
    'Quarter Size',
  ];

  bool get _isEditing => widget.field != null;

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController();
    _notesController = TextEditingController();

    if (_isEditing) {
      _populateFormWithExistingData();
    }
  }

  void _populateFormWithExistingData() {
    final field = widget.field!;
    _nameController.text = field.nameOrNumber;
    _notesController.text = field.notes ?? '';

    // Validate that the field's values are in our dropdown lists, otherwise set to null
    if (field.surfaceType != null && _surfaceTypes.contains(field.surfaceType)) {
      _selectedSurfaceType = field.surfaceType;
    } else {
      _selectedSurfaceType = null;
      if (field.surfaceType != null) {
        print('DEBUG: Field surface type "${field.surfaceType}" not found in surface types list');
      }
    }

    if (field.locationType != null && _locationTypes.contains(field.locationType)) {
      _selectedLocationType = field.locationType;
    } else {
      _selectedLocationType = null;
      if (field.locationType != null) {
        print('DEBUG: Field location type "${field.locationType}" not found in location types list');
      }
    }

    if (field.size != null && _fieldSizes.contains(field.size)) {
      _selectedSize = field.size;
    } else {
      _selectedSize = null;
      if (field.size != null) {
        print('DEBUG: Field size "${field.size}" not found in field sizes list');
      }
    }

    if (field.fieldStartTime != null) {
      _startTime = TimeOfDay.fromDateTime(field.fieldStartTime!);
    }
    if (field.fieldEndTime != null) {
      _endTime = TimeOfDay.fromDateTime(field.fieldEndTime!);
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _selectTime(BuildContext context, bool isStartTime) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: isStartTime
          ? (_startTime ?? TimeOfDay.now())
          : (_endTime ?? TimeOfDay.now()),
    );

    if (picked != null) {
      setState(() {
        if (isStartTime) {
          _startTime = picked;
        } else {
          _endTime = picked;
        }
      });
    }
  }

  DateTime? _timeOfDayToDateTime(TimeOfDay? timeOfDay) {
    if (timeOfDay == null) return null;
    final now = DateTime.now();
    return DateTime(now.year, now.month, now.day, timeOfDay.hour, timeOfDay.minute);
  }

  Future<void> _saveField() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final field = Field(
        id: _isEditing ? widget.field!.id : null,
        venueId: widget.venueId,
        nameOrNumber: _nameController.text.trim(),
        surfaceType: _selectedSurfaceType,
        size: _selectedSize,
        status: 'Open', // Assuming 'Open' is default status for new fields
        fieldStartTime: _timeOfDayToDateTime(_startTime),
        fieldEndTime: _timeOfDayToDateTime(_endTime),
        notes: _notesController.text.trim().isEmpty ? null : _notesController.text.trim(),
        locationType: _selectedLocationType,
      );

      print('DEBUG FieldForm: ${_isEditing ? "Updating" : "Creating"} field with data:');
      print('DEBUG FieldForm: - ID: ${field.id}');
      print('DEBUG FieldForm: - Name: ${field.nameOrNumber}');
      print('DEBUG FieldForm: - Surface Type: ${field.surfaceType}');
      print('DEBUG FieldForm: - Size: ${field.size}');
      print('DEBUG FieldForm: - Location Type: ${field.locationType}');
      print('DEBUG FieldForm: - Notes: ${field.notes}');

      final fieldService = context.read<FieldService>();

      if (_isEditing) {
        print('DEBUG FieldForm: Calling updateField...');
        await fieldService.updateField(field);
        print('DEBUG FieldForm: updateField completed successfully');
      } else {
        print('DEBUG FieldForm: Calling createField...');
        await fieldService.createField(field);
        print('DEBUG FieldForm: createField completed successfully');
      }

      if (mounted) {
        try {
          context.read<CreateTournamentBloc>().add(LoadFieldsForVenue(widget.venueId));
        } catch (e) {
          print('CreateTournamentBloc not available: $e');
        }

        if (!_isEditing) {
          _showAddAnotherFieldDialog();
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Field updated successfully!'),
              backgroundColor: Colors.green,
            ),
          );
          _navigateBack();
        }
      }
    } catch (e) {
      if (mounted) {
        String errorMessage = 'Error saving field: $e';

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _navigateBack() {
    if (Navigator.canPop(context)) {
      Navigator.pop(context, true);
    } else {
      context.go('/create-tournament/venue-field-selection');
    }
  }

  void _showAddAnotherFieldDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Field Created Successfully!'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('${_nameController.text} has been created.'),
              const SizedBox(height: 16),
              const Text('Would you like to add another field to this venue?'),
              const SizedBox(height: 8),
              const Text(
                'This will help you set up all fields quickly.',
                style: TextStyle(fontSize: 12, color: Colors.grey),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _navigateBack();
              },
              child: const Text('Done'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _clearForm();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
              ),
              child: const Text('Add Another'),
            ),
          ],
        );
      },
    );
  }

  void _clearForm() {
    setState(() {
      _nameController.clear();
      _notesController.clear();
      _selectedSurfaceType = null;
      _selectedLocationType = null;
      _selectedSize = null;
      _startTime = null;
      _endTime = null;
      _formKey.currentState?.reset(); // Reset form validation state
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Field created successfully! Add another field below.'),
        backgroundColor: Colors.green,
        duration: Duration(seconds: 2),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_isEditing ? 'Edit Field' : 'Add Field'),
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: () {
              context.read<AuthBloc>().add(AuthSignOutRequested());
            },
            tooltip: 'Sign Out',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    TextFormField(
                      controller: _nameController,
                      decoration: const InputDecoration(
                        labelText: 'Field Name/Number *',
                        hintText: 'e.g., Field 1, Main Field, etc.',
                        border: OutlineInputBorder(),
                      ),
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'Field name/number is required';
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: 16.0),

                    DropdownButtonFormField<String>(
                      value: _selectedSurfaceType,
                      decoration: const InputDecoration(
                        labelText: 'Surface Type',
                        border: OutlineInputBorder(),
                      ),
                      hint: const Text('Select Surface Type'),
                      items: _surfaceTypes.map((type) {
                        return DropdownMenuItem(
                          value: type,
                          child: Text(type),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedSurfaceType = value;
                        });
                      },
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please select a surface type';
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: 16.0),

                    DropdownButtonFormField<String>(
                      value: _selectedLocationType,
                      decoration: const InputDecoration(
                        labelText: 'Location Type',
                        border: OutlineInputBorder(),
                      ),
                      hint: const Text('Select Location Type'),
                      items: _locationTypes.map((type) {
                        return DropdownMenuItem(
                          value: type,
                          child: Text(type),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedLocationType = value;
                        });
                      },
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please select a location type';
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: 16.0),

                    DropdownButtonFormField<String>(
                      value: _selectedSize,
                      decoration: const InputDecoration(
                        labelText: 'Field Size',
                        border: OutlineInputBorder(),
                      ),
                      hint: const Text('Select Field Size'),
                      items: _fieldSizes.map((size) {
                        return DropdownMenuItem(
                          value: size,
                          child: Text(size),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedSize = value;
                        });
                      },
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please select a field size';
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: 16.0),

                    const Text(
                      'Operational Hours (Optional)',
                      style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8.0),

                    Row(
                      children: [
                        Expanded(
                          child: InkWell(
                            onTap: () => _selectTime(context, true),
                            child: InputDecorator(
                              decoration: const InputDecoration(
                                labelText: 'Start Time',
                                border: OutlineInputBorder(),
                                enabledBorder: OutlineInputBorder( // Ensure border is always visible
                                  borderSide: BorderSide(color: Colors.grey),
                                ),
                              ),
                              child: Text(
                                _startTime?.format(context) ?? 'Select time',
                                style: TextStyle(
                                  color: _startTime != null ? Theme.of(context).textTheme.bodyMedium?.color : Colors.grey,
                                ),
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 16.0),
                        Expanded(
                          child: InkWell(
                            onTap: () => _selectTime(context, false),
                            child: InputDecorator(
                              decoration: const InputDecoration(
                                labelText: 'End Time',
                                border: OutlineInputBorder(),
                                enabledBorder: OutlineInputBorder(
                                  borderSide: BorderSide(color: Colors.grey),
                                ),
                              ),
                              child: Text(
                                _endTime?.format(context) ?? 'Select time',
                                style: TextStyle(
                                  color: _endTime != null ? Theme.of(context).textTheme.bodyMedium?.color : Colors.grey,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 16.0),

                    TextFormField(
                      controller: _notesController,
                      decoration: const InputDecoration(
                        labelText: 'Notes (Optional)',
                        hintText: 'Additional information about this field',
                        border: OutlineInputBorder(),
                      ),
                      maxLines: 3,
                    ),

                    const SizedBox(height: 24.0),

                    ElevatedButton(
                      onPressed: _isLoading ? null : _saveField,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16.0),
                      ),
                      child: Text(
                        _isLoading
                            ? 'Saving...'
                            : (_isEditing ? 'Update Field' : 'Create Field'),
                        style: const TextStyle(fontSize: 16),
                      ),
                    ),
                  ],
                ),
              ),
            ),
    );
  }
}