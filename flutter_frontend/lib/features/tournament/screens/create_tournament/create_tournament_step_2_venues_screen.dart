import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:soccer_frontend/data/models/venue.dart';
import 'package:soccer_frontend/features/tournament/blocs/create_tournament/create_tournament_bloc.dart';
import 'package:soccer_frontend/features/tournament/blocs/create_tournament/create_tournament_event.dart';
import 'package:soccer_frontend/features/tournament/blocs/create_tournament/create_tournament_state.dart';

class CreateTournamentStep2VenuesScreen extends StatelessWidget {
  const CreateTournamentStep2VenuesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocListener<CreateTournamentBloc, CreateTournamentState>(
      listener: (context, state) {
        if (state is CreateTournamentStep3InProgress) {
          context.go('/create-tournament/step3');
        }
      },
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Create Tournament - Step 2: Venues'),
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () {
              context.go('/create-tournament/step1');
            },
          ),
        ),
        body: BlocBuilder<CreateTournamentBloc, CreateTournamentState>(
          builder: (context, state) {
            List<Venue> venues = [];
            if (state is CreateTournamentStep2InProgress) {
              venues = state.venues;
            } else if (state is CreateTournamentStep3InProgress) { // For navigating back
              venues = state.venues;
            } else if (state is CreateTournamentStep1InProgress) {
              // If somehow navigated here from step 1 without proper state transition
              // This might happen if BLoC state didn't update fast enough or direct navigation
              // For now, show empty or loading, but ideally state should be Step2InProgress
            } else if (state is CreateTournamentInitial || state is CreateTournamentSaving) {
                 return const Center(child: CircularProgressIndicator());
            }


            return Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  ElevatedButton(
                    onPressed: () async {
                      final result = await context.push<Venue>(
                          '/create-tournament/venue-form'); 
                      if (result != null) {
                        final existingVenue = venues.any((v) => v.id != null && v.id == result.id);
                        if (existingVenue) {
                           context.read<CreateTournamentBloc>().add(VenueUpdated(result));
                        } else {
                           context.read<CreateTournamentBloc>().add(VenueAdded(result.id == null ? result.copyWith(id: UniqueKey().toString()): result ));
                        }
                      }
                    },
                    child: const Text('Add New Venue'),
                  ),
                  const SizedBox(height: 16.0),
                  Text(
                    'Added Venues:',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8.0),
                  Expanded(
                    child: venues.isEmpty
                        ? const Center(child: Text('No venues added yet.'))
                        : ListView.builder(
                            itemCount: venues.length,
                            itemBuilder: (context, index) {
                              final venue = venues[index];
                              return Card(
                                margin:
                                    const EdgeInsets.symmetric(vertical: 4.0),
                                child: ListTile(
                                  title: Text(venue.name),
                                  subtitle: Text(venue.address),
                                  trailing: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      IconButton(
                                        icon: const Icon(Icons.edit),
                                        onPressed: () async {
                                           final result = await context.push<Venue>(
                                            '/create-tournament/venue-form', 
                                            extra: venue
                                          );
                                          if (result != null) {
                                            context.read<CreateTournamentBloc>().add(VenueUpdated(result));
                                          }
                                        },
                                      ),
                                      IconButton(
                                        icon: const Icon(Icons.delete),
                                        onPressed: () {
                                          context.read<CreateTournamentBloc>().add(VenueRemoved(venue));
                                        },
                                      ),
                                    ],
                                  ),
                                ),
                              );
                            },
                          ),
                  ),
                  const SizedBox(height: 16.0),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: <Widget>[
                      TextButton(
                        onPressed: () {
                          context.go('/create-tournament/step1');
                        },
                        child: const Text('Back'),
                      ),
                      ElevatedButton(
                        onPressed: venues.isNotEmpty ? () {
                          context
                              .read<CreateTournamentBloc>()
                              .add(Step2Completed(venues));
                        } : null, 
                        child: const Text('Next'),
                      ),
                    ],
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }
}
