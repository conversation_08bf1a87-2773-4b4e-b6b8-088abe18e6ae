import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:logging/logging.dart';
import 'package:soccer_frontend/data/models/division.dart';
import 'package:soccer_frontend/data/models/tournament.dart';
import 'package:soccer_frontend/features/tournament/blocs/create_tournament/create_tournament_bloc.dart';
import 'package:soccer_frontend/features/tournament/blocs/create_tournament/create_tournament_event.dart';
import 'package:soccer_frontend/features/tournament/blocs/create_tournament/create_tournament_state.dart';

class DivisionMatrixSetupScreen extends StatefulWidget {
  const DivisionMatrixSetupScreen({super.key});

  @override
  State<DivisionMatrixSetupScreen> createState() => _DivisionMatrixSetupScreenState();
}

class _DivisionMatrixSetupScreenState extends State<DivisionMatrixSetupScreen> {
  final Logger _log = Logger('DivisionMatrixSetupScreen');
  List<Division> _configuredDivisions = [];
  Map<String, bool> _enabledMatrixCells = {};
  Map<String, TextEditingController> _divisionNameOverrideControllers = {};
  Map<String, TextEditingController> _divisionNotesControllers = {};
  bool _isInitialized = false;
  bool _isSubmitting = false;

  // Options (could also be passed via BLoC state if dynamic)
  final List<String> _genderOptions = ['Boys', 'Girls', 'Coed'];
  final List<String> _competitiveLevelOptions = ['Premier', 'Select', 'Competitive', 'Recreational', 'Open'];

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted && !_isInitialized) {
        _initializeScreenState();
        if (mounted) {
          setState(() { _isInitialized = true; });
        }
      }
    });
  }

  void _initializeScreenState() {
    final blocState = context.read<CreateTournamentBloc>().state;
    if (blocState is CreateTournamentDivisionMatrixStep) {
      _log.info("Initializing DivisionMatrixSetupScreen from BLoC state.");
      _log.fine("Received selectedAgeGroupFeesByPlayFormat: ${blocState.selectedAgeGroupFeesByPlayFormat}");

      // Pre-populate from existing divisions if user is navigating back or editing
      if (blocState.tournament.divisions != null && blocState.tournament.divisions!.isNotEmpty) {
        _configuredDivisions.addAll(blocState.tournament.divisions!);
        for (var div in _configuredDivisions) {
          final key = "${div.playFormat}_${div.ageGroup}_${div.gender}_${div.competitiveLevel}";
          _enabledMatrixCells[key] = true; // Mark cell as enabled
          _divisionNameOverrideControllers[key] = TextEditingController(text: div.name);
          _divisionNotesControllers[key] = TextEditingController(text: div.notes ?? '');
        }
      }
      _log.info("Initialized _configuredDivisions count: ${_configuredDivisions.length}");
    } else {
      _log.severe("DivisionMatrixSetupScreen initialized with unexpected state: ${blocState.runtimeType}");
    }
  }

  @override
  void dispose() {
    _divisionNameOverrideControllers.forEach((_, controller) => controller.dispose());
    _divisionNotesControllers.forEach((_, controller) => controller.dispose());
    super.dispose();
  }

  void _handleDivisionToggle(bool isEnabled, String playFormat, String ageGroup, String gender, String level, double baseFee) {
    setState(() {
      final matrixKey = "${playFormat}_${ageGroup}_${gender}_${level}";
      _enabledMatrixCells[matrixKey] = isEnabled;

      int existingDivisionIndex = _configuredDivisions.indexWhere((d) =>
          d.playFormat == playFormat &&
          d.ageGroup == ageGroup &&
          d.gender == gender &&
          d.competitiveLevel == level);

      if (isEnabled) {
        if (existingDivisionIndex == -1) { // Add new
          String defaultName = "$ageGroup $gender $level $playFormat";
          final newDivision = Division(
            id: UniqueKey().toString(), // Client-side temporary ID
            name: _divisionNameOverrideControllers[matrixKey]?.text.isNotEmpty == true
                  ? _divisionNameOverrideControllers[matrixKey]!.text
                  : defaultName,
            ageGroup: ageGroup,
            gender: gender,
            competitiveLevel: level,
            playFormat: playFormat,
            registrationFee: baseFee,
            notes: _divisionNotesControllers[matrixKey]?.text,
          );
          _configuredDivisions.add(newDivision);
          // Ensure controllers exist
          if (!_divisionNameOverrideControllers.containsKey(matrixKey)) {
            _divisionNameOverrideControllers[matrixKey] = TextEditingController(text: newDivision.name);
          }
          if (!_divisionNotesControllers.containsKey(matrixKey)) {
             _divisionNotesControllers[matrixKey] = TextEditingController(text: newDivision.notes ?? '');
          }
        } else {
          // Division already exists, just ensure it's enabled (no action needed on _configuredDivisions itself)
        }
      } else { // Remove if unchecked
        if (existingDivisionIndex != -1) {
          _configuredDivisions.removeAt(existingDivisionIndex);
          _divisionNameOverrideControllers[matrixKey]?.dispose();
          _divisionNameOverrideControllers.remove(matrixKey);
          _divisionNotesControllers[matrixKey]?.dispose();
          _divisionNotesControllers.remove(matrixKey);
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    if (!_isInitialized) {
      return Scaffold(
        appBar: AppBar(title: const Text('Division Matrix Setup')),
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    return BlocListener<CreateTournamentBloc, CreateTournamentState>(
      listenWhen: (previous, current) {
        return previous is CreateTournamentDivisionMatrixStep && 
               current is CreateTournamentVenueFieldSelectionStep;
      },
      listener: (context, state) {
        if (state is CreateTournamentVenueFieldSelectionStep) {
          _log.info("DivisionMatrixSetupScreen: Navigating to venue/field selection");
          if (mounted) {
            setState(() { _isSubmitting = false; });
          }
          context.go('/create-tournament/venue-field-selection');
        }
      },
      child: BlocBuilder<CreateTournamentBloc, CreateTournamentState>(
        builder: (context, state) {
          if (state is! CreateTournamentDivisionMatrixStep) {
            return Scaffold(
              appBar: AppBar(title: const Text('Division Matrix Setup')),
              body: const Center(
                child: Text('Invalid state for Division Matrix Setup'),
              ),
            );
          }

          return Scaffold(
            appBar: AppBar(
              title: const Text('Division Matrix Setup'),
              backgroundColor: Theme.of(context).colorScheme.inversePrimary,
            ),
            body: Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Define Divisions',
                          style: Theme.of(context).textTheme.headlineSmall,
                        ),
                        const SizedBox(height: 8.0),
                        const Text(
                          'Select the specific divisions you want to offer for each play format and age group combination.',
                          style: TextStyle(color: Colors.grey),
                        ),
                        const SizedBox(height: 24.0),
                        _buildDivisionMatrix(state),
                      ],
                    ),
                  ),
                ),
                _buildNavigationButtons(),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildDivisionMatrix(CreateTournamentDivisionMatrixStep state) {
    final List<Widget> sections = [];
    
    state.selectedAgeGroupFeesByPlayFormat.forEach((playFormat, ageGroupFees) {
      // Get the base fee (all age groups have the same fee for this format)
      final baseFee = ageGroupFees.values.first;
      
      sections.add(_buildPlayFormatSection(playFormat, ageGroupFees.keys.toList(), baseFee));
      sections.add(const SizedBox(height: 24.0));
    });

    if (sections.isEmpty) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(16.0),
          child: Text(
            'No play formats selected. Please go back to Step 1 and select play formats and age groups.',
            style: TextStyle(color: Colors.grey),
          ),
        ),
      );
    }

    return Column(children: sections);
  }

  Widget _buildPlayFormatSection(String playFormat, List<String> ageGroups, double baseFee) {
    return Card(
      elevation: 2.0,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Define Divisions for $playFormat Format (Base Fee: \$${baseFee.toStringAsFixed(2)})',
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16.0),
            ...ageGroups.map((ageGroup) => _buildAgeGroupSection(playFormat, ageGroup, baseFee)),
          ],
        ),
      ),
    );
  }

  Widget _buildAgeGroupSection(String playFormat, String ageGroup, double baseFee) {
    return ExpansionTile(
      title: Text('$ageGroup Age Group'),
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Column(
            children: _genderOptions.map((gender) => 
              _buildGenderSection(playFormat, ageGroup, gender, baseFee)
            ).toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildGenderSection(String playFormat, String ageGroup, String gender, double baseFee) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8.0),
          child: Text(
            '$gender Divisions:',
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
        ),
        Wrap(
          spacing: 8.0,
          runSpacing: 4.0,
          children: _competitiveLevelOptions.map((level) {
            final matrixKey = "${playFormat}_${ageGroup}_${gender}_${level}";
            final isEnabled = _enabledMatrixCells[matrixKey] ?? false;
            
            return SizedBox(
              width: 200,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Expanded(
                    child: CheckboxListTile(
                      title: Text(level, style: const TextStyle(fontSize: 14)),
                      value: isEnabled,
                      onChanged: (bool? value) {
                        _handleDivisionToggle(value ?? false, playFormat, ageGroup, gender, level, baseFee);
                      },
                      controlAffinity: ListTileControlAffinity.leading,
                      dense: true,
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                  if (isEnabled)
                    IconButton(
                      icon: const Icon(Icons.edit_note, size: 20),
                      onPressed: () => _showEditDivisionDetailsDialog(context, playFormat, ageGroup, gender, level, baseFee),
                      tooltip: 'Edit Name/Notes',
                    ),
                ],
              ),
            );
          }).toList(),
        ),
        const SizedBox(height: 8.0),
      ],
    );
  }

  Widget _buildNavigationButtons() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(top: BorderSide(color: Colors.grey.shade300)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          OutlinedButton(
            onPressed: _isSubmitting ? null : () {
              context.go('/create-tournament/step1');
            },
            child: const Text('Back'),
          ),
          ElevatedButton(
            onPressed: _configuredDivisions.isNotEmpty && !_isSubmitting ? _submitDivisions : null,
            child: _isSubmitting
                ? const SizedBox(width: 20, height: 20, child: CircularProgressIndicator(strokeWidth: 2.0, color: Colors.white))
                : const Text('Next: Venue & Field Selection'),
          ),
        ],
      ),
    );
  }

  void _submitDivisions() {
    if (_configuredDivisions.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please select at least one division to continue.')),
      );
      return;
    }

    setState(() { _isSubmitting = true; });

    final blocState = context.read<CreateTournamentBloc>().state;
    if (blocState is CreateTournamentDivisionMatrixStep) {
      final updatedTournament = blocState.tournament.copyWith(divisions: _configuredDivisions);
      context.read<CreateTournamentBloc>().add(DivisionsSetupCompleted(updatedTournament));
    }
  }

  void _showEditDivisionDetailsDialog(BuildContext parentContext, String playFormat, String ageGroup, String gender, String level, double baseFee) {
    final matrixKey = "${playFormat}_${ageGroup}_${gender}_${level}";

    // Ensure controllers exist, prefill with current name/notes from _configuredDivisions or defaults
    int divIndex = _configuredDivisions.indexWhere((d) => d.playFormat == playFormat && d.ageGroup == ageGroup && d.gender == gender && d.competitiveLevel == level);
    Division? currentDivision = divIndex != -1 ? _configuredDivisions[divIndex] : null;

    final nameController = _divisionNameOverrideControllers.putIfAbsent(matrixKey, () => TextEditingController(text: currentDivision?.name ?? "$ageGroup $gender $level $playFormat"));
    final notesController = _divisionNotesControllers.putIfAbsent(matrixKey, () => TextEditingController(text: currentDivision?.notes ?? ''));

    // If controllers existed, ensure their text reflects the current state if it was from _configuredDivisions
    if (currentDivision != null) {
      if (nameController.text != currentDivision.name) nameController.text = currentDivision.name;
      if (notesController.text != (currentDivision.notes ?? '')) notesController.text = currentDivision.notes ?? '';
    }

    showDialog(
      context: parentContext,
      builder: (dialogContext) {
        return AlertDialog(
          title: Text('Edit Division Details: $ageGroup $gender $level $playFormat'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextFormField(
                  controller: nameController,
                  decoration: const InputDecoration(labelText: 'Custom Division Name (Optional)'),
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: notesController,
                  decoration: const InputDecoration(labelText: 'Notes (Optional)', border: OutlineInputBorder()),
                  maxLines: 3,
                ),
              ],
            ),
          ),
          actions: [
            TextButton(onPressed: () => Navigator.of(dialogContext).pop(), child: const Text('Cancel')),
            ElevatedButton(
              onPressed: () {
                setState(() {
                  int existingDivisionIndex = _configuredDivisions.indexWhere((d) =>
                      d.playFormat == playFormat &&
                      d.ageGroup == ageGroup &&
                      d.gender == gender &&
                      d.competitiveLevel == level);

                  if (existingDivisionIndex != -1) {
                    _configuredDivisions[existingDivisionIndex] = _configuredDivisions[existingDivisionIndex].copyWith(
                      name: nameController.text.trim().isNotEmpty ? nameController.text.trim() : "$ageGroup $gender $level $playFormat",
                      notes: notesController.text.trim().isNotEmpty ? notesController.text.trim() : null,
                    );
                  }
                });
                Navigator.of(dialogContext).pop();
              },
              child: const Text('Save Details'),
            ),
          ],
        );
      },
    );
  }
}