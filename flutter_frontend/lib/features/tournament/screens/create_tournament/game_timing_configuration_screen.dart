import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:logging/logging.dart';
import 'package:soccer_frontend/data/models/game_timing_config.dart';
import 'package:soccer_frontend/features/tournament/blocs/create_tournament/create_tournament_bloc.dart';
import 'package:soccer_frontend/features/tournament/blocs/create_tournament/create_tournament_event.dart';
import 'package:soccer_frontend/features/tournament/blocs/create_tournament/create_tournament_state.dart';

class GameTimingConfigurationScreen extends StatefulWidget {
  const GameTimingConfigurationScreen({super.key});

  @override
  State<GameTimingConfigurationScreen> createState() => _GameTimingConfigurationScreenState();
}

class _GameTimingConfigurationScreenState extends State<GameTimingConfigurationScreen> {
  final Logger _log = Logger('GameTimingConfigurationScreen');
  final Map<String, GameTimingConfig> _gameTimings = {};
  final Set<String> _selectedAgeGroups = {};
  final Set<String> _selectedFieldSizes = {};
  bool _isSubmitting = false;

  @override
  void initState() {
    super.initState();
    _parseAgeGroupsAndFieldSizes();
  }

  void _parseAgeGroupsAndFieldSizes() {
    final state = context.read<CreateTournamentBloc>().state;

    // Use derivedAgeGroups and derivedFieldSizes from the BLoC state
    if (state is CreateTournamentGameTimingConfigStep) {
      // Use the derived age groups from the previous step (from divisions)
      _selectedAgeGroups.addAll(state.derivedAgeGroups);

      // Use the derived field sizes from the previous step (from selected fields)
      _selectedFieldSizes.addAll(state.derivedFieldSizes);

      // Initialize default configurations
      _initializeDefaultConfigurations();
    }
  }

  void _initializeDefaultConfigurations() {
    for (final ageGroup in _selectedAgeGroups) {
      for (final fieldSize in _selectedFieldSizes) {
        final config = GameTimingConfig.getConfigurationFor(ageGroup, fieldSize) ??
            GameTimingConfig(
              ageGroup: ageGroup,
              fieldSize: fieldSize,
              gameDurationMinutes: 60,
              halftimeMinutes: 10,
              bufferTimeMinutes: 10,
              notes: 'Generic default - please review and customize',
            );
        _gameTimings['${ageGroup}_$fieldSize'] = config;
      }
    }
  }

  void _updateTimingConfig(String key, GameTimingConfig config) {
    setState(() {
      _gameTimings[key] = config;
    });
  }

  void _submitTimings() {
    if (_isSubmitting) {
      _log.warning("GameTimingScreen: _submitTimings called but already submitting. Ignoring.");
      return;
    }

    setState(() {
      _isSubmitting = true;
    });

    _log.info("GameTimingScreen: 'Next/Continue' button PRESSED. Dispatching GameTimingsSubmitted with ${_gameTimings.length} configurations.");
    context.read<CreateTournamentBloc>().add(GameTimingsSubmitted(_gameTimings));

    // Note: We don't reset _isSubmitting here because navigation should occur
    // If navigation fails, the screen would need to handle that case
  }

  @override
  void dispose() {
    _log.info("GameTimingConfigurationScreen DISPOSED");
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<CreateTournamentBloc, CreateTournamentState>(
      listenWhen: (previous, current) {
        // Only navigate when transitioning *from this step* to the *specific next step*.
        return previous is CreateTournamentGameTimingConfigStep &&
               current is CreateTournamentAdditionalInfoStep;
      },
      listener: (context, state) {
        if (state is CreateTournamentAdditionalInfoStep) {
          _log.info('DEBUG: Navigating to additional info from GameTimingConfigurationScreen BlocListener');
          context.go('/create-tournament/additional-info');
        }
      },
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Game Timing Configurations'),
        ),
        body: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Expanded(
                child: _gameTimings.isEmpty ? _buildEmptyState() : ListView(
                  children: _gameTimings.entries.map((entry) {
                    return _TimingConfigCard(
                      config: entry.value,
                      onChanged: (newConfig) => _updateTimingConfig(entry.key, newConfig),
                    );
                  }).toList(),
                ),
              ),
              const SizedBox(height: 16),
              _buildNavigationButtons(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    String title = 'No Game Timing Configurations Available';
    String message = 'Unable to create timing configurations.';
    String actionMessage = '';

    if (_selectedAgeGroups.isEmpty && _selectedFieldSizes.isEmpty) {
      title = 'No Age Groups or Field Sizes Available';
      message = 'No divisions or field selections were found for this tournament.';
      actionMessage = 'Please go back to define divisions (Step 1) and select venues/fields (Step 2).';
    } else if (_selectedAgeGroups.isEmpty) {
      title = 'No Age Groups Defined';
      message = 'No divisions with age groups were found for this tournament.';
      actionMessage = 'Please go back to Step 1 and define divisions with age groups.';
    } else if (_selectedFieldSizes.isEmpty) {
      title = 'No Compatible Field Sizes Selected';
      message = 'No fields were selected that match the play formats of your divisions.';
      actionMessage = 'Please go back to Step 2 (Venue/Field Selection) and select fields that match your division play formats.';
    }

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.schedule,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              title,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              message,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            if (actionMessage.isNotEmpty) ...[
              const SizedBox(height: 16),
              Text(
                actionMessage,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.orange[700],
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildNavigationButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        OutlinedButton(
          onPressed: _isSubmitting ? null : () {
            context.go('/create-tournament/venue-field-selection');
          },
          child: const Text('Back'),
        ),
        ElevatedButton(
          onPressed: _gameTimings.isNotEmpty && !_isSubmitting ? _submitTimings : null,
          child: _isSubmitting
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2, color: Colors.white),
                )
              : const Text('Continue'),
        ),
      ],
    );
  }
}

class _TimingConfigCard extends StatefulWidget {
  final GameTimingConfig config;
  final Function(GameTimingConfig) onChanged;

  const _TimingConfigCard({
    required this.config,
    required this.onChanged,
  });

  @override
  State<_TimingConfigCard> createState() => _TimingConfigCardState();
}

class _TimingConfigCardState extends State<_TimingConfigCard> {
  late GameTimingConfig _currentConfig;

  @override
  void initState() {
    super.initState();
    _currentConfig = widget.config;
  }

  void _updateGameDuration(int minutes) {
    setState(() {
      _currentConfig = _currentConfig.copyWith(gameDurationMinutes: minutes);
    });
    widget.onChanged(_currentConfig);
  }

  void _updateHalftime(int minutes) {
    setState(() {
      _currentConfig = _currentConfig.copyWith(halftimeMinutes: minutes);
    });
    widget.onChanged(_currentConfig);
  }

  void _updateBufferTime(int minutes) {
    setState(() {
      _currentConfig = _currentConfig.copyWith(bufferTimeMinutes: minutes);
    });
    widget.onChanged(_currentConfig);
  }

  void _updateNotes(String notes) {
    setState(() {
      _currentConfig = _currentConfig.copyWith(notes: notes);
    });
    widget.onChanged(_currentConfig);
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '${_currentConfig.ageGroup} - ${_currentConfig.fieldSize}',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Text(
              _currentConfig.notes,
              style: Theme.of(context).textTheme.bodySmall,
            ),
            const SizedBox(height: 16),
            _NumberInputField(
              label: 'Game Duration (minutes)',
              value: _currentConfig.gameDurationMinutes,
              onChanged: _updateGameDuration,
            ),
            _NumberInputField(
              label: 'Halftime Duration (minutes)',
              value: _currentConfig.halftimeMinutes,
              onChanged: _updateHalftime,
            ),
            _NumberInputField(
              label: 'Buffer Time (minutes)',
              value: _currentConfig.bufferTimeMinutes,
              onChanged: _updateBufferTime,
            ),
            const SizedBox(height: 8),
            TextField(
              decoration: const InputDecoration(
                labelText: 'Notes',
                border: OutlineInputBorder(),
              ),
              onChanged: _updateNotes,
              controller: TextEditingController(text: _currentConfig.notes),
            ),
            const SizedBox(height: 8),
            Text(
              'Total Time Slot: ${_currentConfig.totalTimeSlotMinutes} minutes',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
          ],
        ),
      ),
    );
  }
}

class _NumberInputField extends StatelessWidget {
  final String label;
  final int value;
  final Function(int) onChanged;

  const _NumberInputField({
    required this.label,
    required this.value,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        children: [
          Expanded(
            child: Text(label),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: TextFormField(
              initialValue: value.toString(),
              keyboardType: TextInputType.number,
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
                contentPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 12),
              ),
              onChanged: (value) {
                final parsed = int.tryParse(value);
                if (parsed != null) {
                  onChanged(parsed);
                }
              },
            ),
          ),
        ],
      ),
    );
  }
}
