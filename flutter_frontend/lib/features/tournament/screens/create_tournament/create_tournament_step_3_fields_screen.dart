import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:soccer_frontend/data/models/field.dart';
import 'package:soccer_frontend/features/tournament/blocs/create_tournament/create_tournament_bloc.dart';
import 'package:soccer_frontend/features/tournament/blocs/create_tournament/create_tournament_event.dart';
import 'package:soccer_frontend/features/tournament/blocs/create_tournament/create_tournament_state.dart';

class CreateTournamentStep3FieldsScreen extends StatelessWidget {
  const CreateTournamentStep3FieldsScreen({super.key});

  Future<void> _showAddFieldDialog(
      BuildContext context, String venueId, String venueName) async {
    final formKey = GlobalKey<FormState>();
    String fieldNameOrNumber = '';
    String? surfaceType; // Changed from fieldTypeName
    String? size;
    String? notes;
    String? locationType;
    String status = 'Open';

    // Predefined options for dropdowns
    final List<String> fieldTypeOptions = [
      'Natural Grass',
      'Artificial Turf',
      'Wood (Hardwood)',
      'Vinyl/Plastic',
      'Acrylic/Rubber',
      'Polyurethane (PU)',
      'Beach Soccer Fields (Sand)',
      'Dirt/Gravel Fields',
      'Other'
    ];

    final List<String> fieldSizeOptions = [
      '4v4 (Typically U6 - U8 players)',
      '7v7 (Typically U9 - U10 players)',
      '9v9 (Typically U11 - U12 players)',
      '11v11 (Typically U13 and older, including adults)',
      'Custom'
    ];

    TimeOfDay? fieldStartTime;
    TimeOfDay? fieldEndTime;

    Future<void> _selectStartTime(BuildContext context) async {
      final TimeOfDay? picked = await showTimePicker(
        context: context,
        initialTime: TimeOfDay.now(),
      );
      if (picked != null) {
        fieldStartTime = picked;
      }
    }

    Future<void> _selectEndTime(BuildContext context) async {
      final TimeOfDay? picked = await showTimePicker(
        context: context,
        initialTime: TimeOfDay.now(),
      );
      if (picked != null) {
        fieldEndTime = picked;
      }
    }

    DateTime? _timeOfDayToDateTime(TimeOfDay? timeOfDay) {
      if (timeOfDay == null) return null;

      final now = DateTime.now();
      return DateTime(
        now.year,
        now.month,
        now.day,
        timeOfDay.hour,
        timeOfDay.minute,
      );
    }

    // Store the CreateTournamentBloc reference before creating a new context
    final createTournamentBloc = context.read<CreateTournamentBloc>();

    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        return StatefulBuilder(
          builder: (builderContext, setState) {
            return AlertDialog(
              title: Text('Add Field to $venueName'),
              content: SingleChildScrollView(
                child: Form(
                  key: formKey,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      // Basic Field Information
                      TextFormField(
                        decoration:
                            const InputDecoration(labelText: 'Field Name/Number*'),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter field name or number';
                          }
                          return null;
                        },
                        onSaved: (value) => fieldNameOrNumber = value!,
                      ),
                      const SizedBox(height: 16.0),

                      // Field Type and Size
                      Row(
                        children: [
                          Expanded(
                            child: DropdownButtonFormField<String>(
                              decoration: const InputDecoration(
                                labelText: 'Field Type',
                              ),
                              hint: const Text('Select Surface Type'), // Changed label
                              value: surfaceType, // Changed variable
                              items: fieldTypeOptions.map((String type) {
                                return DropdownMenuItem<String>(
                                  value: type,
                                  child: Text(type),
                                );
                              }).toList(),
                              onChanged: (value) {
                                setState(() {
                                  surfaceType = value; // Changed variable
                                });
                              },
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'Please select a surface type'; // Changed message
                                }
                                return null;
                              },
                              onSaved: (value) => surfaceType = value, // Changed variable
                            ),
                          ),
                          const SizedBox(width: 16.0),
                          Expanded(
                            child: DropdownButtonFormField<String>(
                              decoration: const InputDecoration(
                                labelText: 'Field Size', // This remains 'Field Size'
                              ),
                              hint: const Text('Select Field Size'), // This remains 'Select Field Size'
                              value: size,
                              items: fieldSizeOptions.map((String sizeOption) {
                                return DropdownMenuItem<String>(
                                  value: sizeOption,
                                  child: Text(sizeOption),
                                );
                              }).toList(),
                              onChanged: (value) {
                                setState(() {
                                  size = value;
                                });
                              },
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'Please select a field size';
                                }
                                return null;
                              },
                              onSaved: (value) => size = value,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16.0),

                      // Field Status and Location Type
                      Row(
                        children: [
                          Expanded(
                            child: DropdownButtonFormField<String>(
                              decoration: const InputDecoration(
                                labelText: 'Status',
                              ),
                              value: status,
                              items: const [
                                DropdownMenuItem(value: 'Open', child: Text('Open')),
                                DropdownMenuItem(value: 'Closed', child: Text('Closed')),
                                DropdownMenuItem(value: 'Maintenance', child: Text('Maintenance')),
                              ],
                              onChanged: (value) {
                                if (value != null) {
                                  setState(() {
                                    status = value;
                                  });
                                }
                              },
                            ),
                          ),
                          const SizedBox(width: 16.0),
                          Expanded(
                            child: DropdownButtonFormField<String>(
                              decoration: const InputDecoration(
                                labelText: 'Location Type',
                              ),
                              value: locationType,
                              items: const [
                                DropdownMenuItem(value: null, child: Text('Select')),
                                DropdownMenuItem(value: 'Indoor', child: Text('Indoor')),
                                DropdownMenuItem(value: 'Outdoor', child: Text('Outdoor')),
                              ],
                              onChanged: (value) {
                                setState(() {
                                  locationType = value;
                                });
                              },
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16.0),

                      // Field Operational Hours
                      const Text(
                        'Field Operational Hours (Overrides Venue)',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8.0),
                      Row(
                        children: [
                          Expanded(
                            child: InkWell(
                              onTap: () async {
                                await _selectStartTime(context);
                                setState(() {});
                              },
                              child: InputDecorator(
                                decoration: const InputDecoration(
                                  labelText: 'Start Time',
                                  border: OutlineInputBorder(),
                                ),
                                child: Text(
                                  fieldStartTime != null
                                      ? fieldStartTime!.format(context)
                                      : 'Select Time',
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(width: 16.0),
                          Expanded(
                            child: InkWell(
                              onTap: () async {
                                await _selectEndTime(context);
                                setState(() {});
                              },
                              child: InputDecorator(
                                decoration: const InputDecoration(
                                  labelText: 'End Time',
                                  border: OutlineInputBorder(),
                                ),
                                child: Text(
                                  fieldEndTime != null
                                      ? fieldEndTime!.format(context)
                                      : 'Select Time',
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16.0),

                      // Notes
                      TextFormField(
                        decoration: const InputDecoration(
                          labelText: 'Notes',
                          hintText: 'Any additional information about the field',
                        ),
                        maxLines: 3,
                        onSaved: (value) => notes = value,
                      ),
                    ],
                  ),
                ),
              ),
              actions: <Widget>[
                TextButton(
                  child: const Text('Cancel'),
                  onPressed: () {
                    Navigator.of(dialogContext).pop();
                  },
                ),
                TextButton(
                  child: const Text('Add Field'),
                  onPressed: () {
                    if (formKey.currentState!.validate()) {
                      formKey.currentState!.save();
                      final newField = Field(
                        id: UniqueKey().toString(),
                        venueId: venueId,
                        nameOrNumber: fieldNameOrNumber,
                        surfaceType: surfaceType, // Changed from fieldTypeName
                        size: size,
                        status: status,
                        fieldStartTime: _timeOfDayToDateTime(fieldStartTime),
                        fieldEndTime: _timeOfDayToDateTime(fieldEndTime),
                        notes: notes,
                        locationType: locationType,
                      );
                      createTournamentBloc.add(FieldAddedToVenue(newField, venueId));
                      Navigator.of(dialogContext).pop();
                    }
                  },
                ),
              ],
            );
          }
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<CreateTournamentBloc, CreateTournamentState>(
      listener: (context, state) {
        if (state is CreateTournamentSaveSuccess) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Tournament saved successfully!')),
          );
          context.go('/');
        } else if (state is CreateTournamentSaveFailure) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Failed to save tournament: ${state.error}')),
          );
        }
      },
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Create Tournament - Step 3: Fields'),
           leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () {
              context.go('/create-tournament/step2');
            },
          ),
        ),
        body: BlocBuilder<CreateTournamentBloc, CreateTournamentState>(
          builder: (context, state) {
            if (state is! CreateTournamentStep3InProgress) {
               if (state is CreateTournamentSaving) {
                 return const Center(child: CircularProgressIndicator(semanticsLabel: "Saving tournament...",));
               }
               // Handle other states or show loading if not Step3InProgress
               return const Center(child: CircularProgressIndicator());
            }

            final venues = state.venues;
            final fieldsByVenueId = state.fieldsByVenueId;

            if (venues.isEmpty) {
              return Center(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Text('No venues available. Please go back and add venues first.', textAlign: TextAlign.center),
                      const SizedBox(height: 20),
                      ElevatedButton(onPressed: () => context.go('/create-tournament/step2'), child: const Text('Go to Add Venues'))
                    ],
                  ),
                )
              );
            }

            return Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  Expanded(
                    child: ListView.builder(
                      itemCount: venues.length,
                      itemBuilder: (context, index) {
                        final venue = venues[index];
                        // Ensure venue.id is not null, as it's used as a key.
                        // If venue.id can be null (e.g., before saving to DB), use a fallback or handle error.
                        final venueIdForMap = venue.id ?? venue.name; // Fallback to name if ID is temp/null
                        final venueFields = fieldsByVenueId[venueIdForMap] ?? [];

                        return Card(
                          margin: const EdgeInsets.symmetric(vertical: 8.0),
                          child: Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  venue.name,
                                  style: Theme.of(context).textTheme.titleLarge,
                                ),
                                const SizedBox(height: 8.0),
                                if (venueFields.isEmpty)
                                  const Padding(
                                    padding: EdgeInsets.symmetric(vertical: 8.0),
                                    child: Text('No fields added for this venue yet.'),
                                  )
                                else
                                  ListView.builder(
                                    shrinkWrap: true,
                                    physics: const NeverScrollableScrollPhysics(),
                                    itemCount: venueFields.length,
                                    itemBuilder: (context, fieldIndex) {
                                      final field = venueFields[fieldIndex];
                                      return ListTile(
                                        title: Text(field.nameOrNumber),
                                        subtitle: Text(
                                            'Type: ${field.surfaceType ?? 'N/A'}, Size: ${field.size ?? 'N/A'}, ${field.locationType ?? 'Outdoor'}'), // Changed field.fieldTypeName to field.surfaceType
                                        trailing: Row(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            // IconButton( // Schedule button might be premature here
                                            //   icon: const Icon(Icons.schedule),
                                            //   tooltip: 'View Schedule',
                                            //   onPressed: () {
                                            //     final tournamentId = (context.read<CreateTournamentBloc>().state as CreateTournamentStep3InProgress).tournament.id;
                                            //     if (tournamentId == null || field.id == null) {
                                            //       ScaffoldMessenger.of(context).showSnackBar(
                                            //         const SnackBar(content: Text('Error: Tournament or Field ID not available.')),
                                            //       );
                                            //       return;
                                            //     }
                                            //     context.go('/tournaments/$tournamentId/fields/${field.id}/schedule');
                                            //   },
                                            // ),
                                            IconButton(
                                              icon: const Icon(Icons.delete, color: Colors.redAccent),
                                              tooltip: 'Delete Field',
                                              onPressed: (){
                                                context.read<CreateTournamentBloc>().add(FieldRemovedFromVenue(field, venueIdForMap));
                                              },
                                            ),
                                          ],
                                        ),
                                      );
                                    },
                                  ),
                                const SizedBox(height: 8.0),
                                Align(
                                  alignment: Alignment.centerRight,
                                  child: TextButton(
                                    onPressed: () {
                                      _showAddFieldDialog(
                                          context, venueIdForMap, venue.name);
                                    },
                                    child: const Text('Add Field'),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                  const SizedBox(height: 16.0),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: <Widget>[
                      TextButton(
                        onPressed: () {
                          context.go('/create-tournament/step2');
                        },
                        child: const Text('Back'),
                      ),
                      ElevatedButton(
                        onPressed: state is CreateTournamentSaving ? null : () {
                          context
                              .read<CreateTournamentBloc>()
                              .add(SaveTournamentRequested());
                        },
                        child: state is CreateTournamentSaving
                               ? const SizedBox(height: 20, width: 20, child: CircularProgressIndicator(strokeWidth: 2.0, color: Colors.white))
                               : const Text('Save Tournament'),
                      ),
                    ],
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }
}
