// soccer_frontend/features/tournament/presentation/screens/venue_form_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:soccer_frontend/data/models/venue.dart';
import 'package:soccer_frontend/features/auth/blocs/auth_bloc.dart';
import 'package:soccer_frontend/services/venue_service.dart';
import 'package:soccer_frontend/features/tournament/blocs/create_tournament/create_tournament_bloc.dart';
import 'package:soccer_frontend/features/tournament/blocs/create_tournament/create_tournament_event.dart';
import 'package:intl/intl.dart'; // For TimeOfDay.format context

class VenueFormScreen extends StatefulWidget {
  final Venue? venue;

  const VenueFormScreen({super.key, this.venue});

  @override
  State<VenueFormScreen> createState() => _VenueFormScreenState();
}

class _VenueFormScreenState extends State<VenueFormScreen> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _nameController;
  late TextEditingController _addressController;
  late TextEditingController _cityController;
  late TextEditingController _zipCodeController;
  late TextEditingController _contactNameController;
  late TextEditingController _contactEmailController;
  late TextEditingController _contactPhoneController;

  String? _selectedState;
  TimeOfDay? _operationalStartTime;
  TimeOfDay? _operationalEndTime;
  bool _isLoading = false;

  // US States list
  static const List<String> _usStates = [
    'Alabama', 'Alaska', 'Arizona', 'Arkansas', 'California', 'Colorado',
    'Connecticut', 'Delaware', 'Florida', 'Georgia', 'Hawaii', 'Idaho',
    'Illinois', 'Indiana', 'Iowa', 'Kansas', 'Kentucky', 'Louisiana',
    'Maine', 'Maryland', 'Massachusetts', 'Michigan', 'Minnesota',
    'Mississippi', 'Missouri', 'Montana', 'Nebraska', 'Nevada',
    'New Hampshire', 'New Jersey', 'New Mexico', 'New York',
    'North Carolina', 'North Dakota', 'Ohio', 'Oklahoma', 'Oregon',
    'Pennsylvania', 'Rhode Island', 'South Carolina', 'South Dakota',
    'Tennessee', 'Texas', 'Utah', 'Vermont', 'Virginia', 'Washington',
    'West Virginia', 'Wisconsin', 'Wyoming'
  ];

  bool get _isEditing => widget.venue != null;

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: widget.venue?.name);
    _addressController = TextEditingController(text: widget.venue?.address);
    _cityController = TextEditingController(text: widget.venue?.city);

    final venueState = widget.venue?.state;
    if (venueState != null && _usStates.contains(venueState)) {
      _selectedState = venueState;
    } else {
      _selectedState = null;
      if (venueState != null) {
        print('DEBUG: Venue state "$venueState" not found in US states list');
      }
    }

    _zipCodeController = TextEditingController(text: widget.venue?.zipCode);
    _contactNameController =
        TextEditingController(text: widget.venue?.contactName);
    _contactEmailController =
        TextEditingController(text: widget.venue?.contactEmail);
    _contactPhoneController =
        TextEditingController(text: widget.venue?.contactPhone);

    if (widget.venue?.operationalStartTime != null) {
      _operationalStartTime = TimeOfDay(
        hour: widget.venue!.operationalStartTime!.hour,
        minute: widget.venue!.operationalStartTime!.minute,
      );
    }
    if (widget.venue?.operationalEndTime != null) {
      _operationalEndTime = TimeOfDay(
        hour: widget.venue!.operationalEndTime!.hour,
        minute: widget.venue!.operationalEndTime!.minute,
      );
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _addressController.dispose();
    _cityController.dispose();
    _zipCodeController.dispose();
    _contactNameController.dispose();
    _contactEmailController.dispose();
    _contactPhoneController.dispose();
    super.dispose();
  }

  Future<void> _selectStartTime() async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: _operationalStartTime ?? TimeOfDay.now(),
    );
    if (picked != null) {
      setState(() {
        _operationalStartTime = picked;
      });
    }
  }

  Future<void> _selectEndTime() async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: _operationalEndTime ?? TimeOfDay.now(),
    );
    if (picked != null) {
      setState(() {
        _operationalEndTime = picked;
      });
    }
  }

  DateTime? _timeOfDayToDateTime(TimeOfDay? timeOfDay) {
    if (timeOfDay == null) return null;
    final now = DateTime.now();
    return DateTime(
      now.year,
      now.month,
      now.day,
      timeOfDay.hour,
      timeOfDay.minute,
    );
  }

  Future<void> _saveVenue() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoading = true;
      });

      try {
        final venueData = Venue(
          id: widget.venue?.id,
          name: _nameController.text.trim(),
          address: _addressController.text.trim(),
          city: _cityController.text.trim().isNotEmpty ? _cityController.text.trim() : null,
          state: _selectedState,
          zipCode: _zipCodeController.text.trim().isNotEmpty ? _zipCodeController.text.trim() : null,
          contactName: _contactNameController.text.trim().isNotEmpty
              ? _contactNameController.text.trim()
              : null,
          contactEmail: _contactEmailController.text.trim().isNotEmpty
              ? _contactEmailController.text.trim()
              : null,
          contactPhone: _contactPhoneController.text.trim().isNotEmpty
              ? _contactPhoneController.text.trim()
              : null,
          operationalStartTime: _timeOfDayToDateTime(_operationalStartTime),
          operationalEndTime: _timeOfDayToDateTime(_operationalEndTime),
        );

        final venueService = context.read<VenueService>();
        if (_isEditing) {
          await venueService.updateVenue(widget.venue!.id!, venueData.toJson());
        } else {
          await venueService.createVenue(venueData.toJson());
        }

        if (mounted) {
          try {
            // This ensures the parent list of venues (in VenueFieldSelectionScreen) is updated
            context.read<CreateTournamentBloc>().add(LoadAvailableVenues());
          } catch (e) {
            print('CreateTournamentBloc not available: $e'); // Log if bloc is not in widget tree
          }

          if (!_isEditing) {
            _showAddFieldsDialog(venueData);
          } else {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Venue updated successfully!'),
                backgroundColor: Colors.green,
              ),
            );
            _navigateBack();
          }
        }
      } catch (e) {
        if (mounted) {
          String errorMessage = 'Error saving venue: $e';
          if (e.toString().contains('duplicate key value violates unique constraint "venues_name_key"')) {
            errorMessage = 'A venue with this name already exists. Please choose a different name.';
          } else if (e.toString().contains('duplicate key')) {
            errorMessage = 'This venue information already exists. Please check your input.';
          }
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(errorMessage),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 5),
            ),
          );
        }
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }

  void _navigateBack() {
    if (Navigator.canPop(context)) {
      Navigator.pop(context, true);
    } else {
      // Fallback if current screen is direct route or root
      context.go('/create-tournament/venue-field-selection');
    }
  }

  void _showAddFieldsDialog(Venue venue) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Venue Created Successfully!'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('${venue.name} has been created.'),
              const SizedBox(height: 16),
              const Text('Would you like to add fields to this venue now?'),
              const SizedBox(height: 8),
              const Text(
                'This will help streamline your tournament setup.',
                style: TextStyle(fontSize: 12, color: Colors.grey),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _navigateBack();
              },
              child: const Text('Later'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                context.go('/create-tournament/venues/${venue.id}/fields/add');
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
              ),
              child: const Text('Add Fields'),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_isEditing ? 'Edit Venue' : 'Add New Venue'),
        actions: [
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: () {
              context.read<AuthBloc>().add(AuthSignOutRequested());
            },
            tooltip: 'Sign Out',
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: <Widget>[
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(labelText: 'Venue Name*'),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter the venue name';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16.0),

              TextFormField(
                controller: _addressController,
                decoration: const InputDecoration(labelText: 'Address*'),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter the address';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16.0),

              Row(
                children: [
                  Expanded(
                    flex: 4,
                    child: TextFormField(
                      controller: _cityController,
                      decoration: const InputDecoration(labelText: 'City'),
                    ),
                  ),
                  const SizedBox(width: 8.0),
                  Expanded(
                    flex: 3,
                    child: DropdownButtonFormField<String>(
                      value: _selectedState,
                      decoration: const InputDecoration(labelText: 'State'),
                      isExpanded: true,
                      hint: const Text('Select State'),
                      items: _usStates.map((String state) {
                        return DropdownMenuItem<String>(
                          value: state,
                          child: Text(
                            state,
                            overflow: TextOverflow.ellipsis,
                          ),
                        );
                      }).toList(),
                      onChanged: (String? newValue) {
                        setState(() {
                          _selectedState = newValue;
                        });
                      },
                      validator: (value) {
                        return null;
                      },
                    ),
                  ),
                  const SizedBox(width: 8.0),
                  Expanded(
                    flex: 2,
                    child: TextFormField(
                      controller: _zipCodeController,
                      decoration: const InputDecoration(labelText: 'Zip Code'),
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value != null && value.isNotEmpty && (int.tryParse(value) == null || value.length != 5)) {
                          return '5-digit zip';
                        }
                        return null;
                      },
                      autovalidateMode: AutovalidateMode.onUserInteraction,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24.0),

              const Text(
                'Venue Contact Information',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16.0),
              TextFormField(
                controller: _contactNameController,
                decoration: const InputDecoration(labelText: 'Venue Contact Name'),
              ),
              const SizedBox(height: 16.0),
              TextFormField(
                controller: _contactEmailController,
                decoration: const InputDecoration(labelText: 'Venue Contact Email'),
                keyboardType: TextInputType.emailAddress,
                validator: (value) {
                  if (value != null && value.isNotEmpty && !value.contains('@')) {
                    return 'Please enter a valid email address';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16.0),
              TextFormField(
                controller: _contactPhoneController,
                decoration: const InputDecoration(labelText: 'Venue Contact Phone'),
                keyboardType: TextInputType.phone,
                validator: (value) {
                  if (value != null && value.isNotEmpty && !RegExp(r'^\d{10}$').hasMatch(value.replaceAll(RegExp(r'[^\d]'), ''))) {
                    return 'Enter 10-digit phone number';
                  }
                  return null;
                },
                autovalidateMode: AutovalidateMode.onUserInteraction,
              ),
              const SizedBox(height: 24.0),

              const Text(
                'Operational Hours',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16.0),
              Row(
                children: [
                  Expanded(
                    child: InkWell(
                      onTap: _selectStartTime,
                      child: InputDecorator(
                        decoration: const InputDecoration(
                          labelText: 'Opening Time',
                          border: OutlineInputBorder(),
                          enabledBorder: OutlineInputBorder( // Ensure border is always visible
                            borderSide: BorderSide(color: Colors.grey),
                          ),
                        ),
                        child: Text(
                          _operationalStartTime != null
                              ? _operationalStartTime!.format(context)
                              : 'Select Time',
                          style: TextStyle(
                            color: _operationalStartTime != null ? Theme.of(context).textTheme.bodyMedium?.color : Colors.grey,
                          ),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16.0),
                  Expanded(
                    child: InkWell(
                      onTap: _selectEndTime,
                      child: InputDecorator(
                        decoration: const InputDecoration(
                          labelText: 'Closing Time',
                          border: OutlineInputBorder(),
                          enabledBorder: OutlineInputBorder(
                            borderSide: BorderSide(color: Colors.grey),
                          ),
                        ),
                        child: Text(
                          _operationalEndTime != null
                              ? _operationalEndTime!.format(context)
                              : 'Select Time',
                          style: TextStyle(
                            color: _operationalEndTime != null ? Theme.of(context).textTheme.bodyMedium?.color : Colors.grey,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 32.0),

              ElevatedButton(
                onPressed: _isLoading ? null : _saveVenue,
                child: _isLoading
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : Text(_isEditing ? 'Update Venue' : 'Save Venue'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}