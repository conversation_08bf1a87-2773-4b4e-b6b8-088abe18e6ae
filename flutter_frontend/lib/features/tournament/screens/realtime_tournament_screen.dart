import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class RealtimeTournamentScreen extends StatelessWidget {
  final String tournamentId;

  const RealtimeTournamentScreen({super.key, required this.tournamentId});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Tournament Details'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text(
              'Realtime Tournament Screen',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'Tournament ID: $tournamentId',
              style: const TextStyle(
                fontSize: 18,
              ),
            ),
            const SizedBox(height: 32),
            ElevatedButton(
              onPressed: () => context.go('/tournaments'),
              child: const Text('Back to Tournaments'),
            ),
          ],
        ),
      ),
    );
  }
}
