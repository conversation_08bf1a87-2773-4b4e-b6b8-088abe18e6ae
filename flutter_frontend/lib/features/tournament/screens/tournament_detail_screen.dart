import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:soccer_frontend/features/tournament/blocs/tournament_bloc.dart';
import 'package:soccer_frontend/features/tournament/blocs/tournament_event.dart';
import 'package:soccer_frontend/features/tournament/blocs/tournament_state.dart';
import 'package:soccer_frontend/data/models/tournament.dart';
import 'package:intl/intl.dart';

class TournamentDetailScreen extends StatefulWidget {
  final String tournamentId;

  const TournamentDetailScreen({super.key, required this.tournamentId});

  static String routeName(String tournamentId) => '/tournament/$tournamentId';

  @override
  State<TournamentDetailScreen> createState() => _TournamentDetailScreenState();
}

class _TournamentDetailScreenState extends State<TournamentDetailScreen> {
  @override
  void initState() {
    super.initState();
    context
        .read<TournamentBloc>()
        .add(TournamentLoadByIdRequested(id: widget.tournamentId));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Tournament Details'),
      ),
      body: BlocBuilder<TournamentBloc, TournamentState>(
        builder: (context, state) {
          if (state is TournamentLoading) {
            return const Center(child: CircularProgressIndicator());
          } else if (state is TournamentDetailLoaded) {
            final tournament = state.tournament;
            return _buildTournamentDetails(context, tournament);
          } else if (state is TournamentError) {
            return Center(
              child: Text('Error loading tournament details: ${state.message}'),
            );
          }
          return const Center(child: Text('Loading details...'));
        },
      ),
    );
  }

  Widget _buildTournamentDetails(BuildContext context, Tournament tournament) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: ListView(
        children: <Widget>[
          _buildDetailItem(context, 'Name', tournament.name),
          _buildDetailItem(context, 'Sport', tournament.sportType),
          _buildDetailItem(context, 'Status', tournament.status),
          _buildDetailItem(context, 'Start Date', DateFormat.yMMMd().format(tournament.startDate.toLocal())),
          _buildDetailItem(context, 'End Date', DateFormat.yMMMd().format(tournament.endDate.toLocal())),
          // Add more details as needed from the Tournament model
          // e.g., location, rules_url, etc. if they were populated.
          // For now, these are the core fields from the model.
          const SizedBox(height: 20),
          _buildManagementActions(context, tournament),
        ],
      ),
    );
  }

  Widget _buildDetailItem(BuildContext context, String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('$label: ', style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold)),
          Expanded(child: Text(value, style: Theme.of(context).textTheme.titleMedium)),
        ],
      ),
    );
  }

  Widget _buildManagementActions(BuildContext context, Tournament tournament) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Tournament Management',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildStatusCard(context, tournament),
            const SizedBox(height: 16),
            _buildActionButtons(context, tournament),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusCard(BuildContext context, Tournament tournament) {
    return Container(
      padding: const EdgeInsets.all(12.0),
      decoration: BoxDecoration(
        color: _getStatusColor(tournament.status).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: _getStatusColor(tournament.status).withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(
            _getStatusIcon(tournament.status),
            color: _getStatusColor(tournament.status),
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Status',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
                Text(
                  _getStatusDisplayName(tournament.status),
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: _getStatusColor(tournament.status),
                  ),
                ),
              ],
            ),
          ),
          if (_canChangeStatus(tournament.status))
            ElevatedButton(
              onPressed: () => _showStatusChangeDialog(context, tournament),
              child: const Text('Change Status'),
            ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context, Tournament tournament) {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      mainAxisSpacing: 8,
      crossAxisSpacing: 8,
      childAspectRatio: 3,
      children: [
        _buildActionButton(
          context,
          'Edit Tournament',
          Icons.edit,
          () => _showComingSoonSnackBar(context, 'Edit tournament'),
        ),
        _buildActionButton(
          context,
          'Manage Venues',
          Icons.location_city,
          () => _showComingSoonSnackBar(context, 'Venue management'),
        ),
        _buildActionButton(
          context,
          'View Registrations',
          Icons.list,
          () => _showComingSoonSnackBar(context, 'Team registrations'),
        ),
        _buildActionButton(
          context,
          'Schedule Matches',
          Icons.schedule,
          () => _showComingSoonSnackBar(context, 'Tournament scheduling'),
        ),
        if (tournament.status == 'planning')
          _buildActionButton(
            context,
            'Delete Tournament',
            Icons.delete,
            () => _showDeleteDialog(context, tournament),
            color: Colors.red,
          ),
        _buildActionButton(
          context,
          'Export Data',
          Icons.download,
          () => _showComingSoonSnackBar(context, 'Export feature'),
        ),
      ],
    );
  }

  Widget _buildActionButton(
    BuildContext context,
    String label,
    IconData icon,
    VoidCallback onPressed, {
    Color? color,
  }) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, size: 16),
      label: Text(
        label,
        style: const TextStyle(fontSize: 12),
        overflow: TextOverflow.ellipsis,
      ),
      style: ElevatedButton.styleFrom(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        backgroundColor: color,
      ),
    );
  }

  // Helper methods
  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'planning':
        return Colors.grey;
      case 'registration_open':
        return Colors.blue;
      case 'registration_closed':
        return Colors.orange;
      case 'scheduled':
        return Colors.purple;
      case 'active':
        return Colors.green;
      case 'completed':
        return Colors.teal;
      case 'cancelled':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  IconData _getStatusIcon(String status) {
    switch (status.toLowerCase()) {
      case 'planning':
        return Icons.edit;
      case 'registration_open':
        return Icons.app_registration;
      case 'registration_closed':
        return Icons.lock;
      case 'scheduled':
        return Icons.schedule;
      case 'active':
        return Icons.play_circle;
      case 'completed':
        return Icons.check_circle;
      case 'cancelled':
        return Icons.cancel;
      default:
        return Icons.event;
    }
  }

  String _getStatusDisplayName(String status) {
    switch (status.toLowerCase()) {
      case 'planning':
        return 'Planning';
      case 'registration_open':
        return 'Registration Open';
      case 'registration_closed':
        return 'Registration Closed';
      case 'scheduled':
        return 'Scheduled';
      case 'active':
        return 'Active';
      case 'completed':
        return 'Completed';
      case 'cancelled':
        return 'Cancelled';
      default:
        return status.toUpperCase();
    }
  }

  bool _canChangeStatus(String status) {
    return status.toLowerCase() != 'completed' && status.toLowerCase() != 'cancelled';
  }

  void _showComingSoonSnackBar(BuildContext context, String feature) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('$feature feature coming soon')),
    );
  }

  void _showStatusChangeDialog(BuildContext context, Tournament tournament) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Change Tournament Status'),
        content: Text('Change status for "${tournament.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _showComingSoonSnackBar(context, 'Status change');
            },
            child: const Text('Change'),
          ),
        ],
      ),
    );
  }

  void _showDeleteDialog(BuildContext context, Tournament tournament) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Tournament'),
        content: Text('Are you sure you want to delete "${tournament.name}"? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _showComingSoonSnackBar(context, 'Delete tournament');
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}
