import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class BracketsScreen extends StatelessWidget {
  const BracketsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Brackets'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text(
              'Brackets Screen',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 32),
            ElevatedButton(
              onPressed: () => context.go('/tournaments'),
              child: const Text('Back to Tournaments'),
            ),
          ],
        ),
      ),
    );
  }
}
