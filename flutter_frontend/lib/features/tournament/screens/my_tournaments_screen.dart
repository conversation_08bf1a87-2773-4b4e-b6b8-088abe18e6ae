import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:soccer_frontend/features/tournament/blocs/tournament_bloc.dart';
import 'package:soccer_frontend/features/tournament/blocs/tournament_event.dart';
import 'package:soccer_frontend/features/tournament/blocs/tournament_state.dart';
import 'package:go_router/go_router.dart'; // If navigation from list items is needed

class MyTournamentsScreen extends StatefulWidget {
  const MyTournamentsScreen({super.key});

  static const String routeName = '/my-tournaments'; // For GoRouter

  @override
  State<MyTournamentsScreen> createState() => _MyTournamentsScreenState();
}

class _MyTournamentsScreenState extends State<MyTournamentsScreen> {
  @override
  void initState() {
    super.initState();
    // Dispatch event to load tournaments when the screen is initialized
    // Assuming TournamentBloc is provided higher up in the widget tree (e.g., in main.dart or a ShellRoute)
    // If not, this context.read will fail.
    // For now, we assume TournamentBloc is already provided.
    // If TournamentBloc is not globally provided, it needs to be provided specifically for this route.
    // Let's assume it's provided via main.dart's MultiBlocProvider.
    context.read<TournamentBloc>().add(LoadMyTournaments());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('My Tournaments'),
      ),
      body: BlocBuilder<TournamentBloc, TournamentState>(
        builder: (context, state) {
          if (state is TournamentLoading) {
            return const Center(child: CircularProgressIndicator());
          } else if (state is MyTournamentsLoaded) {
            if (state.tournaments.isEmpty) {
              return const Center(
                child: Text('You have not created any tournaments yet.'),
              );
            }
            return ListView.builder(
              itemCount: state.tournaments.length,
              itemBuilder: (context, index) {
                final tournament = state.tournaments[index];
                return Card(
                  margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: ListTile(
                    title: Text(tournament.name, style: const TextStyle(fontWeight: FontWeight.bold)),
                    subtitle: Text(
                        '${tournament.sportType} | ${tournament.startDate.toLocal().toString().split(' ')[0]} - ${tournament.endDate.toLocal().toString().split(' ')[0]}'),
                    trailing: Text(tournament.status, style: TextStyle(color: _getStatusColor(tournament.status))),
                    onTap: () {
                      if (tournament.id != null) {
                        context.goNamed('tournamentDetail', pathParameters: {'tournamentId': tournament.id!});
                      } else {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(content: Text('Tournament ID is missing, cannot navigate.')),
                        );
                      }
                    },
                  ),
                );
              },
            );
          } else if (state is TournamentError) {
            return Center(
              child: Text('Error loading tournaments: ${state.message}'),
            );
          }
          // Initial state or other unhandled states
          return const Center(child: Text('Loading tournaments...'));
        },
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'planning':
        return Colors.blue;
      case 'registration_open':
        return Colors.green;
      case 'live':
        return Colors.red;
      case 'completed':
        return Colors.grey;
      default:
        return Colors.black;
    }
  }
}
