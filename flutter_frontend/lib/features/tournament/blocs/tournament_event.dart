// lib/src/blocs/tournament/tournament_event.dart
// part of 'tournament_bloc.dart'; // Removed part-of directive
import 'package:equatable/equatable.dart'; // Added import

abstract class TournamentEvent extends Equatable {
  const TournamentEvent();

  @override
  List<Object?> get props => [];
}

class TournamentLoadAllRequested extends TournamentEvent {}

class TournamentLoadByIdRequested extends TournamentEvent {
  final String id;

  const TournamentLoadByIdRequested({required this.id});

  @override
  List<Object> get props => [id];
}

class LoadMyTournaments extends TournamentEvent {} // Added event

class TournamentCreateRequested extends TournamentEvent {
  final Map<String, dynamic> data;

  const TournamentCreateRequested({required this.data});

  @override
  List<Object> get props => [data];
}

class TournamentUpdateRequested extends TournamentEvent {
  final String id;
  final Map<String, dynamic> data;

  const TournamentUpdateRequested({
    required this.id,
    required this.data,
  });

  @override
  List<Object> get props => [id, data];
}

class TournamentDeleteRequested extends TournamentEvent {
  final String id;

  const TournamentDeleteRequested({required this.id});

  @override
  List<Object> get props => [id];
}
