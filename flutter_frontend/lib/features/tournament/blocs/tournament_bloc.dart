// lib/features/tournament/blocs/tournament_bloc.dart
import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:logging/logging.dart';

// import '../../../core/services/supabase_service.dart'; // Will use TournamentApiService
import 'package:soccer_frontend/features/tournament/services/tournament_api_service.dart'; // Added
import '../../../core/utils/app_exception.dart';
import 'tournament_event.dart'; // Added import
import 'tournament_state.dart'; // Added import

class TournamentBloc extends Bloc<TournamentEvent, TournamentState> {
  // final SupabaseService _supabaseService; // Changed to TournamentApiService
  final TournamentApiService _tournamentApiService;
  final Logger _log = Logger('TournamentBloc');

  TournamentBloc({required TournamentApiService tournamentApiService}) // Changed parameter
      : _tournamentApiService = tournamentApiService, // Changed assignment
        super(TournamentInitial()) {
    on<TournamentLoadAllRequested>(_onTournamentLoadAllRequested);
    on<TournamentLoadByIdRequested>(_onTournamentLoadByIdRequested);
    on<TournamentCreateRequested>(_onTournamentCreateRequested);
    on<TournamentUpdateRequested>(_onTournamentUpdateRequested);
    on<TournamentDeleteRequested>(_onTournamentDeleteRequested);
    on<LoadMyTournaments>(_onLoadMyTournaments); // Added handler
  }

  Future<void> _onLoadMyTournaments( // Added method
    LoadMyTournaments event,
    Emitter<TournamentState> emit,
  ) async {
    _log.info('Loading my tournaments');
    emit(TournamentLoading());
    try {
      final tournaments = await _tournamentApiService.getMyTournaments();
      _log.info('Loaded ${tournaments.length} of my tournaments');
      emit(MyTournamentsLoaded(tournaments: tournaments));
    } catch (e, s) {
      _log.severe('Error loading my tournaments: $e', e, s);
      emit(TournamentError(
        message: e is AppException ? e.message : 'Failed to load my tournaments',
        error: e,
      ));
    }
  }

  Future<void> _onTournamentLoadAllRequested(
    TournamentLoadAllRequested event,
    Emitter<TournamentState> emit,
  ) async {
    _log.info('Loading all tournaments');
    emit(TournamentLoading());

    try {
      // final tournaments = await _supabaseService.getTournaments(); // Changed to use _tournamentApiService
      final tournaments = await _tournamentApiService.getAllTournaments();
      _log.info('Loaded ${tournaments.length} tournaments');
      emit(TournamentLoaded(tournaments: tournaments)); // Changed to TournamentLoaded from TournamentListLoaded
    } catch (e, s) {
      _log.severe('Error loading all tournaments: $e', e, s); // Clarified log message
      emit(TournamentError(
        message: e is AppException ? e.message : 'Failed to load tournaments',
        error: e,
      ));
    }
  }

  Future<void> _onTournamentLoadByIdRequested(
    TournamentLoadByIdRequested event,
    Emitter<TournamentState> emit,
  ) async {
    _log.info('Loading tournament with ID: ${event.id}');
    emit(TournamentLoading());

    try {
      // final tournament = await _supabaseService.getTournamentById(event.id); // Changed to use _tournamentApiService
      final tournament = await _tournamentApiService.getTournamentById(event.id);
      _log.info('Loaded tournament: ${tournament.name}'); // Assuming Tournament model has name
      emit(TournamentDetailLoaded(tournament: tournament)); // Changed to TournamentDetailLoaded
    } catch (e, s) {
      _log.severe('Error loading tournament by ID: $e', e, s); // Clarified log message
      emit(TournamentError(
        message: e is AppException ? e.message : 'Failed to load tournament',
        error: e,
      ));
    }
  }

  Future<void> _onTournamentCreateRequested(
    TournamentCreateRequested event,
    Emitter<TournamentState> emit,
  ) async {
    _log.info('Creating tournament: ${event.data['name']}');
    emit(TournamentLoading());

    try {
      // final tournament = await _supabaseService.client // Changed to use _tournamentApiService
      //     .from('tournaments')
      //     .insert(event.data)
      //     .select()
      //     .single();
      final tournament = await _tournamentApiService.createTournament(event.data);

      _log.info('Tournament created with ID: ${tournament.id}'); // Assuming Tournament model has id
      emit(TournamentDetailLoaded(tournament: tournament)); // Re-emit as DetailLoaded or a specific Created state
    } catch (e, s) {
      _log.severe('Error creating tournament: $e', e, s);
      emit(TournamentError(
        message: e is AppException ? e.message : 'Failed to create tournament',
        error: e,
      ));
    }
  }

  Future<void> _onTournamentUpdateRequested(
    TournamentUpdateRequested event,
    Emitter<TournamentState> emit,
  ) async {
    _log.info('Updating tournament with ID: ${event.id}');
    emit(TournamentLoading());

    try {
      // final tournament = await _supabaseService.client // Changed to use _tournamentApiService
      //     .from('tournaments')
      //     .update(event.data)
      //     .eq('id', event.id)
      //     .select()
      //     .single();
      final tournament = await _tournamentApiService.updateTournament(event.id, event.data);

      _log.info('Tournament updated: ${tournament.name}'); // Assuming Tournament model has name
      emit(TournamentDetailLoaded(tournament: tournament)); // Re-emit as DetailLoaded or a specific Updated state
    } catch (e, s) {
      _log.severe('Error updating tournament: $e', e, s);
      emit(TournamentError(
        message: e is AppException ? e.message : 'Failed to update tournament',
        error: e,
      ));
    }
  }

  Future<void> _onTournamentDeleteRequested(
    TournamentDeleteRequested event,
    Emitter<TournamentState> emit,
  ) async {
    _log.info('Deleting tournament with ID: ${event.id}');
    emit(TournamentLoading());

    try {
      // await _supabaseService.client // Changed to use _tournamentApiService
      //     .from('tournaments')
      //     .delete()
      //     .eq('id', event.id);
      await _tournamentApiService.deleteTournament(event.id);

      _log.info('Tournament deleted');
      emit(TournamentDeleted(id: event.id)); // Consider a more generic success or navigation state
    } catch (e, s) {
      _log.severe('Error deleting tournament: $e', e, s);
      emit(TournamentError(
        message: e is AppException ? e.message : 'Failed to delete tournament',
        error: e,
      ));
    }
  }
}
