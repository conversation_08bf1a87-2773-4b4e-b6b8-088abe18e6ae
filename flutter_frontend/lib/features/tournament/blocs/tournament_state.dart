// lib/src/blocs/tournament/tournament_state.dart
// part of 'tournament_bloc.dart'; // Removed part-of directive
import 'package:equatable/equatable.dart'; // Added import
import 'package:soccer_frontend/data/models/tournament.dart'; // Added import

abstract class TournamentState extends Equatable {
  const TournamentState();

  @override
  List<Object?> get props => [];
}

class TournamentInitial extends TournamentState {}

class TournamentLoading extends TournamentState {}

class TournamentLoaded extends TournamentState { // This state is for all tournaments (e.g. public listing)
  final List<Tournament> tournaments;

  const TournamentLoaded({required this.tournaments});

  @override
  List<Object?> get props => [tournaments];
}

class MyTournamentsLoaded extends TournamentState { // New state for director's specific tournaments
  final List<Tournament> tournaments;

  const MyTournamentsLoaded({required this.tournaments});

  @override
  List<Object?> get props => [tournaments];
}

class TournamentDetailLoaded extends TournamentState {
  final Tournament tournament;

  const TournamentDetailLoaded({required this.tournament});

  @override
  List<Object?> get props => [tournament];
}

class TournamentError extends TournamentState {
  final String message;
  final dynamic error; // Added error field

  const TournamentError({required this.message, this.error}); // Added error to constructor

  @override
  List<Object?> get props => [message, error]; // Added error to props
}

class TournamentUpdated extends TournamentState {
  final Map<String, dynamic> tournament;

  const TournamentUpdated({required this.tournament});

  @override
  List<Object> get props => [tournament];
}

class TournamentDeleted extends TournamentState {
  final String id;

  const TournamentDeleted({required this.id});

  @override
  List<Object> get props => [id];
}

// Removed duplicate TournamentError class. The one above this section is kept.
// class TournamentError extends TournamentState {
//   final String message;
//   final dynamic error;

//   const TournamentError({
//     required this.message,
//     this.error,
//   });

//   @override
//   List<Object?> get props => [message, error];
// }
