// soccer_frontend/features/tournament/blocs/create_tournament/create_tournament_state.dart
import 'package:equatable/equatable.dart';
import 'package:soccer_frontend/data/models/tournament.dart';
import 'package:soccer_frontend/data/models/venue.dart';
import 'package:soccer_frontend/data/models/field.dart';
import 'package:soccer_frontend/data/models/club.dart';
import 'package:soccer_frontend/data/models/field_type.dart';
import 'package:soccer_frontend/data/models/game_timing_config.dart'; // << IMPORT THIS
import 'package:soccer_frontend/data/models/division.dart'; // << IMPORT FOR DIVISION MATRIX
import 'package:flutter/foundation.dart'; // For listEquals, mapEquals

abstract class CreateTournamentState extends Equatable {
  const CreateTournamentState();
  @override
  List<Object?> get props => [];
}

class CreateTournamentInitial extends CreateTournamentState {}

class CreateTournamentStep1InProgress extends CreateTournamentState {
  final Tournament tournament;
  final Map<String, String> validationErrors;
  final bool isAffiliated;
  final bool affiliationStatusLoading;
  final String? selectedCreationOption;
  final List<Club>? affiliatedClubs;
  final bool affiliatedClubsLoading;
  final String? selectedAffiliatedClubId;

  const CreateTournamentStep1InProgress({
    required this.tournament,
    this.validationErrors = const {},
    this.isAffiliated = false,
    this.affiliationStatusLoading = false,
    this.selectedCreationOption,
    this.affiliatedClubs,
    this.affiliatedClubsLoading = false,
    this.selectedAffiliatedClubId,
  });

  CreateTournamentStep1InProgress copyWith({
    Tournament? tournament,
    Map<String, String>? validationErrors,
    bool? isAffiliated,
    bool? affiliationStatusLoading,
    String? selectedCreationOption,
    List<Club>? affiliatedClubs,
    bool? affiliatedClubsLoading,
    String? selectedAffiliatedClubId,
  }) {
    return CreateTournamentStep1InProgress(
      tournament: tournament ?? this.tournament,
      validationErrors: validationErrors ?? this.validationErrors,
      isAffiliated: isAffiliated ?? this.isAffiliated,
      affiliationStatusLoading: affiliationStatusLoading ?? this.affiliationStatusLoading,
      selectedCreationOption: selectedCreationOption ?? this.selectedCreationOption,
      affiliatedClubs: affiliatedClubs ?? this.affiliatedClubs,
      affiliatedClubsLoading: affiliatedClubsLoading ?? this.affiliatedClubsLoading,
      selectedAffiliatedClubId: selectedAffiliatedClubId ?? this.selectedAffiliatedClubId,
    );
  }

  @override
  List<Object?> get props => [
    tournament,
    validationErrors,
    isAffiliated,
    affiliationStatusLoading,
    selectedCreationOption,
    affiliatedClubs,
    affiliatedClubsLoading,
    selectedAffiliatedClubId
  ];
}

class CreateTournamentDivisionMatrixStep extends CreateTournamentState {
  final Tournament tournament; // From Step 1
  final Map<String, Map<String, double>> selectedAgeGroupFeesByPlayFormat; // New structure
  final List<Division> existingDivisions;
  final String? selectedAffiliatedClubId; // For context and back navigation

  const CreateTournamentDivisionMatrixStep({
    required this.tournament,
    required this.selectedAgeGroupFeesByPlayFormat,
    this.existingDivisions = const [],
    this.selectedAffiliatedClubId,
  });

  CreateTournamentDivisionMatrixStep copyWith({
    Tournament? tournament,
    Map<String, Map<String, double>>? selectedAgeGroupFeesByPlayFormat,
    List<Division>? existingDivisions,
    String? selectedAffiliatedClubId,
  }) {
    return CreateTournamentDivisionMatrixStep(
      tournament: tournament ?? this.tournament,
      selectedAgeGroupFeesByPlayFormat: selectedAgeGroupFeesByPlayFormat ?? this.selectedAgeGroupFeesByPlayFormat,
      existingDivisions: existingDivisions ?? this.existingDivisions,
      selectedAffiliatedClubId: selectedAffiliatedClubId ?? this.selectedAffiliatedClubId,
    );
  }

  @override
  List<Object?> get props => [
    tournament,
    selectedAgeGroupFeesByPlayFormat,
    existingDivisions,
    selectedAffiliatedClubId,
  ];
}

// NOTE: This state might be deprecated by the new flow that uses CreateTournamentVenueFieldSelectionStep directly after Step1.
// Review if any part of your application still explicitly relies on CreateTournamentStep2InProgress for core wizard flow.
class CreateTournamentStep2InProgress extends CreateTournamentState {
  final Tournament tournament;
  final List<Venue> venues; // This should be `availableVenues` for clarity in new flow
  final Map<String, String> validationErrors;
  final String? selectedAffiliatedClubId;
  final Set<String> selectedVenueIdsFromSelection; // These are specific to an old selection method
  final Set<String> selectedFieldIdsFromSelection; // These are specific to an old selection method

  const CreateTournamentStep2InProgress({
    required this.tournament,
    this.venues = const [],
    this.validationErrors = const {},
    this.selectedAffiliatedClubId,
    this.selectedVenueIdsFromSelection = const {},
    this.selectedFieldIdsFromSelection = const {},
  });

  CreateTournamentStep2InProgress copyWith({
    Tournament? tournament,
    List<Venue>? venues,
    Map<String, String>? validationErrors,
    String? selectedAffiliatedClubId,
    Set<String>? selectedVenueIdsFromSelection,
    Set<String>? selectedFieldIdsFromSelection,
  }) {
    return CreateTournamentStep2InProgress(
      tournament: tournament ?? this.tournament,
      venues: venues ?? this.venues,
      validationErrors: validationErrors ?? this.validationErrors,
      selectedAffiliatedClubId: selectedAffiliatedClubId ?? this.selectedAffiliatedClubId,
      selectedVenueIdsFromSelection: selectedVenueIdsFromSelection ?? this.selectedVenueIdsFromSelection,
      selectedFieldIdsFromSelection: selectedFieldIdsFromSelection ?? this.selectedFieldIdsFromSelection,
    );
  }

  @override
  List<Object?> get props => [
    tournament,
    venues,
    validationErrors,
    selectedAffiliatedClubId,
    selectedVenueIdsFromSelection,
    selectedFieldIdsFromSelection
  ];
}

// NOTE: This state might be deprecated by the new flow that uses CreateTournamentReviewStep for saving.
// Review if any part of your application still explicitly relies on CreateTournamentStep3InProgress for core wizard flow.
class CreateTournamentStep3InProgress extends CreateTournamentState {
  final Tournament tournament;
  final List<Venue> venues; // Should be `selectedVenues` from previous step
  final Map<String, List<Field>> fieldsByVenueId; // Should be `selectedFieldsByVenueId` from previous step
  final Map<String, String> validationErrors;
  final String? finalSelectedAffiliatedClubId;
  final Set<String> finalSelectedVenueIds;
  final Set<String> finalSelectedFieldIds;

  const CreateTournamentStep3InProgress({
    required this.tournament,
    required this.venues,
    this.fieldsByVenueId = const {},
    this.validationErrors = const {},
    this.finalSelectedAffiliatedClubId,
    this.finalSelectedVenueIds = const {},
    this.finalSelectedFieldIds = const {},
  });

  CreateTournamentStep3InProgress copyWith({
    Tournament? tournament,
    List<Venue>? venues,
    Map<String, List<Field>>? fieldsByVenueId,
    Map<String, String>? validationErrors,
    String? finalSelectedAffiliatedClubId,
    Set<String>? finalSelectedVenueIds,
    Set<String>? finalSelectedFieldIds,
  }) {
    return CreateTournamentStep3InProgress(
      tournament: tournament ?? this.tournament,
      venues: venues ?? this.venues,
      fieldsByVenueId: fieldsByVenueId ?? this.fieldsByVenueId,
      validationErrors: validationErrors ?? this.validationErrors,
      finalSelectedAffiliatedClubId: finalSelectedAffiliatedClubId ?? this.finalSelectedAffiliatedClubId,
      finalSelectedVenueIds: finalSelectedVenueIds ?? this.finalSelectedVenueIds,
      finalSelectedFieldIds: finalSelectedFieldIds ?? this.finalSelectedFieldIds,
    );
  }

  @override
  List<Object?> get props => [
    tournament,
    venues,
    fieldsByVenueId,
    validationErrors,
    finalSelectedAffiliatedClubId,
    finalSelectedVenueIds,
    finalSelectedFieldIds
  ];
}

class CreateTournamentSaving extends CreateTournamentState {}

class CreateTournamentSaveSuccess extends CreateTournamentState {}

class CreateTournamentSaveFailure extends CreateTournamentState {
  final String error;

  const CreateTournamentSaveFailure(this.error);

  @override
  List<Object> get props => [error];
}

class CreateTournamentVenueFieldSelectionStep extends CreateTournamentState {
  final Tournament tournament;
  final String? selectedAffiliatedClubId;
  final List<Venue>? availableVenues;
  final bool venuesLoading;
  final Set<String> selectedVenueIds;
  final Map<String, List<Field>> availableFieldsByVenueId;
  final Map<String, bool> fieldsLoadingByVenueId;
  final Set<String> selectedFieldIds;
  final Map<String, String> validationErrors; // Keep validation errors for this step
  final List<FieldType>? allFieldTypes;

  const CreateTournamentVenueFieldSelectionStep({
    required this.tournament,
    this.selectedAffiliatedClubId,
    this.availableVenues,
    this.venuesLoading = false,
    this.selectedVenueIds = const {},
    this.availableFieldsByVenueId = const {},
    this.fieldsLoadingByVenueId = const {},
    this.selectedFieldIds = const {},
    this.validationErrors = const {},
    this.allFieldTypes,
  });

  CreateTournamentVenueFieldSelectionStep copyWith({
    Tournament? tournament,
    String? selectedAffiliatedClubId,
    List<Venue>? availableVenues,
    bool? venuesLoading,
    Set<String>? selectedVenueIds,
    Map<String, List<Field>>? availableFieldsByVenueId,
    Map<String, bool>? fieldsLoadingByVenueId,
    Set<String>? selectedFieldIds,
    Map<String, String>? validationErrors,
    List<FieldType>? allFieldTypes,
  }) {
    return CreateTournamentVenueFieldSelectionStep(
      tournament: tournament ?? this.tournament,
      selectedAffiliatedClubId: selectedAffiliatedClubId ?? this.selectedAffiliatedClubId,
      availableVenues: availableVenues ?? this.availableVenues,
      venuesLoading: venuesLoading ?? this.venuesLoading,
      selectedVenueIds: selectedVenueIds ?? this.selectedVenueIds,
      availableFieldsByVenueId: availableFieldsByVenueId ?? this.availableFieldsByVenueId,
      fieldsLoadingByVenueId: fieldsLoadingByVenueId ?? this.fieldsLoadingByVenueId,
      selectedFieldIds: selectedFieldIds ?? this.selectedFieldIds,
      validationErrors: validationErrors ?? this.validationErrors,
      allFieldTypes: allFieldTypes ?? this.allFieldTypes,
    );
  }

  @override
  List<Object?> get props => [
    tournament,
    selectedAffiliatedClubId,
    availableVenues,
    venuesLoading,
    selectedVenueIds,
    availableFieldsByVenueId,
    fieldsLoadingByVenueId,
    selectedFieldIds,
    validationErrors,
    allFieldTypes,
  ];
}

class CreateTournamentGameTimingConfigStep extends CreateTournamentState {
  final Tournament tournament;
  final String? selectedAffiliatedClubId;
  final List<Venue>? availableVenues; // Propagating data for 'Back' navigation
  final bool venuesLoading; // Propagating data for 'Back' navigation
  final Set<String> selectedVenueIds;
  final Map<String, List<Field>> availableFieldsByVenueId; // Propagating data for 'Back' navigation
  final Map<String, bool> fieldsLoadingByVenueId; // Propagating data for 'Back' navigation
  final Set<String> selectedFieldIds;
  final List<FieldType>? allFieldTypes; // Propagating data for 'Back' navigation

  // These two are crucial for GameTimingConfigurationScreen to initialize
  final Set<String> derivedAgeGroups; // Renamed for clarity on where it comes from
  final Set<String> derivedFieldSizes; // Renamed for clarity on where it comes from

  const CreateTournamentGameTimingConfigStep({
    required this.tournament,
    this.selectedAffiliatedClubId,
    this.availableVenues,
    this.venuesLoading = false,
    required this.selectedVenueIds, // Mandatory as this is the result of previous step
    this.availableFieldsByVenueId = const {},
    this.fieldsLoadingByVenueId = const {},
    required this.selectedFieldIds, // Mandatory as this is the result of previous step
    this.allFieldTypes,
    required this.derivedAgeGroups, // This should be calculated and passed by the BLoC
    required this.derivedFieldSizes, // This should be calculated and passed by the BLoC
  });

  CreateTournamentGameTimingConfigStep copyWith({
    Tournament? tournament,
    String? selectedAffiliatedClubId,
    List<Venue>? availableVenues,
    bool? venuesLoading,
    Set<String>? selectedVenueIds,
    Map<String, List<Field>>? availableFieldsByVenueId,
    Map<String, bool>? fieldsLoadingByVenueId,
    Set<String>? selectedFieldIds,
    List<FieldType>? allFieldTypes,
    Set<String>? derivedAgeGroups,
    Set<String>? derivedFieldSizes,
  }) {
    return CreateTournamentGameTimingConfigStep(
      tournament: tournament ?? this.tournament,
      selectedAffiliatedClubId: selectedAffiliatedClubId ?? this.selectedAffiliatedClubId,
      availableVenues: availableVenues ?? this.availableVenues,
      venuesLoading: venuesLoading ?? this.venuesLoading,
      selectedVenueIds: selectedVenueIds ?? this.selectedVenueIds,
      availableFieldsByVenueId: availableFieldsByVenueId ?? this.availableFieldsByVenueId,
      fieldsLoadingByVenueId: fieldsLoadingByVenueId ?? this.fieldsLoadingByVenueId,
      selectedFieldIds: selectedFieldIds ?? this.selectedFieldIds,
      allFieldTypes: allFieldTypes ?? this.allFieldTypes,
      derivedAgeGroups: derivedAgeGroups ?? this.derivedAgeGroups,
      derivedFieldSizes: derivedFieldSizes ?? this.derivedFieldSizes,
    );
  }

  @override
  List<Object?> get props => [
    tournament,
    selectedAffiliatedClubId,
    availableVenues,
    venuesLoading,
    selectedVenueIds,
    availableFieldsByVenueId,
    fieldsLoadingByVenueId,
    selectedFieldIds,
    allFieldTypes,
    derivedAgeGroups,
    derivedFieldSizes,
  ];
}

class CreateTournamentAdditionalInfoStep extends CreateTournamentState {
  final Tournament tournament;
  final Map<String, String> validationErrors; // Keep validation errors for this step

  // Carry over relevant data for 'Back' navigation or context for the Review Screen
  final String? selectedAffiliatedClubId;
  final List<Venue>? availableVenues;
  final bool venuesLoading;
  final Set<String> selectedVenueIds;
  final Map<String, List<Field>> availableFieldsByVenueId;
  final Map<String, bool> fieldsLoadingByVenueId;
  final Set<String> selectedFieldIds;
  final List<FieldType>? allFieldTypes;
  final Set<String> derivedAgeGroups;
  final Set<String> derivedFieldSizes;


  const CreateTournamentAdditionalInfoStep({
    required this.tournament,
    this.validationErrors = const {},
    this.selectedAffiliatedClubId,
    this.availableVenues,
    this.venuesLoading = false,
    required this.selectedVenueIds, // Mandatory as this is result of prior step
    this.availableFieldsByVenueId = const {},
    this.fieldsLoadingByVenueId = const {},
    required this.selectedFieldIds, // Mandatory as this is result of prior step
    this.allFieldTypes,
    required this.derivedAgeGroups, // Calculated earlier and passed through
    required this.derivedFieldSizes, // Calculated earlier and passed through
  });

  CreateTournamentAdditionalInfoStep copyWith({
    Tournament? tournament,
    Map<String, String>? validationErrors,
    String? selectedAffiliatedClubId,
    List<Venue>? availableVenues,
    bool? venuesLoading,
    Set<String>? selectedVenueIds,
    Map<String, List<Field>>? availableFieldsByVenueId,
    Map<String, bool>? fieldsLoadingByVenueId,
    Set<String>? selectedFieldIds,
    List<FieldType>? allFieldTypes,
    Set<String>? derivedAgeGroups,
    Set<String>? derivedFieldSizes,
  }) {
    return CreateTournamentAdditionalInfoStep(
      tournament: tournament ?? this.tournament,
      validationErrors: validationErrors ?? this.validationErrors,
      selectedAffiliatedClubId: selectedAffiliatedClubId ?? this.selectedAffiliatedClubId,
      availableVenues: availableVenues ?? this.availableVenues,
      venuesLoading: venuesLoading ?? this.venuesLoading,
      selectedVenueIds: selectedVenueIds ?? this.selectedVenueIds,
      availableFieldsByVenueId: availableFieldsByVenueId ?? this.availableFieldsByVenueId,
      fieldsLoadingByVenueId: fieldsLoadingByVenueId ?? this.fieldsLoadingByVenueId,
      selectedFieldIds: selectedFieldIds ?? this.selectedFieldIds,
      allFieldTypes: allFieldTypes ?? this.allFieldTypes,
      derivedAgeGroups: derivedAgeGroups ?? this.derivedAgeGroups,
      derivedFieldSizes: derivedFieldSizes ?? this.derivedFieldSizes,
    );
  }

  @override
  List<Object?> get props => [
    tournament,
    validationErrors,
    selectedAffiliatedClubId,
    availableVenues,
    venuesLoading,
    selectedVenueIds,
    availableFieldsByVenueId,
    fieldsLoadingByVenueId,
    selectedFieldIds,
    allFieldTypes,
    derivedAgeGroups,
    derivedFieldSizes,
  ];
}

class CreateTournamentReviewStep extends CreateTournamentState {
  final Tournament tournament;
  // Carry over all data from previous steps for "Edit" navigation.
  final String? selectedAffiliatedClubId;
  final List<Venue>? availableVenues;
  final bool venuesLoading;
  final Set<String> selectedVenueIds;
  final Map<String, List<Field>> availableFieldsByVenueId;
  final Map<String, bool> fieldsLoadingByVenueId;
  final Set<String> selectedFieldIds;
  final List<FieldType>? allFieldTypes;
  final Set<String> derivedAgeGroups;
  final Set<String> derivedFieldSizes;


  const CreateTournamentReviewStep({
    required this.tournament,
    // Propagate all data for "Edit" buttons to navigate back correctly
    this.selectedAffiliatedClubId,
    this.availableVenues,
    this.venuesLoading = false,
    required this.selectedVenueIds,
    this.availableFieldsByVenueId = const {},
    this.fieldsLoadingByVenueId = const {},
    required this.selectedFieldIds,
    this.allFieldTypes,
    required this.derivedAgeGroups,
    required this.derivedFieldSizes,
  });

  CreateTournamentReviewStep copyWith({
    Tournament? tournament,
    String? selectedAffiliatedClubId,
    List<Venue>? availableVenues,
    bool? venuesLoading,
    Set<String>? selectedVenueIds,
    Map<String, List<Field>>? availableFieldsByVenueId,
    Map<String, bool>? fieldsLoadingByVenueId,
    Set<String>? selectedFieldIds,
    List<FieldType>? allFieldTypes,
    Set<String>? derivedAgeGroups,
    Set<String>? derivedFieldSizes,
  }) {
    return CreateTournamentReviewStep(
      tournament: tournament ?? this.tournament,
      selectedAffiliatedClubId: selectedAffiliatedClubId ?? this.selectedAffiliatedClubId,
      availableVenues: availableVenues ?? this.availableVenues,
      venuesLoading: venuesLoading ?? this.venuesLoading,
      selectedVenueIds: selectedVenueIds ?? this.selectedVenueIds,
      availableFieldsByVenueId: availableFieldsByVenueId ?? this.availableFieldsByVenueId,
      fieldsLoadingByVenueId: fieldsLoadingByVenueId ?? this.fieldsLoadingByVenueId,
      selectedFieldIds: selectedFieldIds ?? this.selectedFieldIds,
      allFieldTypes: allFieldTypes ?? this.allFieldTypes,
      derivedAgeGroups: derivedAgeGroups ?? this.derivedAgeGroups,
      derivedFieldSizes: derivedFieldSizes ?? this.derivedFieldSizes,
    );
  }

  @override
  List<Object?> get props => [
    tournament,
    selectedAffiliatedClubId,
    availableVenues,
    venuesLoading,
    selectedVenueIds,
    availableFieldsByVenueId,
    fieldsLoadingByVenueId,
    selectedFieldIds,
    allFieldTypes,
    derivedAgeGroups,
    derivedFieldSizes,
  ];
}