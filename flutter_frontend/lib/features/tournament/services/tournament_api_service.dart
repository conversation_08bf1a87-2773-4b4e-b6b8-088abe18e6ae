import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:soccer_frontend/data/models/tournament.dart';
import 'package:soccer_frontend/data/models/venue.dart' as app_venue;
import 'package:soccer_frontend/data/models/field.dart' as app_field;
import 'package:soccer_frontend/data/models/field_type.dart'; // Added import
import 'package:soccer_frontend/services/venue_service.dart';

class TournamentApiService {
  final SupabaseClient _supabaseClient = Supabase.instance.client;
  final VenueService _venueService = VenueService();

  Future<Tournament> saveTournament(Tournament tournament) async {
    print('--- Starting to Save Tournament ---');
    try {
      // Step 1: Create the main tournament record
      final tournamentData = tournament.toJson();

      // Log if managing club ID is present
      if (tournament.managingClubId != null) {
        print('Creating tournament with managing club ID: ${tournament.managingClubId}');
      }

      print('Creating tournament: $tournamentData');
      final tournamentResponse = await _supabaseClient.rpc(
        'create_tournament',
        params: {'p_tournament_data': tournamentData},
      );

      String? newTournamentId;
      Map<String, dynamic>? createdTournamentData;

      // Debug: Log the actual response structure
      print('DEBUG: tournamentResponse type: ${tournamentResponse.runtimeType}');
      print('DEBUG: tournamentResponse content: $tournamentResponse');

      // The RPC function returns the tournament data directly as a JSON object
      if (tournamentResponse is Map) {
        print('DEBUG: Processing as direct Map (tournament object)');
        createdTournamentData = Map<String, dynamic>.from(tournamentResponse);
        newTournamentId = createdTournamentData['id'] as String?;
        print('DEBUG: Extracted tournament data: $createdTournamentData');
        print('DEBUG: Extracted tournament ID: $newTournamentId');
      } else {
        print('DEBUG: Unexpected response type: ${tournamentResponse.runtimeType}');
        print('DEBUG: Expected Map but got: $tournamentResponse');
      }

      if (newTournamentId == null || createdTournamentData == null) {
        throw Exception('Failed to create tournament or retrieve its ID.');
      }
      print('Tournament created with ID: $newTournamentId');

      // Step 2: Link selected venues to the tournament
      if (tournament.selectedVenueIds != null && tournament.selectedVenueIds!.isNotEmpty) {
        print('Linking venues to tournament $newTournamentId: ${tournament.selectedVenueIds}');
        for (final venueId in tournament.selectedVenueIds!) {
          await _supabaseClient.from('tournament_venues').insert({
            'tournament_id': newTournamentId,
            'venue_id': venueId,
          });
        }
        print('Selected venues linked to tournament.');
      }

      // Step 3: Link selected fields to the tournament
      if (tournament.selectedFieldIds != null && tournament.selectedFieldIds!.isNotEmpty) {
        print('Linking fields to tournament $newTournamentId: ${tournament.selectedFieldIds}');
        for (final fieldId in tournament.selectedFieldIds!) {
          await _supabaseClient.from('tournament_fields').insert({
            'tournament_id': newTournamentId,
            'field_id': fieldId,
          });
        }
        print('Selected fields linked to tournament.');
      }

      print('--- Tournament Save Completed Successfully ---');

      // Return the created tournament
      return Tournament.fromJson(createdTournamentData);

    } on PostgrestException catch (e) {
      print('Supabase Error during tournament save process: ${e.message}');
      print('Details: ${e.details}');
      print('Hint: ${e.hint}');
      throw Exception('Failed to save tournament components: ${e.message}');
    } catch (e) {
      print('Unexpected error during tournament save process: $e');
      throw Exception('An unexpected error occurred while saving the tournament: $e');
    }
  }

  Future<List<Tournament>> getMyTournaments() async {
    try {
      final response = await _supabaseClient.rpc('get_director_tournaments'); // Assumes this RPC exists/will be created
      if (response is List) {
        return response.map((data) => Tournament.fromJson(data as Map<String, dynamic>)).toList();
      }
      return [];
    } catch (e) {
      print('Error fetching director tournaments: $e');
      throw Exception('Failed to fetch your tournaments: $e');
    }
  }

  Future<List<Tournament>> getAllTournaments() async {
    try {
      // This might call a generic get_tournaments RPC or query the table directly
      // For now, using a placeholder RPC name
      final response = await _supabaseClient.rpc('get_all_tournaments_public');
      if (response is List) {
        return response.map((data) => Tournament.fromJson(data as Map<String, dynamic>)).toList();
      }
      return [];
    } catch (e) {
      print('Error fetching all tournaments: $e');
      throw Exception('Failed to fetch all tournaments: $e');
    }
  }

  Future<Tournament> getTournamentById(String id) async {
    try {
      // Use the get_tournament_details RPC to ensure all columns are included
      final response = await _supabaseClient.rpc(
        'get_tournament_details',
        params: {'p_tournament_id': id},
      );

      if (response is List && response.isNotEmpty) {
        return Tournament.fromJson(response.first as Map<String, dynamic>);
      } else if (response is Map) {
        return Tournament.fromJson(response as Map<String, dynamic>);
      }

      throw Exception('Tournament not found or invalid response format.');
    } catch (e) {
      print('Error fetching tournament by ID $id: $e');
      throw Exception('Failed to fetch tournament details: $e');
    }
  }

  Future<Tournament> createTournament(Map<String, dynamic> data) async {
    // This is largely similar to the first step of saveTournament.
    // It might be better to refactor saveTournament or ensure this is used appropriately.
    // For now, it will call the same 'create_tournament' RPC.
    try {
      final response = await _supabaseClient.rpc(
        'create_tournament',
        params: {'p_tournament_data': data},
      );
      if (response is List && response.isNotEmpty) {
        return Tournament.fromJson(response.first as Map<String, dynamic>);
      } else if (response is Map) {
         return Tournament.fromJson(response as Map<String, dynamic>);
      }
      throw Exception('Failed to create tournament or retrieve its data.');
    } catch (e) {
      print('Error in createTournament service method: $e');
      throw Exception('Failed to create tournament via service: $e');
    }
  }

  Future<Tournament> updateTournament(String id, Map<String, dynamic> data) async {
    try {
      final response = await _supabaseClient
          .from('tournaments')
          .update(data)
          .eq('id', id)
          .select()
          .single();
      return Tournament.fromJson(response);
    } catch (e) {
      print('Error updating tournament $id: $e');
      throw Exception('Failed to update tournament: $e');
    }
  }

  Future<void> deleteTournament(String id) async {
    try {
      await _supabaseClient
          .from('tournaments')
          .delete()
          .eq('id', id);
    } catch (e) {
      print('Error deleting tournament $id: $e');
      throw Exception('Failed to delete tournament: $e');
    }
  }

  Future<List<app_venue.Venue>> getVenues() async {
    try {
      // Get venues for the current user
      final venueData = await _venueService.getVenues();
      return venueData.map((data) => app_venue.Venue.fromJson(data)).toList();
    } catch (e) {
      print('Error fetching venues: $e');
      throw Exception('Failed to fetch venues: $e');
    }
  }

  // Renamed original method, though it might be replaced by BLoC logic
  Future<List<app_field.Field>> getFieldsForVenueProcessed(String venueId, List<FieldType> allFieldTypes) async {
    try {
      final rawFieldDataList = await getFieldsForVenueRaw(venueId);
      final fieldTypesMap = {for (var ft in allFieldTypes) ft.id: ft.name};
      
      List<app_field.Field> processedFields = [];
      for (var rawField in rawFieldDataList) {
        final fieldTypeId = rawField['field_type_id'] as String?;
        if (fieldTypeId != null && fieldTypesMap.containsKey(fieldTypeId)) {
          rawField['field_type_name'] = fieldTypesMap[fieldTypeId];
        }
        processedFields.add(app_field.Field.fromJson(rawField));
      }
      return processedFields;
    } catch (e) {
      print('Error fetching and processing fields for venue $venueId: $e');
      throw Exception('Failed to fetch and process fields for venue: $e');
    }
  }

  Future<List<Map<String, dynamic>>> getFieldsForVenueRaw(String venueId) async {
    try {
      // _venueService.getFieldsForVenue calls the RPC and returns List<dynamic>
      // which is List<Map<String, dynamic>>
      final fieldData = await _venueService.getFieldsForVenue(venueId);
      // Ensure it's the correct type before returning
      return List<Map<String, dynamic>>.from(fieldData);
    } catch (e) {
      print('Error fetching raw fields for venue $venueId: $e');
      throw Exception('Failed to fetch raw fields for venue: $e');
    }
  }

  Future<List<FieldType>> getFieldTypes() async {
    try {
      final response = await _supabaseClient.rpc('get_field_types');
      if (response is List) {
        return response
            .map((data) => FieldType.fromJson(data as Map<String, dynamic>))
            .toList();
      }
      return [];
    } catch (e) {
      print('Error fetching field types: $e');
      throw Exception('Failed to fetch field types: $e');
    }
  }
}
