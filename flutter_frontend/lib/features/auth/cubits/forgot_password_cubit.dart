import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart'; // Import Equatable here
import 'package:logging/logging.dart';

import '../../../core/interfaces/auth_service_interface.dart';
import '../../../core/utils/app_exception.dart';

part 'forgot_password_state.dart'; // Include the state file as a part

class ForgotPasswordCubit extends Cubit<ForgotPasswordState> {
  final AuthServiceInterface _authService;
  final Logger _log = Logger('ForgotPasswordCubit');

  ForgotPasswordCubit({required AuthServiceInterface authService})
      : _authService = authService,
        super(const ForgotPasswordInitial());

  /// Requests a password reset email for the given email address.
  Future<void> requestPasswordReset(String email) async {
    _log.info('Requesting password reset for: $email');
    emit(const ForgotPasswordSubmitting());
    try {
      await _authService.requestPasswordReset(email);
      _log.info('Password reset email sent successfully for: $email');
      emit(ForgotPasswordSuccess(email: email));
    } on AppException catch (e) {
      _log.warning('Password reset failed for $email: ${e.message}', e.originalError);
      emit(ForgotPasswordFailure(message: e.message, error: e));
    } catch (e, s) {
      _log.severe('Unexpected error requesting password reset for $email', e, s);
      emit(ForgotPasswordFailure(message: 'An unexpected error occurred.', error: e));
    }
  }
}
