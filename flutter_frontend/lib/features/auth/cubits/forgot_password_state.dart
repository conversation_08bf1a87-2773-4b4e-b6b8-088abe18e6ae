part of 'forgot_password_cubit.dart';

abstract class ForgotPasswordState extends Equatable {
  const ForgotPasswordState();

  @override
  List<Object?> get props => [];
}

/// Initial state, form is ready.
class ForgotPasswordInitial extends ForgotPasswordState {
  const ForgotPasswordInitial();
}

/// State when the password reset request is being submitted.
class ForgotPasswordSubmitting extends ForgotPasswordState {
  const ForgotPasswordSubmitting();
}

/// State when the password reset email has been successfully sent.
class ForgotPasswordSuccess extends ForgotPasswordState {
  final String email;
  const ForgotPasswordSuccess({required this.email});

  @override
  List<Object?> get props => [email];
}

/// State when an error occurred during the password reset request.
class ForgotPasswordFailure extends ForgotPasswordState {
  final String message;
  final Object? error; // Optional original error object

  const ForgotPasswordFailure({required this.message, this.error});

  @override
  List<Object?> get props => [message, error];
}
