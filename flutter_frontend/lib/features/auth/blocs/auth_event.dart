// lib/features/auth/blocs/auth_event.dart
part of 'auth_bloc.dart';

abstract class AuthEvent extends Equatable {
  const AuthEvent();
  @override List<Object?> get props => [];
}

class AuthAppStarted extends AuthEvent {}

// Internal event - payload is Supabase AuthState
class _AuthChangeEventOccurred extends AuthEvent {
   final supabase.AuthState supabaseAuthState;
   const _AuthChangeEventOccurred(this.supabaseAuthState);
    @override List<Object?> get props => [supabaseAuthState];
}

class AuthSignInRequested extends AuthEvent {
  final String email;
  final String password;
  const AuthSignInRequested({required this.email, required this.password});
  @override List<Object?> get props => [email];
}

class AuthSignUpRequested extends AuthEvent {
  final String email;
  final String password;
  final String firstName;
  final String lastName;
  final String role; // Include role for individual signup
  const AuthSignUpRequested({ required this.email, required this.password, required this.firstName, required this.lastName, required this.role });
  @override List<Object?> get props => [email, firstName, lastName, role];
}

class AuthClubAdminRegisterRequested extends AuthEvent {
   final String clubName;
   final String adminFirstName;
   final String adminLastName;
   final String adminEmail;
   final String adminPassword;
   const AuthClubAdminRegisterRequested({ required this.clubName, required this.adminFirstName, required this.adminLastName, required this.adminEmail, required this.adminPassword });
   @override List<Object?> get props => [clubName, adminEmail, adminFirstName, adminLastName];
}

class AuthPasswordResetRequested extends AuthEvent {
  final String email;
  const AuthPasswordResetRequested({required this.email});
   @override List<Object?> get props => [email];
}

class AuthUpdatePasswordRequested extends AuthEvent {
  final String password;
  const AuthUpdatePasswordRequested({required this.password});
   @override List<Object?> get props => [password];
}

class AuthSignOutRequested extends AuthEvent {}

// Explicit event to refresh profile data if needed
class AuthProfileRefreshRequested extends AuthEvent {}

// Events for SupaEmailAuth component
class AuthSupaUISuccess extends AuthEvent {
  final supabase.Session session;
  const AuthSupaUISuccess(this.session);
  @override List<Object?> get props => [session];
}

class AuthSupaUIError extends AuthEvent {
  final dynamic error; // Can be AuthException or other errors
  const AuthSupaUIError(this.error);
  @override List<Object?> get props => [error];
}

class AuthProfileUpdateRequested extends AuthEvent {
  final String userId;
  final Map<String, dynamic> data;
  const AuthProfileUpdateRequested({required this.userId, required this.data});
  @override List<Object?> get props => [userId, data];
}

class AuthChangePasswordRequested extends AuthEvent {
  final String currentPassword;
  final String newPassword;
  const AuthChangePasswordRequested({required this.currentPassword, required this.newPassword});
  @override List<Object?> get props => [currentPassword, newPassword];
}
