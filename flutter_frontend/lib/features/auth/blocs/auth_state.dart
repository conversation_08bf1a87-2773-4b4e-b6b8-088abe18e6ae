// lib/features/auth/blocs/auth_state.dart
part of 'auth_bloc.dart';

abstract class AuthState extends Equatable {
  final supabase.User? user;
  final UserProfile? userProfile; // Holds fetched profile
  final List<ClubAffiliation>? directorAffiliations; // Added for TDs

  const AuthState({this.user, this.userProfile, this.directorAffiliations});

  @override
  List<Object?> get props => [user, userProfile, directorAffiliations];
}

// Initial state before checking auth status
class AuthInitial extends AuthState {
  const AuthInitial() : super();
}

// State during async operations like login, signup, profile fetch
class AuthLoading extends AuthState {
  const AuthLoading({supabase.User? user, UserProfile? userProfile, List<ClubAffiliation>? directorAffiliations})
      : super(user: user, userProfile: userProfile, directorAffiliations: directorAffiliations);
}

// User is authenticated AND profile is loaded successfully
class AuthAuthenticated extends AuthState {
  // user and userProfile are guaranteed non-null here via constructor assertion
  const AuthAuthenticated({
    required supabase.User user,
    required UserProfile userProfile,
    List<ClubAffiliation>? directorAffiliations, // Can be null if not a TD or no affiliations
  }) : super(user: user, userProfile: userProfile, directorAffiliations: directorAffiliations);

  @override
  List<Object?> get props => [user, userProfile, directorAffiliations];
}

// User is authenticated (Supabase user exists) BUT profile is missing or failed to load
class AuthAuthenticatedNoProfile extends AuthState {
   const AuthAuthenticatedNoProfile({required supabase.User user}) : super(user: user); // Profile and affiliations are implicitly null
    @override
   List<Object?> get props => [user];
}

// User is not authenticated
class AuthUnauthenticated extends AuthState {
  const AuthUnauthenticated({supabase.User? user}) : super(user: user); // Affiliations are null
}

// Specific state after requesting password reset (still unauthenticated)
class AuthPasswordResetEmailSent extends AuthUnauthenticated {
  final String email;
  const AuthPasswordResetEmailSent({required this.email, supabase.User? user}) : super(user: user);
   @override List<Object?> get props => [email, user];
}

// Specific state after signup requires email verification (user exists, no session)
class AuthVerificationEmailSent extends AuthUnauthenticated {
   final String email;
   // User might be available from signUp response even without session
  const AuthVerificationEmailSent({required this.email, supabase.User? user}) : super(user: user);
   @override List<Object?> get props => [email, user];
}

// Specific state when user is in password recovery flow (temp auth)
class AuthPasswordRecovery extends AuthState {
   // Requires profile for the reset screen context
   const AuthPasswordRecovery({
     required supabase.User user,
     required UserProfile userProfile,
     List<ClubAffiliation>? directorAffiliations, // Could be fetched if TD
   }) : super(user: user, userProfile: userProfile, directorAffiliations: directorAffiliations);
    @override List<Object?> get props => [user, userProfile, directorAffiliations];
}

// Specific state after password reset is successful (clears recovery flag internally)
class AuthPasswordResetSuccess extends AuthState {
    // Carry over user/profile from recovery state
    const AuthPasswordResetSuccess({
      supabase.User? user,
      UserProfile? userProfile,
      List<ClubAffiliation>? directorAffiliations,
    }) : super(user: user, userProfile: userProfile, directorAffiliations: directorAffiliations);
}

// Specific state after successful email confirmation -> leads to AuthAuthenticated
// Can be used for showing a specific success screen before redirecting
class AuthEmailConfirmationSuccess extends AuthAuthenticated {
   const AuthEmailConfirmationSuccess({
     required supabase.User user,
     required UserProfile userProfile,
     List<ClubAffiliation>? directorAffiliations,
   }) : super(user: user, userProfile: userProfile, directorAffiliations: directorAffiliations);
}

// General failure state for auth/profile operations
class AuthFailure extends AuthState {
  final String message;
  final dynamic error; // Optional original error for debugging

  const AuthFailure({
    required this.message,
    this.error,
    supabase.User? user,
    UserProfile? userProfile,
    List<ClubAffiliation>? directorAffiliations,
  }) : super(user: user, userProfile: userProfile, directorAffiliations: directorAffiliations); // Keep previous context if available

  @override
  List<Object?> get props => [message, error, user, userProfile, directorAffiliations];
}
