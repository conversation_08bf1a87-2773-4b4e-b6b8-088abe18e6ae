// lib/features/auth/blocs/auth_bloc.dart
import 'dart:async';
import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:logging/logging.dart';
import 'package:supabase_flutter/supabase_flutter.dart' as supabase; // Import with alias

// Import paths for feature-first architecture
import '../../../core/interfaces/auth_service_interface.dart';
import '../../../data/models/user_profile.dart';
import '../../../data/models/club_affiliation.dart'; // Added import
import '../../../core/utils/app_exception.dart';

part 'auth_event.dart';
part 'auth_state.dart';

class AuthBloc extends Bloc<AuthEvent, AuthState> {
  final AuthServiceInterface _authService;
  final Logger _log = Logger('AuthBloc');
  StreamSubscription<supabase.AuthState>? _authStateSubscription;

  AuthBloc({required AuthServiceInterface authService})
      : _authService = authService,
        super(const AuthInitial()) {

    on<AuthAppStarted>(_onAppStarted);
    on<_AuthChangeEventOccurred>(_onAuthChangeEventOccurred);
    on<AuthSignInRequested>(_onSignInRequested);
    on<AuthSignUpRequested>(_onSignUpRequested);
    on<AuthClubAdminRegisterRequested>(_onClubAdminRegisterRequested);
    on<AuthPasswordResetRequested>(_onPasswordResetRequested);
    on<AuthUpdatePasswordRequested>(_onUpdatePasswordRequested);
    on<AuthSignOutRequested>(_onSignOutRequested);
    on<AuthProfileRefreshRequested>(_onProfileRefreshRequested);
    on<AuthProfileUpdateRequested>(_onProfileUpdateRequested);
    on<AuthChangePasswordRequested>(_onChangePasswordRequested); // Added handler

    // Handlers for SupaEmailAuth component events
    on<AuthSupaUISuccess>(_onSupaUISuccess);
    on<AuthSupaUIError>(_onSupaUIError);

    _subscribeToAuthChanges();

    // Log initial session validity (non-blocking)
    Future.microtask(() => _authService.logInitialSessionValidity());
  }

  void _subscribeToAuthChanges() {
     _authStateSubscription?.cancel();
     // Listen to the stream from AuthService
     _authStateSubscription = _authService.onAuthStateChange.listen(
       (supabaseAuthState) => add(_AuthChangeEventOccurred(supabaseAuthState)),
       onError: (error, stackTrace) {
          _log.severe('Auth stream error: $error', error, stackTrace);
          emit(AuthFailure(message: 'Auth listener failed: $error', error: error));
       }
     );
     _log.info('Subscribed to Supabase auth state changes.');
  }

  @override
  Future<void> close() {
    _log.info('Closing AuthBloc and cancelling subscription.');
    _authStateSubscription?.cancel();
    return super.close();
  }

  // --- Event Handlers ---

  Future<void> _onAppStarted(AuthAppStarted event, Emitter<AuthState> emit) async {
    _log.info('[AuthBloc] AuthAppStarted: Checking initial session...');
    // Don't emit loading here initially, let the listener catch the initial state if available
    await Future.delayed(Duration.zero); // Allow listener to potentially fire first
    if (state is AuthInitial) { // Only proceed if still initial
        // Supabase client might have already restored session via adapter
        final currentSession = _authService.currentSession;
        final initialUser = currentSession?.user ?? _authService.currentUser; // Check both

        if (initialUser != null) {
            _log.info('[AuthBloc] AuthAppStarted: Found initial user ${initialUser.id}.');

            // --- Perform JWT Validity Check ---
            final bool isTokenValid = _authService.isCurrentTokenValid();
            _log.info('[AuthBloc] Initial token validity check: $isTokenValid');
            if (!isTokenValid && currentSession != null) {
               _log.warning('[AuthBloc] Initial session token is expired/invalid. Relying on Supabase refresh via listener.');
               // We DON'T emit AuthUnauthenticated here immediately.
               // The onAuthStateChange listener should receive a tokenRefreshed
               // event if the refresh token is valid, or signedOut if not.
               // Let the listener handle the state transition.
            }
            // --- End Check ---

            _log.info('[AuthBloc] Proceeding to fetch profile...');
            emit(AuthLoading(user: initialUser)); // Indicate loading profile
            await _fetchProfileAndEmit(initialUser, 'AuthAppStarted', emit);
        } else {
            _log.info('[AuthBloc] AuthAppStarted: No initial user found. Emitting AuthUnauthenticated.');
            emit(const AuthUnauthenticated());
        }
    } else {
         _log.info('[AuthBloc] AuthAppStarted: State already changed by listener, skipping initial check.');
    }
  }

  Future<void> _onAuthChangeEventOccurred(_AuthChangeEventOccurred event, Emitter<AuthState> emit) async {
     final supabaseAuthState = event.supabaseAuthState;
     final supabaseEvent = supabaseAuthState.event;
     final user = supabaseAuthState.session?.user;
     _log.info('[AuthBloc] Listener processing event: ${supabaseEvent}, User: ${user?.id ?? 'null'}, Current BLoC State: ${state.runtimeType}');

     // Check if user has changed or profile needs to be fetched
     bool needsProfileFetch = false;
     if (user != null) {
       // User ID changed or we don't have a profile yet
       if (state.user?.id != user.id) {
         _log.info('[AuthBloc] Listener detected user ID change: ${state.user?.id} -> ${user.id}');
         needsProfileFetch = true;
       } else if (state is AuthAuthenticatedNoProfile) {
         _log.info('[AuthBloc] Listener detected AuthAuthenticatedNoProfile state, will try to fetch profile');
         needsProfileFetch = true; // Always try to fetch profile if we're in NoProfile state
       } else if (state is! AuthAuthenticated && state is! AuthPasswordRecovery) {
         _log.info('[AuthBloc] Listener detected non-authenticated state with user: ${state.runtimeType}');
         needsProfileFetch = true; // Not in an authenticated state with profile
       }
     }

     switch (supabaseEvent) {
       case supabase.AuthChangeEvent.initialSession: // Add this case
         // Treat initialSession similar to signedIn or tokenRefreshed if a user exists
         // This event fires when the SDK initializes and finds an existing session.
         _log.info('[AuthBloc] Listener detected InitialSession.');
         if (user != null) {
           if (needsProfileFetch || state is AuthInitial) { // Also fetch if state is still AuthInitial
             _log.info('[AuthBloc] Listener InitialSession for ${user.id}, triggering profile fetch.');
             emit(AuthLoading(user: user, userProfile: state.userProfile));
             await _fetchProfileAndEmit(user, supabaseEvent.name, emit);
           } else {
             _log.fine('[AuthBloc] Listener InitialSession: User unchanged and profile exists.');
           }
         } else {
           _log.warning('[AuthBloc] Listener InitialSession: User object is null. Emitting AuthUnauthenticated.');
           if (state is! AuthUnauthenticated) emit(const AuthUnauthenticated());
         }
         break;
       case supabase.AuthChangeEvent.signedIn:
       case supabase.AuthChangeEvent.tokenRefreshed:
       case supabase.AuthChangeEvent.userUpdated:
         if (user != null) {
           if (needsProfileFetch) {
             _log.info('[AuthBloc] Listener detected SignedIn for ${user.id}, checking if profile fetch needed.');
             _log.info('[AuthBloc] Listener triggering profile fetch for ${user.id}.');
             emit(AuthLoading(user: user, userProfile: state.userProfile)); // Keep old profile while loading
             await _fetchProfileAndEmit(user, supabaseEvent.name, emit);
           } else {
             _log.fine('[AuthBloc] Listener: User unchanged and profile exists, no state change needed.');
             // If user object itself updated but ID is the same, might need to emit AuthAuthenticated again
             if (state is AuthAuthenticated && (state as AuthAuthenticated).user != user) {
               _log.info('[AuthBloc] User object updated, re-emitting AuthAuthenticated.');
               final currentState = state as AuthAuthenticated;
               emit(AuthAuthenticated(user: user, userProfile: currentState.userProfile!));
             }
           }
         } else {
           _log.warning('[AuthBloc] Listener: User object is null. Emitting AuthUnauthenticated.');
           if (state is! AuthUnauthenticated) emit(const AuthUnauthenticated());
         }
         break;

       case supabase.AuthChangeEvent.passwordRecovery:
         _log.info('[AuthBloc] *** PASSWORD RECOVERY EVENT RECEIVED ***');
         final user = event.supabaseAuthState.session?.user; // Ensure user is from the event
         if (user != null) {
           _log.info('[AuthBloc] Emitting immediate minimal AuthPasswordRecovery for user ${user.id}');
           // Emit a minimal recovery state immediately
           // Ensure UserProfile has a constructor or factory that can create a minimal profile
           final minimalProfile = UserProfile(
             id: user.id,
             email: user.email ?? '',
             // Provide default or empty values for other required fields if UserProfile demands them
             // e.g., firstName: '', lastName: '', role: 'user', etc.
             // Adjust according to your UserProfile model definition.
           );
           emit(AuthPasswordRecovery(user: user, userProfile: minimalProfile));
           _log.info('[AuthBloc] Minimal AuthPasswordRecovery emitted. Fetching full profile in background.');

           // Then fetch full profile and update again later
           unawaited(_fetchProfileAndEmit(
             user,
             supabase.AuthChangeEvent.passwordRecovery.name, // Use the enum's name
             emit,
             forceRecoveryState: true,
           ));
         } else {
           _log.warning('[AuthBloc] Password recovery event received but user was null.');
           emit(const AuthUnauthenticated());
         }
         break;

       case supabase.AuthChangeEvent.signedOut:
         _log.info('[AuthBloc] Event signedOut: Emitting AuthUnauthenticated.');
         if (state is! AuthUnauthenticated) emit(const AuthUnauthenticated());
         break;

       // Other events can be handled if needed
       case supabase.AuthChangeEvent.mfaChallengeVerified:
       case supabase.AuthChangeEvent.userDeleted:
         _log.info('[AuthBloc] Event $supabaseEvent received.');
         if (user == null && state is! AuthUnauthenticated && state is! AuthInitial) {
           _log.info('[AuthBloc] Event $supabaseEvent: User became null. Emitting AuthUnauthenticated.');
           emit(const AuthUnauthenticated());
         }
         break;
     }
  }

  // Helper to fetch profile and emit appropriate state
  Future<void> _fetchProfileAndEmit(supabase.User user, String eventName, Emitter<AuthState> emit, {bool forceRecoveryState = false}) async {
      _log.info('[AuthBloc] _fetchProfileAndEmit called for user ${user.id} from event $eventName.');
      try {
          final profile = await _authService.getUserProfile(user.id);
          _log.fine('[AuthBloc] Profile data received: ${profile != null ? profile.toJson() : 'null'}');

          List<ClubAffiliation>? affiliations;
          if (profile != null && profile.role == 'Tournament Director') {
            _log.info('[AuthBloc] User ${user.id} is a Tournament Director. Fetching affiliations...');
            affiliations = await _authService.getDirectorClubAffiliations(user.id);
            _log.info('[AuthBloc] Fetched ${affiliations.length} affiliations for TD ${user.id}.');
          }

          if (profile != null) {
             if (forceRecoveryState) {
                 _log.info('[AuthBloc] Emitting AuthPasswordRecovery for ${user.id}');
                 emit(AuthPasswordRecovery(user: user, userProfile: profile, directorAffiliations: affiliations));
             } else {
                 _log.info('[AuthBloc] Emitting AuthAuthenticated for ${user.id}');
                 emit(AuthAuthenticated(user: user, userProfile: profile, directorAffiliations: affiliations));
             }
          } else {
              _log.warning('[AuthBloc] Profile is null for user ${user.id}. Emitting AuthAuthenticatedNoProfile.');
              emit(AuthAuthenticatedNoProfile(user: user)); // Affiliations will be null here
          }
       } catch (e, s) {
           _log.severe('[AuthBloc] Error during _fetchProfileAndEmit ($eventName): $e', e, s);
           final message = (e is AppException) ? e.message : 'Failed to load profile or affiliation data.';
           // Preserve existing affiliations if any, during failure
           emit(AuthFailure(message: message, error: e, user: user, userProfile: state.userProfile, directorAffiliations: state.directorAffiliations));
       }
  }


  Future<void> _onSignInRequested(AuthSignInRequested event, Emitter<AuthState> emit) async {
     _log.info('[AuthBloc] AuthSignInRequested: For ${event.email}');
     emit(AuthLoading(user: state.user, userProfile: state.userProfile, directorAffiliations: state.directorAffiliations));
     try {
       await _authService.signIn(email: event.email, password: event.password);
       // State change handled by listener
        _log.info('[AuthBloc] AuthSignInRequested: Call successful, waiting for listener...');
     } on AppException catch (e) { // Catch specific AppException
        _log.warning('[AuthBloc] AuthSignInRequested: AppException: ${e.message}');
        emit(AuthFailure(message: e.message, error: e, user: state.user, userProfile: state.userProfile, directorAffiliations: state.directorAffiliations));
     } catch (e, s) {
        _log.severe('[AuthBloc] AuthSignInRequested: Unexpected error', e, s);
        emit(AuthFailure(message: 'An unexpected login error occurred.', error: e, user: state.user, userProfile: state.userProfile, directorAffiliations: state.directorAffiliations));
     }
  }

  Future<void> _onSignUpRequested(AuthSignUpRequested event, Emitter<AuthState> emit) async {
     _log.info('[AuthBloc] Event AuthSignUpRequested for ${event.email}');
     emit(AuthLoading(user: state.user, userProfile: state.userProfile, directorAffiliations: state.directorAffiliations));
     try {
       _log.fine('[AuthBloc] Calling authService.signUp with details: email=${event.email}, name=${event.firstName} ${event.lastName}, role=${event.role}');
       // *** Call the service method ***
       final response = await _authService.signUp(
         email: event.email,
         password: event.password,
         firstName: event.firstName,
         lastName: event.lastName,
         role: event.role,
       );
       _log.info('[AuthBloc] authService.signUp call completed.');
       _log.fine('[AuthBloc] SignUp Response User: ${response.user?.id}, Session: ${response.session?.accessToken != null}');

       // Check if confirmation is needed based on Supabase response
       // For email confirmation, we check both emailConfirmedAt and identities
       final bool emailConfirmed = response.user?.emailConfirmedAt != null;
       final bool hasIdentities = !(response.user?.identities?.isEmpty ?? true);
       final bool requiresConfirmation = !emailConfirmed;

       _log.fine('[AuthBloc] Email confirmation check: emailConfirmed=$emailConfirmed, hasIdentities=$hasIdentities');
       _log.fine('[AuthBloc] requiresConfirmation final result: $requiresConfirmation');

       // Always emit AuthVerificationEmailSent for signup flow with email confirmation
       // This ensures the user is directed to the check email screen
       _log.info('[AuthBloc] Emitting AuthVerificationEmailSent for ${event.email}');
       emit(AuthVerificationEmailSent(email: event.email, user: response.user));

       // Note: If email confirmation is not required, the auth state listener will
       // eventually receive a signedIn event and handle it appropriately
     } on AppException catch (e) {
        _log.warning('[AuthBloc] SignUp failed: ${e.message}', e.originalError); // Log original error too
        emit(AuthFailure(message: e.message, error: e, user: state.user, userProfile: state.userProfile, directorAffiliations: state.directorAffiliations));
     } catch (e, s) {
        _log.severe('[AuthBloc] Unexpected SignUp error', e, s);
        emit(AuthFailure(message: 'An unexpected sign up error occurred.', error: e, user: state.user, userProfile: state.userProfile, directorAffiliations: state.directorAffiliations));
     }
  }

  Future<void> _onClubAdminRegisterRequested(AuthClubAdminRegisterRequested event, Emitter<AuthState> emit) async {
      _log.info('[AuthBloc] AuthClubAdminRegisterRequested: For ${event.adminEmail}');
      emit(AuthLoading(user: state.user, userProfile: state.userProfile, directorAffiliations: state.directorAffiliations));
      try {
        await _authService.registerClubAdmin(
           clubName: event.clubName, adminFirstName: event.adminFirstName,
           adminLastName: event.adminLastName, adminEmail: event.adminEmail,
           adminPassword: event.adminPassword,
        );
         _log.info('[AuthBloc] AuthClubAdminRegisterRequested: RPC call initiated.');
        // Assume email confirmation is required
        emit(AuthVerificationEmailSent(email: event.adminEmail));
      } on AppException catch (e) {
         _log.warning('[AuthBloc] AuthClubAdminRegisterRequested: AppException: ${e.message}');
         emit(AuthFailure(message: e.message, error: e, user: state.user, userProfile: state.userProfile, directorAffiliations: state.directorAffiliations));
      } catch (e, s) {
         _log.severe('[AuthBloc] AuthClubAdminRegisterRequested: Unexpected error', e, s);
         emit(AuthFailure(message: 'Club registration failed: ${e.toString()}', error: e, user: state.user, userProfile: state.userProfile, directorAffiliations: state.directorAffiliations));
      }
  }

  Future<void> _onPasswordResetRequested(
    AuthPasswordResetRequested event,
    Emitter<AuthState> emit,
  ) async {
    _log.info('[_onPasswordResetRequested] ⇒ Enter (email=${event.email})');
    // Keep previous state only for potential revert on *failure*
    final previousState = state is AuthLoading ? const AuthUnauthenticated() : state;

    _log.info('[_onPasswordResetRequested] ⇒ ENTRY POINT with email: ${event.email}');
    print('>>> [_onPasswordResetRequested] ENTRY POINT with email: ${event.email}');
    _log.info('[_onPasswordResetRequested] Current state: ${state.runtimeType}');
    print('>>> [_onPasswordResetRequested] Current state: ${state.runtimeType}');

    emit(AuthLoading(user: state.user, userProfile: state.userProfile, directorAffiliations: state.directorAffiliations));
    _log.info('[_onPasswordResetRequested] Emitted AuthLoading state');
    print('>>> [_onPasswordResetRequested] Emitted AuthLoading state');

    try {
      _log.info('[_onPasswordResetRequested] Calling authService.requestPasswordReset...');
      print('>>> [_onPasswordResetRequested] Calling authService.requestPasswordReset...');
      await _authService.requestPasswordReset(event.email);
      _log.info('[_onPasswordResetRequested] ⇒ reset email sent successfully');

      // EMIT SUCCESS AND *STOP* for this handler. Let UI react.
      _log.fine('[_onPasswordResetRequested] About to emit AuthPasswordResetEmailSent');
      print('>>> ABOUT TO EMIT AuthPasswordResetEmailSent for ${event.email}');
      final newState = AuthPasswordResetEmailSent(email: event.email);
      print('>>> Created new state: ${newState.runtimeType}');
      emit(newState); // <<< THE KEY EMIT
      print('>>> EMITTED AuthPasswordResetEmailSent <<<');
      _log.info('[_onPasswordResetRequested] Successfully emitted AuthPasswordResetEmailSent');

      // DEBUG: Print the current state after emission
      print('>>> Current state after emission: ${state.runtimeType}');
      print('>>> Current state is AuthPasswordResetEmailSent? ${state is AuthPasswordResetEmailSent}');
      if (state is AuthPasswordResetEmailSent) {
        print('>>> Email in state: ${(state as AuthPasswordResetEmailSent).email}');
      }

      // NO REVERT ON SUCCESS
    } on AppException catch (e) {
      _log.warning('[_onPasswordResetRequested] ⇒ ERROR (AppException): ${e.message}', e.originalError);
      // Check if this is a rate limit error - use readable message from AppException
      if (e.message.contains('Too many attempts')) {
        _log.fine('[_onPasswordResetRequested] Rate limit error detected');
        emit(AuthFailure(message: e.message, error: e, user: state.user, userProfile: state.userProfile, directorAffiliations: state.directorAffiliations));
        _log.info('[_onPasswordResetRequested] Emitted AuthFailure (Rate Limit)');
      } else {
        _log.fine('[_onPasswordResetRequested] Non-rate limit error, emitting failure');
        emit(AuthFailure(message: e.message, error: e, user: state.user, userProfile: state.userProfile, directorAffiliations: state.directorAffiliations));
        _log.info('[_onPasswordResetRequested] Emitted AuthFailure. Will revert state after delay.');
        await Future.delayed(const Duration(seconds: 3));
        if (state is AuthFailure) { // Check if still in failure state
          _log.info('[_onPasswordResetRequested] Reverting state to ${previousState.runtimeType}');
          emit(previousState);
        }
      }
    } catch (e, s) {
      _log.severe('[_onPasswordResetRequested] ⇒ ERROR (Unexpected): $e', e, s);
      emit(AuthFailure(message: 'Password reset request failed: ${e.toString()}', error: e, user: state.user, userProfile: state.userProfile, directorAffiliations: state.directorAffiliations));
      _log.info('[_onPasswordResetRequested] Emitted AuthFailure (Unexpected). Will revert state after delay.');
      await Future.delayed(const Duration(seconds: 3));
      if (state is AuthFailure) {
        _log.info('[_onPasswordResetRequested] Reverting state to ${previousState.runtimeType}');
        emit(previousState);
      }
    }
    _log.info('[_onPasswordResetRequested] ⇐ Exit');
  }

  Future<void> _onUpdatePasswordRequested(AuthUpdatePasswordRequested event, Emitter<AuthState> emit) async {
     _log.info('[AuthBloc] AuthUpdatePasswordRequested: Processing password update');
     emit(AuthLoading(user: state.user, userProfile: state.userProfile, directorAffiliations: state.directorAffiliations)); // Indicate loading
     try {
       // Assume we are in the recovery flow if this event is dispatched
       // No need to check state here, as reaching ResetPasswordScreen implies recovery context
       await _authService.updatePassword(event.password);
       _log.info('[AuthBloc] AuthUpdatePasswordRequested: Password updated successfully via recovery flow.');

       // Emit success state temporarily
       emit(AuthPasswordResetSuccess(user: state.user, userProfile: state.userProfile, directorAffiliations: state.directorAffiliations));

       // After a short delay, transition to unauthenticated state to force login
       // This ensures the user has to log in with their new password
       await Future.delayed(const Duration(seconds: 2));
       _log.info('[AuthBloc] AuthUpdatePasswordRequested: Transitioning to unauthenticated state after password reset.');
       // Ensure we emit AuthUnauthenticated even if the previous state wasn't AuthPasswordResetSuccess
       // (e.g., if another event occurred during the delay)
       emit(const AuthUnauthenticated()); 

     } on AppException catch (e) {
       _log.warning('[AuthBloc] AuthUpdatePasswordRequested: AppException: ${e.message}');
       emit(AuthFailure(message: e.message, error: e, user: state.user, userProfile: state.userProfile, directorAffiliations: state.directorAffiliations));
     } catch (e, s) {
       _log.severe('[AuthBloc] AuthUpdatePasswordRequested: Unexpected error', e, s);
       emit(AuthFailure(message: 'Password update failed: ${e.toString()}', error: e, user: state.user, userProfile: state.userProfile, directorAffiliations: state.directorAffiliations));
     }
  }

  Future<void> _onSignOutRequested(AuthSignOutRequested event, Emitter<AuthState> emit) async {
      _log.info('[AuthBloc] AuthSignOutRequested: Signing out...');
      // Emit Loading, clearing user data optimistically
      emit(AuthLoading(user: null, userProfile: null, directorAffiliations: null));
     try {
       await _authService.signOut();
        _log.info('[AuthBloc] AuthSignOutRequested: Sign out call successful, waiting for listener...');
        // Listener will emit AuthUnauthenticated
     } catch (e, s) {
       _log.severe('[AuthBloc] AuthSignOutRequested: Sign out error', e, s);
        // Ensure unauthenticated state even on error
        emit(AuthFailure(message: 'Sign out failed: ${e.toString()}', error: e));
        await Future.delayed(Duration.zero);
        emit(const AuthUnauthenticated());
     }
  }

   Future<void> _onProfileRefreshRequested(AuthProfileRefreshRequested event, Emitter<AuthState> emit) async {
       final currentUser = state.user;
       if (currentUser != null) {
           _log.info('[AuthBloc] AuthProfileRefreshRequested: Refreshing profile for ${currentUser.id}');
           // Keep existing profile and affiliations while loading, indicate loading
           emit(AuthLoading(user: currentUser, userProfile: state.userProfile, directorAffiliations: state.directorAffiliations));
           await _fetchProfileAndEmit(currentUser, 'AuthProfileRefreshRequested', emit);
       } else {
           _log.warning('[AuthBloc] AuthProfileRefreshRequested: Cannot refresh, no user logged in.');
           if (state is! AuthUnauthenticated) emit(const AuthUnauthenticated());
       }
   }

  // --- SupaEmailAuth Event Handlers ---

  Future<void> _onSupaUISuccess(AuthSupaUISuccess event, Emitter<AuthState> emit) async {
    _log.info('[AuthBloc] SupaUI Success reported for user: ${event.session.user.id}');
    // Similar logic to SignedIn event: fetch profile and affiliations
    emit(AuthLoading(user: event.session.user, userProfile: state.userProfile, directorAffiliations: state.directorAffiliations));
    await _fetchProfileAndEmit(event.session.user, 'SupaUISuccess', emit);
  }

  void _onSupaUIError(AuthSupaUIError event, Emitter<AuthState> emit) {
    _log.warning('[AuthBloc] SupaUI Error reported: ${event.error}');
    // Convert the error (if possible) and emit failure
    final appException = (event.error is supabase.AuthException || event.error is supabase.PostgrestException)
                        ? AppException.fromSupabaseError(event.error)
                        : AppException('An error occurred during authentication.', originalError: event.error);
    emit(AuthFailure(message: appException.message, error: event.error, user: state.user, userProfile: state.userProfile, directorAffiliations: state.directorAffiliations));
  }

  Future<void> _onProfileUpdateRequested(AuthProfileUpdateRequested event, Emitter<AuthState> emit) async {
    _log.info('[AuthBloc] AuthProfileUpdateRequested for user ${event.userId}');
    // Keep current user, profile, and affiliations while loading
    emit(AuthLoading(user: state.user, userProfile: state.userProfile, directorAffiliations: state.directorAffiliations));
    try {
      await _authService.updateProfile(event.userId, event.data);
      _log.info('[AuthBloc] Profile update successful for ${event.userId}. Triggering profile refresh.');
      // After successful update, refresh the entire profile and affiliations
      // to ensure consistency and get any server-side changes.
      if (state.user != null) {
        await _fetchProfileAndEmit(state.user!, 'AuthProfileUpdateRequested_PostUpdate', emit);
      } else {
         _log.warning('[AuthBloc] User became null after profile update attempt. This should not happen.');
         emit(const AuthUnauthenticated());
      }
    } on AppException catch (e) {
      _log.warning('[AuthBloc] AuthProfileUpdateRequested: AppException: ${e.message}');
      emit(AuthFailure(
        message: e.message,
        error: e,
        user: state.user,
        userProfile: state.userProfile, // Revert to old profile on failure
        directorAffiliations: state.directorAffiliations, // Revert to old affiliations
      ));
    } catch (e, s) {
      _log.severe('[AuthBloc] AuthProfileUpdateRequested: Unexpected error', e, s);
      emit(AuthFailure(
        message: 'An unexpected error occurred while updating profile.',
        error: e,
        user: state.user,
        userProfile: state.userProfile,
        directorAffiliations: state.directorAffiliations,
      ));
    }
  }

  Future<void> _onChangePasswordRequested(AuthChangePasswordRequested event, Emitter<AuthState> emit) async {
    _log.info('[AuthBloc] AuthChangePasswordRequested');
    emit(AuthLoading(user: state.user, userProfile: state.userProfile, directorAffiliations: state.directorAffiliations));
    try {
      await _authService.changePassword(
        currentPassword: event.currentPassword,
        newPassword: event.newPassword,
      );
      _log.info('[AuthBloc] Password changed successfully. User might need to re-authenticate or session might be updated by Supabase.');
      // Emitting a generic success or letting onAuthStateChange handle it.
      // For simplicity, we can emit a temporary success state or just rely on onAuthStateChange.
      // If Supabase invalidates the session, onAuthStateChange will lead to AuthUnauthenticated.
      // If it updates the user object, onAuthStateChange might re-trigger _fetchProfileAndEmit.
      // For now, let's assume the user remains authenticated and refresh their profile.
      if (state.user != null) {
         // Optionally, emit a specific AuthPasswordChangeSuccess state if needed by UI
        emit(AuthPasswordResetSuccess(user: state.user, userProfile: state.userProfile, directorAffiliations: state.directorAffiliations)); // Re-using for general password success
        // await _fetchProfileAndEmit(state.user!, 'AuthChangePasswordRequested_PostUpdate', emit);
      } else {
        emit(const AuthUnauthenticated()); // Should not happen if changePassword requires auth
      }
    } on AppException catch (e) {
      _log.warning('[AuthBloc] AuthChangePasswordRequested: AppException: ${e.message}');
      emit(AuthFailure(
        message: e.message,
        error: e,
        user: state.user,
        userProfile: state.userProfile,
        directorAffiliations: state.directorAffiliations,
      ));
    } catch (e, s) {
      _log.severe('[AuthBloc] AuthChangePasswordRequested: Unexpected error', e, s);
      emit(AuthFailure(
        message: 'An unexpected error occurred while changing password.',
        error: e,
        user: state.user,
        userProfile: state.userProfile,
        directorAffiliations: state.directorAffiliations,
      ));
    }
  }
}
