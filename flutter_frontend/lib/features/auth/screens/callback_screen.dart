import 'package:flutter/material.dart' hide CallbackAction;
import 'package:go_router/go_router.dart';
import 'package:logging/logging.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../utils/callback_handler.dart';

/// Screen that handles auth callbacks from Supabase
/// This screen is shown when the user clicks on a link in an email
/// (confirmation, password reset, etc.)
class CallbackScreen extends StatefulWidget {
  const CallbackScreen({super.key});

  @override
  State<CallbackScreen> createState() => _CallbackScreenState();
}

class _CallbackScreenState extends State<CallbackScreen> {
  final _log = Logger('CallbackScreen');
  final _callbackHandler = CallbackHandler();
  bool _isLoading = true;
  String? _error;
  String? _callbackType;

  @override
  void initState() {
    super.initState();
    _processCallback();
  }

  Future<void> _processCallback() async {
    try {
      _log.info('[CallbackScreen] ⇒ Enter _processCallback');
      print(">>> CallbackScreen: Processing callback");

      // Try to get the current URL (for web)
      Uri? uri;

      try {
        // This works for web
        uri = Uri.base;
        _log.info('[CallbackScreen] processing URI = ${Uri.base}');
        print(">>> CallbackScreen URI: $uri");

        // 1) Hydrate Supabase with the tokens from URL fragments
        try {
          _log.info('[CallbackScreen] 🔄 Calling Supabase.instance.client.auth.getSessionFromUrl()');
          print(">>> CallbackScreen: Calling getSessionFromUrl to process tokens in fragment");
          await Supabase.instance.client.auth.getSessionFromUrl(uri);
          _log.info('[CallbackScreen] ✅ Supabase session from URL processed successfully');
          print(">>> CallbackScreen: getSessionFromUrl completed successfully");
        } catch (e, st) {
          _log.warning('[CallbackScreen] ⚠️ getSessionFromUrl failed: $e', e, st);
          print(">>> CallbackScreen: getSessionFromUrl failed: $e");
        }

        // 2) Parse both query parameters AND fragment
        // Log query parameters in detail
        final queryParams = uri.queryParameters;
        _log.info('[CallbackScreen] Query parameters: $queryParams');
        print(">>> CallbackScreen: Query parameters: $queryParams");

        // Parse fragment parameters (critical for Supabase auth)
        final fragmentParams = uri.fragment.isNotEmpty
            ? Uri.splitQueryString(uri.fragment)
            : <String, String>{};
        _log.info('[CallbackScreen] Fragment parameters: $fragmentParams');
        print(">>> CallbackScreen: Fragment parameters: $fragmentParams");

        // Combine both for complete parameter access
        final allParams = {
          ...queryParams,
          ...fragmentParams,
        };
        _log.info('[CallbackScreen] Combined parameters: $allParams');
        print(">>> CallbackScreen: Combined parameters: $allParams");

        // Check for type parameter (could be in fragment or query)
        if (allParams.containsKey('type')) {
          _log.info('[CallbackScreen] Callback type: ${allParams["type"]}');
          print(">>> CallbackScreen: Callback type: ${allParams["type"]}");
          _callbackType = allParams["type"];
        }

        // Check for access_token (in fragment)
        if (fragmentParams.containsKey('access_token')) {
          final token = fragmentParams["access_token"] ?? '';
          final tokenPreview = token.length > 10 ? '${token.substring(0, 10)}...' : token;
          _log.info('[CallbackScreen] Access token present: $tokenPreview');
          print(">>> CallbackScreen: Access token present: $tokenPreview");
        }

        // Check for error parameters
        if (allParams.containsKey('error')) {
          _log.warning('[CallbackScreen] Error parameter: ${allParams["error"]}');
          print(">>> CallbackScreen: ERROR parameter: ${allParams["error"]}");
        }
        if (allParams.containsKey('error_description')) {
          _log.warning('[CallbackScreen] Error description: ${allParams["error_description"]}');
          print(">>> CallbackScreen: ERROR description: ${allParams["error_description"]}");
        }
      } catch (e) {
        _log.warning('[CallbackScreen] Uri.base not available (likely on mobile): $e');
        print(">>> CallbackScreen: Uri.base not available");
      }

      CallbackResult result;

      // If we couldn't get the URI (on mobile), we'll try to extract it from the route
      if (uri == null || uri.toString().isEmpty) {
        // For mobile, we'll rely on the deep link handling in app.dart
        // which should have already navigated us here
        _log.info('[CallbackScreen] Using mobile callback handling');
        print(">>> CallbackScreen: Using mobile callback handling");
        result = await _callbackHandler.processMobileCallback();
      } else {
        // For web, process the URI parameters
        _log.info('[CallbackScreen] Processing web callback URI: $uri');
        print(">>> CallbackScreen: Processing web callback URI");
        result = _callbackHandler.processCallbackUri(uri);
      }

      _log.info('[CallbackScreen] → CallbackResult: action=${result.action}, type=${result.callbackType}');
      print(">>> CallbackScreen: Result action=${result.action}, type=${result.callbackType}");


      // Update the UI based on the result
      setState(() {
        _callbackType = result.callbackType;
        if (result.action == CallbackAction.showError) {
          _error = result.errorMessage;
          _isLoading = false;
        }
      });

      // Perform the appropriate navigation
      Future.delayed(Duration.zero, () {
        if (!mounted) return;

        _log.info('*** CALLBACK PROCESSING RESULT ***');
        _log.info('CallbackScreen - Processing result action: ${result.action}');
        _log.info('CallbackScreen - Callback type: ${result.callbackType}');

        switch (result.action) {
          case CallbackAction.navigateToResetPassword:
            _log.info('CallbackScreen - Navigating to reset password screen');
            _log.info('CallbackScreen - Current context mounted: $mounted');
            context.go('/auth/reset-password');
            _log.info('CallbackScreen - Navigation to /auth/reset-password completed');
            break;
          case CallbackAction.navigateToConfirmationSuccess:
            _log.info('CallbackScreen - Navigating to confirmation success screen');
            context.go('/auth/confirmation-success');
            _log.info('CallbackScreen - Navigation to /auth/confirmation-success completed');
            break;
          case CallbackAction.navigateToHome:
            _log.info('CallbackScreen - Navigating to home screen');
            context.go('/');
            _log.info('CallbackScreen - Navigation to / completed');
            break;
          case CallbackAction.navigateToLogin:
            _log.info('CallbackScreen - Navigating to login screen');
            context.go('/login');
            _log.info('CallbackScreen - Navigation to /login completed');
            break;
          case CallbackAction.showError:
            _log.warning('CallbackScreen - Showing error: ${result.errorMessage}');
            // Error is already displayed in the UI
            break;
        }
      });

      _log.info('[CallbackScreen] ⇐ Exit _processCallback');
      print(">>> CallbackScreen: Callback processing completed");
    } catch (e, stackTrace) {
      _log.severe('[CallbackScreen] ⇒ ERROR processing callback', e, stackTrace);
      print(">>> CallbackScreen ERROR: $e");
      _handleError('An unexpected error occurred while processing the authentication callback.');
    }
    _log.info('[CallbackScreen] ⇐ Exit _processCallback (final)');
  }

  void _handleError(String errorMessage) {
    _log.warning('Callback error: $errorMessage');
    setState(() {
      _isLoading = false;
      _error = errorMessage;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: _error != null
              ? _buildErrorContent()
              : _buildLoadingContent(),
        ),
      ),
    );
  }

  Widget _buildLoadingContent() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        _isLoading ? const CircularProgressIndicator() : const SizedBox(height: 24),
        const SizedBox(height: 24),
        Text(
          _getLoadingMessage(),
          textAlign: TextAlign.center,
          style: const TextStyle(fontSize: 16),
        ),
      ],
    );
  }

  String _getLoadingMessage() {
    if (_callbackType == 'recovery') {
      return 'Verifying your password reset request...';
    } else if (_callbackType == 'signup') {
      return 'Confirming your email address...';
    } else {
      return 'Processing authentication...';
    }
  }

  Widget _buildErrorContent() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const Icon(
          Icons.error_outline,
          color: Colors.red,
          size: 64,
        ),
        const SizedBox(height: 16),
        const Text(
          'Authentication Error',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          _error!,
          textAlign: TextAlign.center,
          style: const TextStyle(fontSize: 16),
        ),
        const SizedBox(height: 24),
        ElevatedButton(
          onPressed: () => context.go('/login'),
          child: const Text('Back to Login'),
        ),
      ],
    );
  }
}
