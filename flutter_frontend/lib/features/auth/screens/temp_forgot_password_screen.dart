import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class TempForgotPasswordScreen extends StatelessWidget {
  const TempForgotPasswordScreen({super.key});

  @override
  Widget build(BuildContext context) {
    print('>>> TempForgotPasswordScreen BUILD METHOD CALLED <<<');
    return Scaffold(
      appBar: AppBar(
        title: const Text('Temporary Forgot Password'),
        leading: Icon<PERSON><PERSON>on(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            print('>>> TempForgot: Back button pressed <<<');
            context.go('/login');
          },
        ),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text('This is the temporary forgot password screen.'),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: () {
                print('>>> TempForgot: Go to Login button pressed <<<');
                context.go('/login');
              },
              child: const Text('Go Back to Login (from Temp)'),
            ),
          ],
        ),
      ),
    );
  }
}
