import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:supabase_auth_ui/supabase_auth_ui.dart';
import 'package:logging/logging.dart';

import '../blocs/auth_bloc.dart';

/// A consolidated authentication screen that uses Supabase's ready-made SupaEmailAuth component
/// for login and a custom form for signup to support password verification and role selection.
class AuthScreen extends StatefulWidget {
  /// Creates an AuthScreen.
  const AuthScreen({super.key});

  @override
  State<AuthScreen> createState() => _AuthScreenState();
}

class _AuthScreenState extends State<AuthScreen> {
  final _log = Logger('AuthScreen');
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _firstNameController = TextEditingController();
  final _lastNameController = TextEditingController();
  String? _selectedRole;
  bool _isLoading = false;
  bool _isSignUp = false;
  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;
  String? _errorMessage;

  final List<Map<String, String>> _roleOptions = [
    {'value': 'player_parent', 'label': 'Player/Parent'},
    {'value': 'coach', 'label': 'Coach'},
    {'value': 'referee', 'label': 'Referee'},
    {'value': 'tournament_director', 'label': 'Tournament Director'},
  ];

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _firstNameController.dispose();
    _lastNameController.dispose();
    super.dispose();
  }

  Future<void> _signUp() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final email = _emailController.text.trim();
      final password = _passwordController.text;
      final firstName = _firstNameController.text.trim();
      final lastName = _lastNameController.text.trim();
      final role = _selectedRole!;

      // Prepare metadata
      final metadata = {
        'first_name': firstName,
        'last_name': lastName,
        'user_role': role,
      };

      _log.info('Signing up user with email: $email and role: $role');

      // Get the redirect URL based on platform
      final redirectUrl = kIsWeb
          ? 'http://localhost:8114/callback.html' // Use static HTML page for web
          : (Platform.isIOS || Platform.isAndroid)
              ? 'tournamentscheduler:///auth/callback'
              : 'http://localhost:8114/callback.html';

      // Call Supabase signUp
      final response = await Supabase.instance.client.auth.signUp(
        email: email,
        password: password,
        data: metadata,
        emailRedirectTo: redirectUrl,
      );

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        if (response.session != null) {
          // User is immediately signed in (auto-confirm is enabled)
          context.read<AuthBloc>().add(AuthSupaUISuccess(response.session!));
        } else {
          // User needs to confirm email
          context.go('/check-email?email=$email');
        }
      }
    } catch (e) {
      _log.severe('Error during sign up: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
          _errorMessage = e.toString();
        });

        // Show error in a snackbar
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );

        // Notify the BLoC
        context.read<AuthBloc>().add(AuthSupaUIError(e.toString()));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Account Login'),
      ),
      body: Center(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.sports_soccer,
                size: 80,
                color: Colors.blue,
              ),
              const SizedBox(height: 24),
              const Text(
                'Tournament Scheduler',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 32),
              // Toggle between sign in and sign up
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  TextButton(
                    onPressed: () => setState(() => _isSignUp = false),
                    style: TextButton.styleFrom(
                      backgroundColor: !_isSignUp ? Colors.blue.withAlpha(25) : null,
                    ),
                    child: const Text('Sign In'),
                  ),
                  const SizedBox(width: 16),
                  TextButton(
                    onPressed: () => setState(() => _isSignUp = true),
                    style: TextButton.styleFrom(
                      backgroundColor: _isSignUp ? Colors.blue.withAlpha(25) : null,
                    ),
                    child: const Text('Sign Up'),
                  ),
                ],
              ),
              TextButton(
                onPressed: () => context.go('/register-club'),
                child: const Text('Register a Club'),
              ),
              const SizedBox(height: 16),
              // Show error message if any
              if (_errorMessage != null)
                Padding(
                  padding: const EdgeInsets.only(bottom: 16),
                  child: Text(
                    _errorMessage!,
                    style: const TextStyle(color: Colors.red),
                  ),
                ),
              // Sign In Form (using SupaEmailAuth)
              if (!_isSignUp)
                SupaEmailAuth(
                  redirectTo: kIsWeb
                    ? 'http://localhost:8114/callback.html' // Use static HTML page for web
                    : (Platform.isIOS || Platform.isAndroid)
                        ? 'tournamentscheduler:///auth/callback'
                        : 'http://localhost:8114/callback.html',
                  onSignInComplete: (response) {
                    if (response.session != null) {
                      context.read<AuthBloc>().add(AuthSupaUISuccess(response.session!));
                    } else {
                      context.read<AuthBloc>().add(const AuthSupaUIError('Login succeeded but session was null.'));
                    }
                  },
                  onSignUpComplete: (response) {
                    // This shouldn't be called since we're only using SupaEmailAuth for sign in
                    _log.warning('Unexpected onSignUpComplete called in sign-in only mode');
                  },
                  onError: (error) {
                    _log.warning('Auth error: $error');
                    context.read<AuthBloc>().add(AuthSupaUIError(error));

                    // Show error in a snackbar
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('Error: $error'),
                        backgroundColor: Colors.red,
                      ),
                    );
                  },
                  // Force sign-in mode only
                ),
              // Custom Sign Up Form
              if (_isSignUp)
                Form(
                  key: _formKey,
                  child: Column(
                    children: [
                      // First name field
                      TextFormField(
                        controller: _firstNameController,
                        decoration: const InputDecoration(
                          labelText: 'First Name',
                          prefixIcon: Icon(Icons.person),
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter your first name';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),
                      // Last name field
                      TextFormField(
                        controller: _lastNameController,
                        decoration: const InputDecoration(
                          labelText: 'Last Name',
                          prefixIcon: Icon(Icons.person),
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter your last name';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),
                      // Role dropdown
                      DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'Role',
                          prefixIcon: Icon(Icons.assignment_ind),
                        ),
                        value: _selectedRole,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please select your role';
                          }
                          return null;
                        },
                        onChanged: (String? newValue) {
                          setState(() {
                            _selectedRole = newValue;
                          });
                        },
                        items: _roleOptions.map<DropdownMenuItem<String>>((Map<String, String> option) {
                          return DropdownMenuItem<String>(
                            value: option['value'],
                            child: Text(option['label']!),
                          );
                        }).toList(),
                      ),
                      const SizedBox(height: 16),
                      // Email field
                      TextFormField(
                        controller: _emailController,
                        decoration: const InputDecoration(
                          labelText: 'Email',
                          prefixIcon: Icon(Icons.email),
                        ),
                        keyboardType: TextInputType.emailAddress,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter your email';
                          }
                          if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
                            return 'Please enter a valid email';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),
                      // Password field
                      TextFormField(
                        controller: _passwordController,
                        decoration: InputDecoration(
                          labelText: 'Password',
                          prefixIcon: const Icon(Icons.lock),
                          suffixIcon: IconButton(
                            icon: Icon(_obscurePassword ? Icons.visibility : Icons.visibility_off),
                            onPressed: () {
                              setState(() {
                                _obscurePassword = !_obscurePassword;
                              });
                            },
                          ),
                        ),
                        obscureText: _obscurePassword,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter your password';
                          }
                          if (value.length < 6) {
                            return 'Password must be at least 6 characters';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),
                      // Confirm password field
                      TextFormField(
                        controller: _confirmPasswordController,
                        decoration: InputDecoration(
                          labelText: 'Confirm Password',
                          prefixIcon: const Icon(Icons.lock),
                          suffixIcon: IconButton(
                            icon: Icon(_obscureConfirmPassword ? Icons.visibility : Icons.visibility_off),
                            onPressed: () {
                              setState(() {
                                _obscureConfirmPassword = !_obscureConfirmPassword;
                              });
                            },
                          ),
                        ),
                        obscureText: _obscureConfirmPassword,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please confirm your password';
                          }
                          if (value != _passwordController.text) {
                            return 'Passwords do not match';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 24),
                      // Sign up button
                      ElevatedButton(
                        onPressed: _isLoading ? null : _signUp,
                        style: ElevatedButton.styleFrom(
                          minimumSize: const Size.fromHeight(50),
                        ),
                        child: _isLoading
                            ? const CircularProgressIndicator()
                            : const Text('Sign Up'),
                      ),
                    ],
                  ),
                ),

            ],
          ),
        ),
      ),
    );
  }
}
