// lib/features/auth/screens/forgot_password_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:logging/logging.dart';


// Adjust import paths
import '../../../core/interfaces/auth_service_interface.dart'; // Needed for Cubit provider
import '../../../core/utils/validators.dart';
import '../cubits/forgot_password_cubit.dart'; // Import the Cubit

// Wrapper to provide the Cubit
class ForgotPasswordScreenWrapper extends StatelessWidget {
  const ForgotPasswordScreenWrapper({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => ForgotPasswordCubit(
        authService: context.read<AuthServiceInterface>(),
      ),
      child: const ForgotPasswordScreen(),
    );
  }
}

class ForgotPasswordScreen extends StatefulWidget {
  const ForgotPasswordScreen({super.key});
  @override
  State<ForgotPasswordScreen> createState() => _ForgotPasswordScreenState();
}

class _ForgotPasswordScreenState extends State<ForgotPasswordScreen> {
  final _log = Logger('ForgotPasswordScreen');
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();

  // NOTE: Local state flags _isLoading, _error, _emailSent are removed.
  // UI is now built directly from Bloc state.

  @override
  void dispose() {
    _emailController.dispose();
    super.dispose();
  }

  void _onSubmit() {
    _log.info('[ForgotPasswordScreen] _onSubmit called');
    print('>>> _onSubmit called');

    if (!_formKey.currentState!.validate()) {
      _log.info('[ForgotPasswordScreen] Form validation failed');
      print('>>> Form validation failed');
      return;
    }

    _log.info('[ForgotPasswordScreen] Form validation passed');
    print('>>> Form validation passed');

    FocusScope.of(context).unfocus();

    final email = _emailController.text.trim();
    _log.info('[ForgotPasswordScreen] Preparing to call ForgotPasswordCubit.requestPasswordReset with email: $email');
    print('>>> [ForgotPasswordScreen _onSubmit] Preparing to call ForgotPasswordCubit.requestPasswordReset for email: $email');

    // Call the method on the Cubit
    context.read<ForgotPasswordCubit>().requestPasswordReset(email);

    _log.info('[ForgotPasswordScreen] ForgotPasswordCubit.requestPasswordReset called for $email.');
    print('>>> [ForgotPasswordScreen _onSubmit] ForgotPasswordCubit.requestPasswordReset called for $email.');
  }

  @override
  Widget build(BuildContext context) {
    // Listen to the ForgotPasswordCubit for navigation/side-effects
    return BlocListener<ForgotPasswordCubit, ForgotPasswordState>(
       listener: (context, state) {
         _log.fine('[ForgotPasswordScreen Listener] State changed: ${state.runtimeType}');
         print('>>> [ForgotPasswordScreen Listener] listener called with state: ${state.runtimeType}');

         if (state is ForgotPasswordSuccess) {
           final targetEmail = state.email;
           _log.info('[ForgotPasswordScreen Listener] Detected ForgotPasswordSuccess state for $targetEmail, preparing to navigate.');
           print('>>> [ForgotPasswordScreen Listener] DETECTED ForgotPasswordSuccess for email: $targetEmail');

           WidgetsBinding.instance.addPostFrameCallback((_) {
             if (mounted) {
               print('>>> [ForgotPasswordScreen Listener] Attempting navigation via goNamed to checkEmail for $targetEmail');
               try {
                 context.goNamed('checkEmail', queryParameters: {'email': targetEmail, 'reset': 'true'});
                 print('>>> [ForgotPasswordScreen Listener] goNamed call completed for $targetEmail.');
               } catch (e, s) {
                 print('>>> [ForgotPasswordScreen Listener] ERROR during goNamed navigation: $e');
                 _log.severe('[ForgotPasswordScreen Listener] ERROR during goNamed navigation', e, s);
               }
             } else {
               print('>>> [ForgotPasswordScreen Listener] Widget not mounted, skipping navigation for $targetEmail.');
               _log.warning('[ForgotPasswordScreen Listener] Widget not mounted, skipping navigation for $targetEmail.');
             }
           });
         } else if (state is ForgotPasswordFailure) {
            // Optionally show a snackbar for failure, though the builder handles inline error display
            // ScaffoldMessenger.of(context)..hideCurrentSnackBar()..showSnackBar(SnackBar(content: Text(state.message)));
         }
       },
      child: Scaffold(
        appBar: AppBar(title: const Text('Reset Password')),
        // Build the UI based on ForgotPasswordCubit state
        body: BlocBuilder<ForgotPasswordCubit, ForgotPasswordState>(
          builder: (ctx, state) {
            _log.fine('[ForgotPasswordScreen] BUILDER running with state ${state.runtimeType}');
            // print('>>> ForgotPasswordScreen BlocBuilder running with state: ${state.runtimeType} <<<'); // Removed debug print

            // Extract loading/error info from the Cubit's state
            final bool showLoading = state is ForgotPasswordSubmitting;
            final String? errorMessage = (state is ForgotPasswordFailure) ? state.message : null;

            // Don't show the form if the email was successfully sent (handled by listener navigation)
            // The listener will navigate away upon ForgotPasswordSuccess state.
            // We can keep showing the form but disable inputs during submission.
            // Or return a placeholder like below if we want the screen cleared immediately on success state before navigation.
            // if (state is ForgotPasswordSuccess) {
            //   return const Center(child: CircularProgressIndicator.adaptive()); 
            // }

            return Center(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(24),
                child: Form(
                  key: _formKey,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      const Icon(Icons.lock_reset, size: 80, color: Colors.blue),
                      const SizedBox(height: 24),
                      const Text('Forgot Password', textAlign: TextAlign.center, style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold)),
                      const SizedBox(height: 16),
                       const Text(
                        'Enter your email address and we\'ll send you a link to reset your password.',
                        textAlign: TextAlign.center,
                        style: TextStyle(color: Colors.grey), // Example style
                      ),
                      const SizedBox(height: 32),
                      TextFormField(
                        controller: _emailController,
                        decoration: const InputDecoration(
                          labelText: 'Your email',
                          prefixIcon: Icon(Icons.email_outlined),
                        ),
                        validator: Validators.validateEmail, // Use your validator
                        autofillHints: const [AutofillHints.email],
                        keyboardType: TextInputType.emailAddress,
                        textInputAction: TextInputAction.done,
                         // Disable field while loading
                        readOnly: showLoading,
                        onFieldSubmitted: (_) {
                          // print('>>> TEXTFORMFIELD ONSUBMITTED <<<'); // Removed debug print
                          if (!showLoading) _onSubmit();
                        },
                      ),
                      const SizedBox(height: 16),
                      // Display error message if present
                      if (errorMessage != null) ...[
                         Container(
                           padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                           decoration: BoxDecoration(
                             color: Colors.red.shade50,
                             borderRadius: BorderRadius.circular(8),
                             border: Border.all(color: Colors.red.shade300)
                           ),
                           child: Row(children: [
                             Icon(Icons.error_outline, color: Theme.of(context).colorScheme.error, size: 18),
                             const SizedBox(width: 8),
                             Expanded(child: Text(errorMessage, style: TextStyle(color: Theme.of(context).colorScheme.error, fontSize: 13))),
                           ]),
                         ),
                         const SizedBox(height: 16),
                       ],
                      // Submit Button
                      ElevatedButton(
                        onPressed: showLoading ? null : _onSubmit, // Restore original logic
                         style: ElevatedButton.styleFrom(
                             padding: const EdgeInsets.symmetric(vertical: 14),
                             minimumSize: const Size(double.infinity, 48), // Ensure good height
                         ),
                        child: showLoading
                            ? const CircularProgressIndicator.adaptive()
                            : const Text('Send Reset Link'),
                      ),
                      const SizedBox(height: 16),
                      TextButton( // Back button
                        onPressed: showLoading ? null : () => context.go('/login'), // Restore original logic
                        child: const Text('Back to Login'),
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
