import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class InvalidLinkScreen extends StatelessWidget {
  final String? errorMessage;
  
  const InvalidLinkScreen({
    super.key, 
    this.errorMessage,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.link_off,
                color: Colors.red,
                size: 80,
              ),
              const SizedBox(height: 24),
              const Text(
                'Invalid or Expired Link',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              Text(
                errorMessage ?? 'The link you clicked is invalid or has expired.',
                style: const TextStyle(fontSize: 16),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              const Text(
                'Please try again or request a new link.',
                style: TextStyle(fontSize: 16),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),
              ElevatedButton(
                onPressed: () => context.go('/login'),
                child: const Text('Back to Login'),
              ),
              const SizedBox(height: 16),
              TextButton(
                onPressed: () => context.go('/forgot-password'),
                child: const Text('Request New Password Reset'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
