import 'package:flutter/material.dart';
import 'package:logging/logging.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../blocs/auth_bloc.dart';

class ClubRegistrationScreen extends StatefulWidget {
  const ClubRegistrationScreen({super.key});

  @override
  State<ClubRegistrationScreen> createState() => _ClubRegistrationScreenState();
}

class _ClubRegistrationScreenState extends State<ClubRegistrationScreen> {
  final _formKey = GlobalKey<FormState>();
  final _clubNameController = TextEditingController();
  final _adminFirstNameController = TextEditingController();
  final _adminLastNameController = TextEditingController();
  final _adminEmailController = TextEditingController();
  final _adminPasswordController = TextEditingController();
  final _adminConfirmPasswordController = TextEditingController();

  final _log = Logger('ClubRegistrationScreen');
  bool _isLoading = false;
  String? _error;

  @override
  void dispose() {
    _clubNameController.dispose();
    _adminFirstNameController.dispose();
    _adminLastNameController.dispose();
    _adminEmailController.dispose();
    _adminPasswordController.dispose();
    _adminConfirmPasswordController.dispose();
    super.dispose();
  }

  void _registerClubAndAdmin() {
    if (!_formKey.currentState!.validate()) return;
    FocusScope.of(context).unfocus();

    // Remove local setState - let BlocListener handle it

    final clubName = _clubNameController.text.trim();
    final adminFirstName = _adminFirstNameController.text.trim();
    final adminLastName = _adminLastNameController.text.trim();
    final adminEmail = _adminEmailController.text.trim();
    final adminPassword = _adminPasswordController.text;

    _log.info('Dispatching AuthClubAdminRegisterRequested: Club=$clubName, Admin=$adminFirstName $adminLastName, Email=$adminEmail');

    // Dispatch event to BLoC
    context.read<AuthBloc>().add(AuthClubAdminRegisterRequested(
      clubName: clubName,
      adminFirstName: adminFirstName,
      adminLastName: adminLastName,
      adminEmail: adminEmail,
      adminPassword: adminPassword,
    ));
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return BlocListener<AuthBloc, AuthState>(
      listener: (context, state) {
        if (state is AuthLoading) {
          setState(() => _isLoading = true);
        } else if (state is AuthFailure) {
          setState(() {
            _isLoading = false;
            _error = state.message;
          });
        } else if (state is AuthVerificationEmailSent) {
          setState(() => _isLoading = false);
          // Show success message
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Club registered successfully! Please check your email to confirm your account.'),
              backgroundColor: Colors.green,
            ),
          );
          context.go('/check-email?email=${_adminEmailController.text}');
        }
      },
      child: Scaffold(
      appBar: AppBar(
        title: const Text('Register a Club'),
      ),
      body: Center(
        child: SingleChildScrollView(
          padding: const EdgeInsets.symmetric(vertical: 32.0, horizontal: 16.0),
          child: ConstrainedBox(
            constraints: const BoxConstraints(maxWidth: 450),
            child: Card(
              elevation: 8.0,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16.0)),
              child: Padding(
                padding: const EdgeInsets.all(24.0),
                child: Form(
                  key: _formKey,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                       // Icon and Title
                       const Icon(Icons.business_outlined, size: 50, color: Colors.blueAccent),
                       const SizedBox(height: 8),
                       Text('Club Information', style: theme.textTheme.headlineSmall),
                       const SizedBox(height: 16),
                       TextFormField(
                          controller: _clubNameController,
                          decoration: const InputDecoration(
                            labelText: 'Club Name',
                            hintText: 'Enter the official name of your club',
                            prefixIcon: Icon(Icons.business),
                          ),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter the club name';
                            }
                            return null;
                          },
                       ),

                       const SizedBox(height: 24),
                       Text('Admin Account Information', style: theme.textTheme.titleMedium),
                       const SizedBox(height: 16),

                       // Admin First Name
                       TextFormField(
                          controller: _adminFirstNameController,
                          decoration: const InputDecoration(
                            labelText: 'Admin First Name',
                            hintText: 'Enter your first name',
                            prefixIcon: Icon(Icons.person),
                          ),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter your first name';
                            }
                            return null;
                          },
                       ),

                       const SizedBox(height: 16),

                       // Admin Last Name
                       TextFormField(
                          controller: _adminLastNameController,
                          decoration: const InputDecoration(
                            labelText: 'Admin Last Name',
                            hintText: 'Enter your last name',
                            prefixIcon: Icon(Icons.person),
                          ),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter your last name';
                            }
                            return null;
                          },
                       ),

                       const SizedBox(height: 16),

                       // Admin Email
                       TextFormField(
                          controller: _adminEmailController,
                          decoration: const InputDecoration(
                            labelText: 'Admin Email',
                            hintText: 'Enter your email address',
                            prefixIcon: Icon(Icons.email),
                          ),
                          keyboardType: TextInputType.emailAddress,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter your email';
                            }
                            if (!value.contains('@')) {
                              return 'Please enter a valid email address';
                            }
                            return null;
                          },
                       ),

                       const SizedBox(height: 16),

                       // Admin Password
                       TextFormField(
                          controller: _adminPasswordController,
                          decoration: const InputDecoration(
                            labelText: 'Admin Password',
                            hintText: 'Create a secure password',
                            prefixIcon: Icon(Icons.lock),
                          ),
                          obscureText: true,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter a password';
                            }
                            if (value.length < 8) {
                              return 'Password must be at least 8 characters';
                            }
                            return null;
                          },
                       ),

                       const SizedBox(height: 16),

                       // Confirm Password
                       TextFormField(
                          controller: _adminConfirmPasswordController,
                          decoration: const InputDecoration(
                            labelText: 'Confirm Password',
                            hintText: 'Confirm your password',
                            prefixIcon: Icon(Icons.lock_outline),
                          ),
                          obscureText: true,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please confirm your password';
                            }
                            if (value != _adminPasswordController.text) {
                              return 'Passwords do not match';
                            }
                            return null;
                          },
                       ),

                       if (_error != null)
                         Padding(
                           padding: const EdgeInsets.only(top: 16.0),
                           child: Text(
                             _error!,
                             style: TextStyle(color: theme.colorScheme.error),
                             textAlign: TextAlign.center,
                           ),
                         ),

                       const SizedBox(height: 32),

                       ElevatedButton(
                          onPressed: _isLoading ? null : _registerClubAndAdmin,
                          style: ElevatedButton.styleFrom(backgroundColor: Colors.blueAccent),
                          child: _isLoading
                             ? const SizedBox(height: 20, width: 20, child: CircularProgressIndicator(strokeWidth: 2, color: Colors.white))
                             : const Text('Register Club & Admin Account', style: TextStyle(color: Colors.white)),
                       ),
                       const SizedBox(height: 16),
                       Center(
                         child: TextButton(
                           onPressed: () => context.go('/login'),
                           child: const Text('Already have an account? Log In / Register Individually'),
                         ),
                       ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    ));
  }
}
