import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class CheckEmailScreen extends StatelessWidget {
  final String? email;
  final bool isPasswordReset;
  
  const CheckEmailScreen({
    super.key, 
    this.email,
    this.isPasswordReset = false,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(isPasswordReset ? 'Password Reset' : 'Email Verification'),
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                isPasswordReset ? Icons.mark_email_read_outlined : Icons.email_outlined, 
                size: 64, 
                color: Colors.blue
              ),
              const SizedBox(height: 24),
              Text(
                isPasswordReset ? 'Password Reset Email Sent' : 'Verification Email Sent',
                style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              Text(
                isPasswordReset
                    ? 'We\'ve sent a password reset link to ${email ?? 'your email address'}. '
                      'Please check your inbox and follow the link to reset your password.'
                    : 'We\'ve sent a verification link to ${email ?? 'your email address'}. '
                      'Please check your inbox and click the link to activate your account.',
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),
              ElevatedButton(
                onPressed: () => context.go('/login'),
                child: const Text('Back to Login'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
