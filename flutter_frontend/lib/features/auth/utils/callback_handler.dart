import 'package:logging/logging.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// Represents the different actions that can be taken after processing a callback
enum CallbackAction {
  /// Navigate to the reset password screen
  navigateToResetPassword,

  /// Navigate to the confirmation success screen
  navigateToConfirmationSuccess,

  /// Navigate to the home screen
  navigateToHome,

  /// Navigate to the login screen
  navigateToLogin,

  /// Show an error message
  showError
}

/// Result of processing a callback
class CallbackResult {
  /// The action to take
  final CallbackAction action;

  /// The callback type (signup, recovery, etc.)
  final String? callbackType;

  /// The error message (if any)
  final String? errorMessage;

  /// Creates a new [CallbackResult]
  const CallbackResult({
    required this.action,
    this.callbackType,
    this.errorMessage,
  });

  /// Creates a result for a reset password action
  factory CallbackResult.resetPassword() => const CallbackResult(
    action: CallbackAction.navigateToResetPassword,
    callbackType: 'recovery',
  );

  /// Creates a result for a confirmation success action
  factory CallbackResult.confirmationSuccess() => const CallbackResult(
    action: CallbackAction.navigateToConfirmationSuccess,
    callbackType: 'signup',
  );

  /// Creates a result for a home navigation action
  factory CallbackResult.home() => const CallbackResult(
    action: CallbackAction.navigateToHome,
    callbackType: 'unknown',
  );

  /// Creates a result for a login navigation action
  factory CallbackResult.login() => const CallbackResult(
    action: CallbackAction.navigateToLogin,
    callbackType: 'unknown',
  );

  /// Creates a result for an error action
  factory CallbackResult.error(String message) => CallbackResult(
    action: CallbackAction.showError,
    errorMessage: message,
  );
}

/// Handles the processing of authentication callbacks
class CallbackHandler {
  final Logger _log = Logger('CallbackHandler');
  final SupabaseClient _supabaseClient;

  /// Creates a new [CallbackHandler].
  ///
  /// An optional [supabaseClient] can be provided for testing purposes.
  /// If not provided, it defaults to `Supabase.instance.client`.
  CallbackHandler({SupabaseClient? supabaseClient})
      : _supabaseClient = supabaseClient ?? Supabase.instance.client;

  /// Processes a callback URI and returns the appropriate action
  CallbackResult processCallbackUri(Uri uri) {
    _log.info('⇒ [CallbackHandler] START processCallbackUri');
    print(">>> CallbackHandler: Processing URI: $uri");

    _log.info('[CallbackHandler] Full URI: $uri');
    _log.info('[CallbackHandler] Path: ${uri.path}');
    _log.info('[CallbackHandler] Query parameters: ${uri.queryParameters}');
    _log.info('[CallbackHandler] Fragment: ${uri.fragment}');
    print(">>> CallbackHandler: Fragment: ${uri.fragment}");

    // Parse both query parameters AND fragment
    final queryParams = uri.queryParameters;

    // Parse fragment parameters (critical for Supabase auth)
    final fragmentParams = uri.fragment.isNotEmpty
        ? Uri.splitQueryString(uri.fragment)
        : <String, String>{};
    _log.info('[CallbackHandler] Fragment parameters: $fragmentParams');
    print(">>> CallbackHandler: Fragment parameters: $fragmentParams");

    // Combine both for complete parameter access
    final allParams = {
      ...queryParams,
      ...fragmentParams,
    };
    _log.info('[CallbackHandler] Combined parameters: $allParams');
    print(">>> CallbackHandler: Combined parameters: $allParams");

    // Check for code parameter (PKCE flow)
    if (queryParams.containsKey('code')) {
      final code = queryParams["code"] ?? '';
      final codePreview = code.length > 10 ? '${code.substring(0, 10)}...' : code;
      _log.info('[CallbackHandler] Auth code present: $codePreview');
      print(">>> CallbackHandler: Auth code present: $codePreview");
    }

    // Check for access_token (in fragment)
    if (fragmentParams.containsKey('access_token')) {
      final token = fragmentParams["access_token"] ?? '';
      final tokenPreview = token.length > 10 ? '${token.substring(0, 10)}...' : token;
      _log.info('[CallbackHandler] Access token present: $tokenPreview');
      print(">>> CallbackHandler: Access token present: $tokenPreview");
    }

    // Check for error parameter
    if (allParams.containsKey('error_description')) {
      final errorMessage = allParams['error_description']!;
      _log.warning('[CallbackHandler] ❌ Callback error: $errorMessage');
      print(">>> CallbackHandler ERROR: $errorMessage");
      return CallbackResult.error(errorMessage);
    }

    // Check for type parameter (could be in fragment or query)
    final type = allParams['type'];
    _log.info('[CallbackHandler] Callback type: $type');
    print(">>> CallbackHandler: Callback type: $type");

    CallbackResult result;

    if (type == 'recovery') {
      _log.info('[CallbackHandler] 🔑 DETECTED PASSWORD RESET CALLBACK');
      print(">>> CallbackHandler: DETECTED PASSWORD RESET");
      _log.info('[CallbackHandler] Returning CallbackResult.resetPassword()');
      result = CallbackResult.resetPassword();
    } else if (type == 'signup') {
      // This handles explicit signup type, e.g. from magic links if they include type=signup
      _log.info('[CallbackHandler] ✉️ Processing explicit signup confirmation callback (type=signup)');
      print(">>> CallbackHandler: Processing explicit signup confirmation (type=signup)");
      result = CallbackResult.confirmationSuccess();
    } else if (queryParams.containsKey('code')) {
      // PKCE flow for email confirmation typically involves a 'code' and no 'type',
      // or 'type' might be something else or absent.
      // The presence of 'code' after a signup attempt usually means email confirmation.
      _log.info('[CallbackHandler] ✉️ Detected PKCE-style callback with "code" parameter. Assuming email confirmation.');
      print(">>> CallbackHandler: Detected PKCE-style callback with 'code'. Assuming email confirmation.");
      result = CallbackResult.confirmationSuccess();
    }
    else {
      _log.info('[CallbackHandler] ⚠️ No specific recovery, signup, or PKCE code detected. Processing as generic callback.');
      print(">>> CallbackHandler: No specific type or PKCE code detected");
      // Default to home if no other specific action is determined.
      // This could be after a successful OAuth login, for example.
      result = CallbackResult.home();
    }

    _log.info('⇐ [CallbackHandler] END processCallbackUri with action: ${result.action}');
    print(">>> CallbackHandler: Returning result with action: ${result.action}");
    return result;
  }

  /// Processes a callback for mobile devices where the URI is not available.
  /// Relies on Supabase session state, assuming AuthBloc will handle specific
  /// auth events like password recovery.
  Future<CallbackResult> processMobileCallback() async {
    _log.info('⇒ [CallbackHandler] START processMobileCallback (Simplified)');
    print(">>> CallbackHandler: Processing mobile callback (Simplified)");

    // Give Supabase a moment if getSessionFromUrl was potentially called by a deep link handler
    // or if the auth state is still propagating.
    await Future.delayed(const Duration(milliseconds: 500));

    final session = _supabaseClient.auth.currentSession;
    if (session != null) {
      final user = session.user;
      _log.info('[CallbackHandler Mobile] Session found for user: ${user.email}');

      // If a session exists, it could be email confirmation or a general login.
      // It's NOT reliably a password recovery without the 'type=recovery' from URI.
      // AuthBloc is responsible for reacting to the specific Supabase AuthChangeEvent
      // (e.g., passwordRecovery) and setting the appropriate BLoC state.
      // This handler should not try to guess 'recovery'.
      if (user.emailConfirmedAt != null) {
        _log.info('[CallbackHandler Mobile] Email is confirmed. Navigating to confirmation success.');
        print(">>> CallbackHandler Mobile: Email confirmed, action: confirmationSuccess");
        return CallbackResult.confirmationSuccess();
      } else {
        // Email not confirmed, but session exists (e.g. magic link, OAuth, or initial phase of email+pass signup)
        _log.info('[CallbackHandler Mobile] Session exists, email not confirmed (or status unknown). Navigating home.');
        print(">>> CallbackHandler Mobile: Session exists, email not confirmed, action: home");
        return CallbackResult.home();
      }
    } else {
      _log.warning('[CallbackHandler Mobile] No session found after delay. Navigating to login.');
      print(">>> CallbackHandler Mobile: No session, action: login");
      return CallbackResult.login();
    }
  }
}
